<?php
// Inclure le fichier de configuration
require_once 'config.php';

// Gestion de la session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Vérification de l'authentification
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php?error=session_invalide");
    exit();
}

// Pour le débogage
error_log("Session user_id: " . $_SESSION['user_id']);
error_log("Session role: " . ($_SESSION['role'] ?? 'non défini'));
error_log("Session user_type: " . ($_SESSION['user_type'] ?? 'non défini'));

// Forcer le type d'utilisateur à chef_departement pour cette page
$_SESSION['user_type'] = 'chef_departement';
$_SESSION['role'] = 'chef_departement';

try {
    // Connexion à la base de données
    $pdo = new PDO(
        "mysql:host=".DB_HOST.";dbname=".DB_NAME.";charset=utf8",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
} catch (PDOException $e) {
    die("Erreur de connexion : " . $e->getMessage());
}

// Récupérer toutes les unités d'enseignement
function getUnitesEnseignement() {
    global $pdo;

    try {
        // Vérifier si la table departements existe
        $stmt = $pdo->query("SHOW TABLES LIKE 'departements'");
        $depTableExists = $stmt->rowCount() > 0;

        // Vérifier si la colonne departement_id existe dans unites_enseignements
        $stmt = $pdo->query("SHOW COLUMNS FROM unites_enseignements LIKE 'departement_id'");
        $depIdExists = $stmt->rowCount() > 0;

        // Vérifier si la colonne code_ue existe dans unites_enseignements
        $stmt = $pdo->query("SHOW COLUMNS FROM unites_enseignements LIKE 'code_ue'");
        $codeUeExists = $stmt->rowCount() > 0;

        if ($depTableExists && $depIdExists) {
            // Si la table departements existe et la colonne departement_id existe
            $query = "
                SELECT ue.*, d.nom as nom_departement
                FROM unites_enseignements ue
                LEFT JOIN departements d ON ue.departement_id = d.departement_id
                ORDER BY " . ($codeUeExists ? "ue.code_ue" : "ue.id_ue");
        } else {
            // Si la table departements n'existe pas ou la colonne departement_id n'existe pas
            $query = "
                SELECT
                    ue.*,
                    'Non spécifié' as nom_departement
                FROM
                    unites_enseignements ue
                ORDER BY " . ($codeUeExists ? "ue.code_ue" : "ue.id_ue");
        }

        $stmt = $pdo->query($query);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        // En cas d'erreur, retourner un tableau vide
        error_log("Erreur dans getUnitesEnseignement: " . $e->getMessage());
        return [];
    }
}

$unites_enseignement = getUnitesEnseignement();
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion UE - Chef de Département</title>

    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">

    <style>
        :root {
            --bleu-fonce: #0A2463;
            --bleu-moyen: #3A5CA9;
            --bleu-clair: #D6E4F0;
            --rouge: #FB3640;
        }

        body {
            background: #f8f9fa;
            min-height: 100vh;
        }

        /* Sidebar */
        .sidebar {
            background: linear-gradient(180deg, var(--bleu-fonce) 0%, var(--bleu-moyen) 100%);
            width: 280px;
            min-height: 100vh;
            position: fixed;
            box-shadow: 3px 0 15px rgba(0,0,0,0.2);
        }

        .logo-sidebar {
            width: 200px;
            padding: 2rem 1rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .nav-link {
            color: rgba(255,255,255,0.8) !important;
            padding: 0.8rem 1.5rem !important;
            margin: 0.5rem 1rem;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .nav-link:hover {
            background: rgba(255,255,255,0.1) !important;
            transform: translateX(8px);
        }

        .nav-link.active {
            background: var(--rouge) !important;
            color: white !important;
            font-weight: 600;
        }

        /* Contenu principal */
        .main-content {
            margin-left: 280px;
            padding: 2rem;
            background: white;
            min-height: 100vh;
        }

        .table-ue thead {
            background: var(--bleu-fonce);
            color: white;
        }

        .badge-semestre {
            background: var(--bleu-moyen);
            padding: 0.4em 0.8em;
        }

        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                position: relative;
            }
            .main-content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>

<!-- Sidebar -->
<nav class="sidebar">
    <div class="text-center py-4">
        <img src="logo.png" alt="Logo" class="logo-sidebar">
        <h4 class="text-white mt-2">Espace Chef de Département</h4>
    </div>

    <div class="nav flex-column px-3">
        <a class="nav-link active" href="Affichage_liste_UE.php">
            <i class="fas fa-book me-2"></i> Liste des UE
        </a>
        <a class="nav-link" href="souhaits_enseignants.php">
            <i class="fas fa-handshake me-2"></i> Souhaits enseignants
        </a>
        <a class="nav-link" href="Calcul_automatique_charge_horaire.php">
            <i class="fas fa-calculator me-2"></i> Calcul des charges
        </a>
        <a class="nav-link" href="Notification_non-respect_charge_minimale.php">
            <i class="fas fa-bell me-2"></i> Notifications
        </a>
        <a class="nav-link" href="Consulter_modules_assures.php">
            <i class="fas fa-list-check me-2"></i> Modules assurés
        </a>
        <a class="nav-link" href="Uploader_notes.php">
            <i class="fas fa-file-import me-2"></i> Importer notes
        </a>
        <a class="nav-link" href="historique.php">
            <i class="fas fa-clock-rotate-left me-2"></i> Historique
        </a>
        <a href="logout.php" class="nav-link mt-4" style="background: var(--rouge) !important;">
            <i class="fas fa-power-off me-2"></i> Déconnexion
        </a>
    </div>
</nav>

<!-- Contenu -->
<div class="main-content">
    <h1 class="mb-4"><i class="fas fa-book me-3"></i>Liste des Unités d'Enseignement</h1>

    <div class="card shadow-sm">
        <div class="card-body">
            <table class="table table-ue table-striped">
                <thead>
                    <tr>
                        <th>Code UE</th>
                        <th>Intitulé</th>
                        <th>Crédits</th>
                        <th>Volume Horaire</th>
                        <th>Semestre</th>
                        <th>Responsable</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($unites_enseignement)): ?>
                        <?php foreach ($unites_enseignement as $ue): ?>
                        <tr>
                            <td><?= htmlspecialchars($ue['code_ue'] ?? $ue['id_ue'] ?? 'N/A') ?></td>
                            <td><?= htmlspecialchars($ue['intitule'] ?? $ue['nom'] ?? 'N/A') ?></td>
                            <td><?= $ue['credits'] ?? $ue['credit'] ?? 'N/A' ?></td>
                            <td><?= ($ue['volume_horaire'] ?? 'N/A') ?>h</td>
                            <td>
                                <span class="badge badge-semestre">
                                    S<?= $ue['semestre'] ?? 'N/A' ?>
                                </span>
                            </td>
                            <td><?= htmlspecialchars($ue['responsable'] ?? 'Non attribué') ?></td>
                        </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="6" class="text-center text-muted">
                                <i class="fas fa-info-circle me-2"></i>
                                Aucune unité d'enseignement trouvée
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Scripts -->
<script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.print.min.js"></script>

<script>
$(document).ready(function() {
    $('.table-ue').DataTable({
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/fr-FR.json'
        },
        dom: '<"top"Bf>rt<"bottom"lip>',
        buttons: [
            {
                extend: 'print',
                text: '<i class="fas fa-print"></i> Imprimer',
                className: 'btn btn-sm btn-secondary'
            }
        ]
    });

    // Gestion active menu
    const currentPage = location.pathname.split('/').pop();
    document.querySelectorAll('.nav-link').forEach(link => {
        if(link.getAttribute('href') === currentPage) {
            link.classList.add('active');
        }
    });
});
</script>

</body>
</html>
