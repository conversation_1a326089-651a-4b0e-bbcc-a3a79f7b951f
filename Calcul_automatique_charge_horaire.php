<?php
session_start();

// Connexion à la base de données
$host = "localhost";
$dbname = "gestion_coordinteur";
$user = "root";
$pass = "";

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $user, $pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    $message = "";
    $charges_horaires = [];
    $total_charge = 0;
    $charge_minimale = 0;

    // Déterminer le mode d'affichage
    $mode = isset($_GET['mode']) ? $_GET['mode'] : 'form';

    // ID de l'enseignant connecté
    $id_enseignant_connecte = isset($_GET['id_enseignant']) ? $_GET['id_enseignant'] : 19;

    // Traitement des formulaires
    if ($_SERVER["REQUEST_METHOD"] == "POST") {
        if (isset($_POST['calculer'])) {
            $id_enseignant = $_POST["id_enseignant"];
            $annee_scolaire = $_POST["annee_scolaire"];
            header("Location: " . $_SERVER['PHP_SELF'] . "?mode=results&id_enseignant=" . urlencode($id_enseignant) . "&annee_scolaire=" . urlencode($annee_scolaire));
            exit();
        }
        elseif (isset($_POST['sauvegarder'])) {
            try {
                $id_enseignant = $_POST["id_enseignant"];
                $annee_scolaire = $_POST["annee_scolaire"];
                $total_charge = $_POST["total_charge"];

                // Debug: Afficher les valeurs reçues
                error_log("Tentative de sauvegarde - ID: $id_enseignant, Année: $annee_scolaire, Charge: $total_charge");

                // Vérifier l'existence de l'entrée
                $check_stmt = $pdo->prepare("
                    SELECT COUNT(*) 
                    FROM charge_horaire_minimale 
                    WHERE id_enseignant = ? 
                    AND annee_scolaire = ?
                ");
                $check_stmt->execute([$id_enseignant, $annee_scolaire]);
                $exists = $check_stmt->fetchColumn();

                if ($exists > 0) {
                    // UPDATE
                    $stmt = $pdo->prepare("
                        UPDATE charge_horaire_minimale 
                        SET charge_min = ?, date_modification = NOW()
                        WHERE id_enseignant = ? 
                        AND annee_scolaire = ?
                    ");
                    $result = $stmt->execute([$total_charge, $id_enseignant, $annee_scolaire]);
                    
                    if ($result && $stmt->rowCount() > 0) {
                        $message = "✅ Charge horaire mise à jour avec succès ! ($total_charge h)";
                        error_log("UPDATE réussi - Lignes affectées: " . $stmt->rowCount());
                    } else {
                        $message = "⚠ Aucune ligne mise à jour. Vérifiez les données.";
                        error_log("UPDATE échoué - Aucune ligne affectée");
                    }
                } else {
                    // INSERT
                    $stmt = $pdo->prepare("
                        INSERT INTO charge_horaire_minimale 
                        (id_enseignant, annee_scolaire, charge_min, date_creation)
                        VALUES (?, ?, ?, NOW())
                    ");
                    $result = $stmt->execute([$id_enseignant, $annee_scolaire, $total_charge]);
                    
                    if ($result) {
                        $message = "✅ Nouvelle charge horaire enregistrée avec succès ! ($total_charge h)";
                        error_log("INSERT réussi - ID: " . $pdo->lastInsertId());
                    } else {
                        $message = "❌ Erreur lors de l'insertion";
                        error_log("INSERT échoué");
                    }
                }

                // PAS DE REDIRECTION - on reste sur la page pour voir le message
                $mode = 'results';
                $_GET['id_enseignant'] = $id_enseignant;
                $_GET['annee_scolaire'] = $annee_scolaire;

            } catch (PDOException $e) {
                $message = "❌ Erreur de base de données : " . $e->getMessage();
                error_log("Erreur PDO: " . $e->getMessage());
                $mode = 'results';
                $_GET['id_enseignant'] = $_POST["id_enseignant"];
                $_GET['annee_scolaire'] = $_POST["annee_scolaire"];
            }
        }
    }

    // Traitement des résultats
    if ($mode === 'results' && isset($_GET['id_enseignant']) && isset($_GET['annee_scolaire'])) {
        $id_enseignant = $_GET['id_enseignant'];
        $annee_scolaire = $_GET['annee_scolaire'];

        // Récupération des affectations
        $stmt = $pdo->prepare("
            SELECT
                ue.id_matiere,
                ue.filiere,
                ue.niveau,
                ue.type_enseignement,
                ue.volume_horaire,
                ue.annee_scolaire,
                a.date_affectation
            FROM affectations a
            INNER JOIN unites_enseignements ue ON a.ue_id = ue.id_ue
            WHERE a.professeur_id = :id_enseignant
            AND (ue.annee_scolaire = :annee_scolaire OR ue.annee_scolaire = :annee_scolaire_alt)
            ORDER BY ue.filiere, ue.niveau, ue.type_enseignement
        ");

        $stmt->execute([
            ':id_enseignant' => $id_enseignant,
            ':annee_scolaire' => $annee_scolaire,
            ':annee_scolaire_alt' => str_replace('/', '-', $annee_scolaire)
        ]);

        $charges_horaires = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Calcul du total
        $total_charge = 0; // Reset du total
        foreach ($charges_horaires as $charge) {
            $total_charge += $charge['volume_horaire'];
        }

        // Récupération charge minimale
        try {
            $stmt_min = $pdo->prepare("
                SELECT charge_min 
                FROM charge_horaire_minimale 
                WHERE id_enseignant = :id_enseignant 
                AND annee_scolaire = :annee_scolaire
            ");
            $stmt_min->execute([
                ':id_enseignant' => $id_enseignant,
                ':annee_scolaire' => $annee_scolaire
            ]);
            $result_min = $stmt_min->fetch(PDO::FETCH_ASSOC);
            $charge_minimale = $result_min ? $result_min['charge_min'] : 192;
        } catch (PDOException $e) {
            $charge_minimale = 192;
            error_log("Erreur lors de la récupération de la charge minimale: " . $e->getMessage());
        }

        if (!empty($charges_horaires) && empty($message)) {
            $message = "✅ Charge horaire calculée avec succès.";
        }
    }

} catch (PDOException $e) {
    $message = "❌ Erreur de connexion : " . $e->getMessage();
    error_log("Erreur de connexion PDO: " . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title><?php echo ($mode === 'results') ? 'Résultats - Charge Horaire' : 'Charge Horaire Enseignant'; ?></title>
    <style>
        :root {
            --primary-blue: #1E90FF;
            --primary-magenta: magenta;
            --blue-transparent: rgba(30, 144, 255, 0.3);
            --success-green: #28a745;
            --warning-orange: #ffc107;
            --danger-red: #dc3545;
        }

        body {
            background: url('image copy 4.png') no-repeat center center fixed;
            background-size: cover;
            color: white;
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            display: flex;
        }

        body::before {
            content: "";
            position: fixed;
            top: 0; left: 0; right: 0; bottom: 0;
            background-color: rgba(10, 25, 47, 0.85);
            z-index: -1;
        }

        .sidebar {
            width: 250px;
            background-color: rgba(10, 25, 47, 0.95);
            padding: 2rem 1rem;
            height: 100vh;
            position: fixed;
            top: 0;
            left: 0;
            border-right: 2px solid var(--primary-magenta);
            box-shadow: 2px 0 10px rgba(0,0,0,0.2);
        }

        .sidebar img {
            max-width: 150px;
            width: 100%;
            height: auto;
            display: block;
            margin: 0 auto 20px;
        }

        .sidebar a {
            display: block;
            padding: 10px 15px;
            color: white;
            text-decoration: none;
            margin-bottom: 10px;
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 6px;
            transition: all 0.3s;
        }

        .sidebar a:hover {
            background-color: var(--primary-blue);
            color: white;
        }

        .container {
            margin-left: 250px;
            padding: 2rem;
            width: calc(100% - 250px);
        }

        h2 {
            text-align: center;
            font-size: 2rem;
            color: var(--primary-blue);
            text-shadow: 0 0 10px var(--blue-transparent);
            border-bottom: 2px solid var(--primary-magenta);
            padding-bottom: 1rem;
            margin-bottom: 2rem;
        }

        form {
            max-width: 600px;
            margin: 0 auto 2rem;
            background-color: rgba(255, 255, 255, 0.05);
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(30, 144, 255, 0.2);
        }

        form label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: white;
        }

        form input, form select {
            width: 100%;
            padding: 10px;
            margin-bottom: 20px;
            border-radius: 6px;
            border: 1px solid var(--blue-transparent);
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }

        form input:focus, form select:focus {
            outline: none;
            border-color: var(--primary-blue);
            background-color: rgba(255, 255, 255, 0.2);
        }

        button {
            background-color: var(--primary-blue);
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            transition: background-color 0.3s;
            width: 100%;
            font-size: 1rem;
        }

        button:hover {
            background-color: var(--primary-magenta);
        }

        .btn-back {
            background-color: var(--warning-orange);
            color: black;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 6px;
            display: inline-block;
            margin-bottom: 2rem;
            transition: all 0.3s;
        }

        .btn-back:hover {
            background-color: #e0a800;
            transform: translateY(-2px);
        }

        .message {
            text-align: center;
            margin: 1rem 0;
            font-weight: bold;
            font-size: 1.1rem;
            padding: 15px;
            border-radius: 8px;
            background-color: rgba(255, 255, 255, 0.1);
        }

        .message.success {
            background-color: rgba(40, 167, 69, 0.2);
            border: 1px solid var(--success-green);
        }

        .message.error {
            background-color: rgba(220, 53, 69, 0.2);
            border: 1px solid var(--danger-red);
        }

        .results-section {
            max-width: 1000px;
            margin: 2rem auto;
        }

        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .card {
            background-color: rgba(255, 255, 255, 0.1);
            padding: 1.5rem;
            border-radius: 10px;
            text-align: center;
            border: 2px solid transparent;
        }

        .card.total {
            border-color: var(--primary-blue);
        }

        .card.minimal {
            border-color: var(--warning-orange);
        }

        .card.status {
            border-color: var(--success-green);
        }

        .card.status.deficit {
            border-color: var(--danger-red);
        }

        .card h3 {
            margin: 0 0 0.5rem 0;
            font-size: 1rem;
        }

        .card .value {
            font-size: 2rem;
            font-weight: bold;
            margin: 0;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            overflow: hidden;
        }

        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        th {
            background-color: var(--primary-blue);
            font-weight: bold;
            color: white;
        }

        tr:hover {
            background-color: rgba(255, 255, 255, 0.05);
        }

        .type-cours { color: #ff6b6b; }
        .type-td { color: #4ecdc4; }
        .type-tp { color: #45b7d1; }

        .no-data {
            text-align: center;
            padding: 2rem;
            color: #ccc;
            font-style: italic;
        }

        .info-header {
            background-color: rgba(255, 255, 255, 0.1);
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            text-align: center;
        }

        .info-header h3 {
            margin: 0;
            color: var(--primary-blue);
        }

        .debug-info {
            background-color: rgba(255, 255, 0, 0.1);
            border: 1px solid #ffc107;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-size: 0.9em;
            color: #ffc107;
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <img src="image copy 5.png" alt="Logo">
        <a href="Affichage_liste_UE.php">Affichage la liste de UE</a>
        <a href="souhaits_enseignants.php">Souhaits Enseignants</a>
        <a href="Calcul_automatique_charge_horaire.php">Calcul automatique de la charge horaire</a>
        <a href="gestion_charges_minimales.php">Gestion des charges minimales</a>
        <a href="Notification_non-respect_charge_minimale.php">Notification en cas de non-respect de la charge minimale</a>
        <a href="Consulter_modules_assurés_assure.php">Consulter la liste des modules assurés et qu'il assure.</a>
        <a href="Uploader_notes_session_normale_rattrapage.php">Uploader les notes de la session normale et rattrapage.</a>
        <a href="Consulter_historique_années_passées.php">Consulter l'historique des années passées.</a>
        <a href="?logout=true" class="btn btn-danger w-100 mt-3">Déconnexion</a>
    </div>

    <div class="container">
        <?php if ($mode === 'form'): ?>
            <h2>Charge Horaire de l'Enseignant</h2>
            <form method="post">
                <label for="id_enseignant">ID Enseignant</label>
                <input type="number" name="id_enseignant" id="id_enseignant" value="<?php echo $id_enseignant_connecte; ?>" required>

                <label for="annee_scolaire">Année scolaire</label>
                <select name="annee_scolaire" id="annee_scolaire" required>
                    <option value="2024-2025" selected>2024-2025</option>
                    <option value="2025-2026">2025-2026</option>
                    <option value="2023-2024">2023-2024</option>
                </select>

                <button type="submit" name="calculer">Calculer la charge horaire</button>
            </form>

        <?php else: ?>
            <a href="<?php echo $_SERVER['PHP_SELF']; ?>" class="btn-back">← Retour au formulaire</a>
            <h2>Résultats - Charge Horaire</h2>

            <div class="info-header">
                <h3>Enseignant ID: <?php echo htmlspecialchars($_GET['id_enseignant']); ?> - Année: <?php echo htmlspecialchars($_GET['annee_scolaire']); ?></h3>
            </div>

            <?php if (!empty($message)): ?>
                <div class="message <?php echo (strpos($message, '✅') !== false) ? 'success' : 'error'; ?>">
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>

            <?php if (!empty($charges_horaires)): ?>
                <div class="results-section">
                    <div class="summary-cards">
                        <div class="card total">
                            <h3>Charge Totale</h3>
                            <p class="value"><?php echo $total_charge; ?>h</p>
                        </div>
                        <div class="card minimal">
                            <h3>Charge Minimale</h3>
                            <p class="value"><?php echo $charge_minimale; ?>h</p>
                        </div>
                        <div class="card status <?php echo ($total_charge < $charge_minimale) ? 'deficit' : ''; ?>">
                            <h3>Statut</h3>
                            <p class="value">
                                <?php
                                if ($total_charge >= $charge_minimale) {
                                    echo "✅ OK";
                                } else {
                                    echo "⚠ Déficit";
                                }
                                ?>
                            </p>
                        </div>
                        <div class="card">
                            <h3>Différence</h3>
                            <p class="value" style="color: <?php echo ($total_charge - $charge_minimale >= 0) ? 'var(--success-green)' : 'var(--danger-red)'; ?>">
                                <?php echo ($total_charge - $charge_minimale >= 0 ? '+' : '') . ($total_charge - $charge_minimale); ?>h
                            </p>
                        </div>
                    </div>

                    <table>
                        <thead>
                            <tr>
                                <th>Matière</th>
                                <th>Filière</th>
                                <th>Niveau</th>
                                <th>Type</th>
                                <th>Volume Horaire</th>
                                <th>Année Scolaire</th>
                                <th>Date Affectation</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($charges_horaires as $charge): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($charge['id_matiere']); ?></td>
                                    <td><?php echo htmlspecialchars($charge['filiere']); ?></td>
                                    <td><?php echo htmlspecialchars($charge['niveau']); ?></td>
                                    <td>
                                        <span class="type-<?php echo strtolower($charge['type_enseignement']); ?>">
                                            <?php echo htmlspecialchars($charge['type_enseignement']); ?>
                                        </span>
                                    </td>
                                    <td><strong><?php echo $charge['volume_horaire']; ?>h</strong></td>
                                    <td><?php echo htmlspecialchars($charge['annee_scolaire']); ?></td>
                                    <td><?php echo date('d/m/Y H:i', strtotime($charge['date_affectation'])); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                        <tfoot>
                            <tr style="background-color: rgba(30, 144, 255, 0.2); font-weight: bold;">
                                <td colspan="4">TOTAL</td>
                                <td><strong><?php echo $total_charge; ?>h</strong></td>
                                <td colspan="2"></td>
                            </tr>
                        </tfoot>
                    </table>

                    <!-- Formulaire de sauvegarde amélioré -->
                    <form method="post" style="margin-top: 2rem;">
                        <input type="hidden" name="id_enseignant" value="<?php echo htmlspecialchars($id_enseignant); ?>">
                        <input type="hidden" name="annee_scolaire" value="<?php echo htmlspecialchars($annee_scolaire); ?>">
                        <input type="hidden" name="total_charge" value="<?php echo $total_charge; ?>">
                        
                        <div class="debug-info">
                            <strong>Infos de débogage :</strong><br>
                            - ID Enseignant: <?php echo htmlspecialchars($id_enseignant); ?><br>
                            - Année scolaire: <?php echo htmlspecialchars($annee_scolaire); ?><br>
                            - Charge totale: <?php echo $total_charge; ?>h
                        </div>
                        
                        <button type="submit" name="sauvegarder" style="background-color: #28a745; padding: 15px 25px; font-size: 1.1em;">
                            💾 Sauvegarder la charge (<?php echo $total_charge; ?>h)
                        </button>
                    </form>
                </div>
            <?php else: ?>
                <div class="no-data">
                    <p>Aucune affectation trouvée pour cet enseignant dans l'année scolaire sélectionnée.</p>
                    <div class="debug-info">
                        <strong>Vérifications à faire :</strong><br>
                        - L'enseignant ID <?php echo htmlspecialchars($_GET['id_enseignant']); ?> existe-t-il ?<br>
                        - Y a-t-il des affectations pour l'année <?php echo htmlspecialchars($_GET['annee_scolaire']); ?> ?<br>
                        - Les tables 'affectations' et 'unites_enseignements' sont-elles correctement liées ?
                    </div>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</body>
</html>