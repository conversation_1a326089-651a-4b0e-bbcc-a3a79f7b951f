<?php
session_start();

// Configuration de la base de données
class DatabaseConfig {
    const HOST = "localhost";
    const DBNAME = "gestion_coordinteur";
    const USER = "root";
    const PASS = "";
}

// Classe pour gérer la base de données
class Database {
    private static $instance = null;
    private $pdo;

    private function __construct() {
        try {
            $this->pdo = new PDO(
                "mysql:host=" . DatabaseConfig::HOST . ";dbname=" . DatabaseConfig::DBNAME . ";charset=utf8mb4",
                DatabaseConfig::USER,
                DatabaseConfig::PASS,
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false
                ]
            );
        } catch (PDOException $e) {
            error_log("Erreur de connexion à la base de données: " . $e->getMessage());
            throw new Exception("Erreur de connexion à la base de données");
        }
    }

    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function getPDO() {
        return $this->pdo;
    }
}

// Classe pour gérer les charges horaires
class ChargeHoraireManager {
    private $pdo;

    public function __construct() {
        $this->pdo = Database::getInstance()->getPDO();
    }

    public function getChargesHoraires($id_enseignant, $annee_scolaire) {
        $stmt = $this->pdo->prepare("
            SELECT
                COALESCE(m.nom_matiere, ue.id_matiere) as nom_matiere,
                ue.filiere,
                ue.niveau,
                ue.type_enseignement,
                ue.volume_horaire,
                ue.annee_scolaire,
                a.date_affectation
            FROM affectations a
            INNER JOIN unites_enseignements ue ON a.ue_id = ue.id_ue
            LEFT JOIN matieres m ON ue.id_matiere = m.id_matiere
            WHERE a.professeur_id = :id_enseignant
            AND (ue.annee_scolaire = :annee_scolaire OR ue.annee_scolaire = :annee_scolaire_alt)
            ORDER BY ue.filiere, ue.niveau, ue.type_enseignement
        ");

        $stmt->execute([
            ':id_enseignant' => $id_enseignant,
            ':annee_scolaire' => $annee_scolaire,
            ':annee_scolaire_alt' => str_replace('/', '-', $annee_scolaire)
        ]);

        return $stmt->fetchAll();
    }

    public function calculerTotalCharge($charges_horaires) {
        return array_sum(array_column($charges_horaires, 'volume_horaire'));
    }

    public function getChargeMinimale($id_enseignant, $annee_scolaire) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT charge_min 
                FROM charge_horaire_minimale 
                WHERE id_utilisateur = :id_enseignant 
                AND annee_scolaire = :annee_scolaire
            ");
            $stmt->execute([
                ':id_enseignant' => $id_enseignant,
                ':annee_scolaire' => $annee_scolaire
            ]);
            
            $result = $stmt->fetch();
            return $result ? (int)$result['charge_min'] : 192;
        } catch (PDOException $e) {
            error_log("Erreur lors de la récupération de la charge minimale: " . $e->getMessage());
            return 192;
        }
    }

    public function sauvegarderCharge($id_enseignant, $annee_scolaire, $total_charge) {
        try {
            // Vérifier l'existence
            $check_stmt = $this->pdo->prepare("
                SELECT COUNT(*) as count
                FROM charge_horaire_minimale 
                WHERE id_utilisateur = ? AND annee_scolaire = ?
            ");
            $check_stmt->execute([$id_enseignant, $annee_scolaire]);
            $exists = $check_stmt->fetch()['count'] > 0;

            if ($exists) {
                // Mise à jour
                $stmt = $this->pdo->prepare("
                    UPDATE charge_horaire_minimale 
                    SET charge_min = ?, date_modification = NOW()
                    WHERE id_utilisateur = ? AND annee_scolaire = ?
                ");
                $result = $stmt->execute([$total_charge, $id_enseignant, $annee_scolaire]);
                
                if ($result && $stmt->rowCount() > 0) {
                    return [
                        'success' => true,
                        'message' => "✅ Charge horaire mise à jour avec succès ! ({$total_charge}h)"
                    ];
                } else {
                    return [
                        'success' => false,
                        'message' => "⚠ Aucune ligne mise à jour. Vérifiez les données."
                    ];
                }
            } else {
                // Insertion
                $stmt = $this->pdo->prepare("
                    INSERT INTO charge_horaire_minimale 
                    (id_utilisateur, annee_scolaire, charge_min, date_creation)
                    VALUES (?, ?, ?, NOW())
                ");
                $result = $stmt->execute([$id_enseignant, $annee_scolaire, $total_charge]);
                
                if ($result) {
                    return [
                        'success' => true,
                        'message' => "✅ Nouvelle charge horaire enregistrée avec succès ! ({$total_charge}h)"
                    ];
                } else {
                    return [
                        'success' => false,
                        'message' => "❌ Erreur lors de l'insertion"
                    ];
                }
            }
        } catch (PDOException $e) {
            error_log("Erreur lors de la sauvegarde: " . $e->getMessage());
            return [
                'success' => false,
                'message' => "❌ Erreur de base de données : " . $e->getMessage()
            ];
        }
    }
}

// Classe pour la validation des données
class Validator {
    public static function validateId($id) {
        return filter_var($id, FILTER_VALIDATE_INT, ['options' => ['min_range' => 1]]);
    }

    public static function validateAnnee($annee) {
        return preg_match('/^\d{4}-\d{4}$/', $annee);
    }

    public static function sanitizeInput($input) {
        return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }
}

// Initialisation des variables
$message = "";
$charges_horaires = [];
$total_charge = 0;
$charge_minimale = 192;
$mode = $_GET['mode'] ?? 'form';
$id_enseignant_connecte = $_GET['id_enseignant'] ?? 19;

try {
    $chargeManager = new ChargeHoraireManager();

    // Traitement des formulaires
    if ($_SERVER["REQUEST_METHOD"] == "POST") {
        if (isset($_POST['calculer'])) {
            $id_enseignant = Validator::validateId($_POST["id_enseignant"]);
            $annee_scolaire = $_POST["annee_scolaire"];

            if ($id_enseignant && Validator::validateAnnee($annee_scolaire)) {
                header("Location: " . $_SERVER['PHP_SELF'] . "?mode=results&id_enseignant=" . 
                       urlencode($id_enseignant) . "&annee_scolaire=" . urlencode($annee_scolaire));
                exit();
            } else {
                $message = "❌ Données d'entrée invalides";
            }
        }
        elseif (isset($_POST['sauvegarder'])) {
            $id_enseignant = Validator::validateId($_POST["id_enseignant"]);
            $annee_scolaire = $_POST["annee_scolaire"];
            $total_charge = filter_var($_POST["total_charge"], FILTER_VALIDATE_INT);

            if ($id_enseignant && Validator::validateAnnee($annee_scolaire) && $total_charge !== false) {
                $result = $chargeManager->sauvegarderCharge($id_enseignant, $annee_scolaire, $total_charge);
                $message = $result['message'];
                
                // Rester sur la page des résultats
                $mode = 'results';
                $_GET['id_enseignant'] = $id_enseignant;
                $_GET['annee_scolaire'] = $annee_scolaire;
            } else {
                $message = "❌ Données d'entrée invalides pour la sauvegarde";
            }
        }
    }

    // Traitement des résultats
    if ($mode === 'results' && isset($_GET['id_enseignant']) && isset($_GET['annee_scolaire'])) {
        $id_enseignant = Validator::validateId($_GET['id_enseignant']);
        $annee_scolaire = $_GET['annee_scolaire'];

        if ($id_enseignant && Validator::validateAnnee($annee_scolaire)) {
            $charges_horaires = $chargeManager->getChargesHoraires($id_enseignant, $annee_scolaire);
            $total_charge = $chargeManager->calculerTotalCharge($charges_horaires);
            $charge_minimale = $chargeManager->getChargeMinimale($id_enseignant, $annee_scolaire);

            if (!empty($charges_horaires) && empty($message)) {
                $message = "✅ Charge horaire calculée avec succès.";
            }
        } else {
            $message = "❌ Paramètres invalides";
            $mode = 'form';
        }
    }

} catch (Exception $e) {
    $message = "❌ Erreur système : " . $e->getMessage();
    error_log("Erreur dans le script principal: " . $e->getMessage());
}

// Fonction pour générer les options d'années scolaires
function generateYearOptions($selectedYear = '2024-2025') {
    $currentYear = date('Y');
    $options = '';
    
    for ($year = $currentYear - 2; $year <= $currentYear + 2; $year++) {
        $yearOption = $year . '-' . ($year + 1);
        $selected = ($yearOption === $selectedYear) ? 'selected' : '';
        $options .= "<option value=\"{$yearOption}\" {$selected}>{$yearOption}</option>";
    }
    
    return $options;
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo ($mode === 'results') ? 'Résultats - Charge Horaire' : 'Charge Horaire Enseignant'; ?></title>
    <style>
        :root {
            --primary-blue: #1E90FF;
            --primary-magenta: #FF1493;
            --blue-transparent: rgba(30, 144, 255, 0.3);
            --success-green: #28a745;
            --warning-orange: #ffc107;
            --danger-red: #dc3545;
            --dark-bg: rgba(10, 25, 47, 0.95);
        }

        * {
            box-sizing: border-box;
        }

        body {
            background: url('image copy 4.png') no-repeat center center fixed;
            background-size: cover;
            color: white;
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            display: flex;
            line-height: 1.6;
        }

        body::before {
            content: "";
            position: fixed;
            top: 0; left: 0; right: 0; bottom: 0;
            background-color: rgba(10, 25, 47, 0.85);
            z-index: -1;
        }

        .sidebar {
            width: 250px;
            background-color: var(--dark-bg);
            padding: 2rem 1rem;
            height: 100vh;
            position: fixed;
            top: 0;
            left: 0;
            border-right: 2px solid var(--primary-magenta);
            box-shadow: 2px 0 10px rgba(0,0,0,0.3);
            overflow-y: auto;
        }

        .sidebar img {
            max-width: 150px;
            width: 100%;
            height: auto;
            display: block;
            margin: 0 auto 20px;
            border-radius: 8px;
        }

        .sidebar a {
            display: block;
            padding: 12px 15px;
            color: white;
            text-decoration: none;
            margin-bottom: 8px;
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }

        .sidebar a:hover {
            background-color: var(--primary-blue);
            border-left-color: var(--primary-magenta);
            transform: translateX(5px);
        }

        .container {
            margin-left: 250px;
            padding: 2rem;
            width: calc(100% - 250px);
            min-height: 100vh;
        }

        h2 {
            text-align: center;
            font-size: 2.5rem;
            color: var(--primary-blue);
            text-shadow: 0 0 20px var(--blue-transparent);
            border-bottom: 3px solid var(--primary-magenta);
            padding-bottom: 1rem;
            margin-bottom: 2rem;
            background: linear-gradient(45deg, var(--primary-blue), var(--primary-magenta));
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .form-container {
            max-width: 600px;
            margin: 0 auto 2rem;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            padding: 2.5rem;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #fff;
            font-size: 1.1rem;
        }

        .form-group input, 
        .form-group select {
            width: 100%;
            padding: 12px 15px;
            border-radius: 8px;
            border: 2px solid rgba(255, 255, 255, 0.2);
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-group input:focus, 
        .form-group select:focus {
            outline: none;
            border-color: var(--primary-blue);
            background-color: rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 0 3px rgba(30, 144, 255, 0.2);
        }

        .btn {
            background: linear-gradient(45deg, var(--primary-blue), var(--primary-magenta));
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            font-size: 1.1rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(30, 144, 255, 0.4);
        }

        .btn-back {
            background: linear-gradient(45deg, var(--warning-orange), #ff8c00);
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 8px;
            display: inline-block;
            margin-bottom: 2rem;
            transition: all 0.3s ease;
            font-weight: 600;
        }

        .btn-back:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 140, 0, 0.4);
        }

        .message {
            text-align: center;
            margin: 1.5rem 0;
            font-weight: 600;
            font-size: 1.1rem;
            padding: 15px 20px;
            border-radius: 10px;
            border-left: 5px solid;
            backdrop-filter: blur(10px);
        }

        .message.success {
            background-color: rgba(40, 167, 69, 0.2);
            border-left-color: var(--success-green);
            color: #d4edda;
        }

        .message.error {
            background-color: rgba(220, 53, 69, 0.2);
            border-left-color: var(--danger-red);
            color: #f8d7da;
        }

        .results-section {
            max-width: 1200px;
            margin: 2rem auto;
        }

        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
            padding: 2rem;
            border-radius: 15px;
            text-align: center;
            border: 2px solid;
            backdrop-filter: blur(10px);
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .card.total { border-color: var(--primary-blue); }
        .card.minimal { border-color: var(--warning-orange); }
        .card.status { border-color: var(--success-green); }
        .card.status.deficit { border-color: var(--danger-red); }

        .card h3 {
            margin: 0 0 1rem 0;
            font-size: 1.1rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .card .value {
            font-size: 2.5rem;
            font-weight: bold;
            margin: 0;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .table-container {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 15px 12px;
            text-align: left;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        th {
            background: linear-gradient(45deg, var(--primary-blue), var(--primary-magenta));
            font-weight: 600;
            color: white;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 0.9rem;
        }

        tbody tr:hover {
            background-color: rgba(255, 255, 255, 0.08);
            transform: scale(1.01);
            transition: all 0.2s ease;
        }

        .type-cours { color: #ff6b6b; font-weight: 600; }
        .type-td { color: #4ecdc4; font-weight: 600; }
        .type-tp { color: #45b7d1; font-weight: 600; }

        .no-data {
            text-align: center;
            padding: 3rem 2rem;
            color: #ccc;
            font-style: italic;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            margin: 2rem 0;
        }

        .info-header {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
            padding: 1.5rem;
            border-radius: 12px;
            margin-bottom: 2rem;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .info-header h3 {
            margin: 0;
            color: var(--primary-blue);
            font-size: 1.3rem;
        }

        .debug-info {
            display: none; /* Masquer les infos de debug */
        }

        tfoot tr {
            background: linear-gradient(45deg, var(--primary-blue), var(--primary-magenta));
            font-weight: bold;
            font-size: 1.1rem;
        }

        .save-form {
            margin-top: 2rem;
            padding: 2rem;
            background: linear-gradient(135deg, rgba(40, 167, 69, 0.2), rgba(40, 167, 69, 0.1));
            border-radius: 15px;
            border: 2px solid var(--success-green);
        }

        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }
            
            .container {
                margin-left: 0;
                width: 100%;
                padding: 1rem;
            }
            
            .summary-cards {
                grid-template-columns: 1fr;
            }
            
            h2 {
                font-size: 2rem;
            }
            
            .form-container {
                padding: 2rem 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <img src="image copy 5.png" alt="Logo">
        <a href="Affichage_liste_UE.php">📚 Affichage la liste de UE</a>
        <a href="souhaits_enseignants.php">💭 Souhaits Enseignants</a>
        <a href="Calcul_automatique_charge_horaire.php">⚡ Calcul automatique de la charge horaire</a>
        <a href="gestion_charges_minimales.php">⚖ Gestion des charges minimales</a>
        <a href="Notification_non-respect_charge_minimale.php">🔔 Notification en cas de non-respect de la charge minimale</a>
        <a href="Consulter_modules_assurés_assure.php">📋 Consulter la liste des modules assurés</a>
        <a href="Uploader_notes_session_normale_rattrapage.php">📤 Uploader les notes</a>
        <a href="Consulter_historique_années_passées.php">📊 Consulter l'historique</a>
        <a href="?logout=true" style="background-color: var(--danger-red); margin-top: 1rem;">🚪 Déconnexion</a>
    </div>

    <div class="container">
        <?php if ($mode === 'form'): ?>
            <h2>Charge Horaire de l'Enseignant</h2>
            <div class="form-container">
                <form method="post">
                    <div class="form-group">
                        <label for="id_enseignant">🆔 ID Enseignant</label>
                        <input type="number" name="id_enseignant" id="id_enseignant" 
                               value="<?php echo Validator::sanitizeInput($id_enseignant_connecte); ?>" 
                               required min="1" max="9999">
                    </div>

                    <div class="form-group">
                        <label for="annee_scolaire">📅 Année scolaire</label>
                        <select name="annee_scolaire" id="annee_scolaire" required>
                            <?php echo generateYearOptions(); ?>
                        </select>
                    </div>

                    <button type="submit" name="calculer" class="btn">
                        🔍 Calculer la charge horaire
                    </button>
                </form>
            </div>

        <?php else: ?>
            <a href="<?php echo $_SERVER['PHP_SELF']; ?>" class="btn-back">← Retour au formulaire</a>
            <h2>Résultats - Charge Horaire</h2>

            <div class="info-header">
                <h3>👨‍🏫 Enseignant ID: <?php echo Validator::sanitizeInput($_GET['id_enseignant']); ?> - 
                    📅 Année: <?php echo Validator::sanitizeInput($_GET['annee_scolaire']); ?></h3>
            </div>

            <?php if (!empty($message)): ?>
                <div class="message <?php echo (strpos($message, '✅') !== false) ? 'success' : 'error'; ?>">
                    <?php echo Validator::sanitizeInput($message); ?>
                </div>
            <?php endif; ?>

            <?php if (!empty($charges_horaires)): ?>
                <div class="results-section">
                    <div class="summary-cards">
                        <div class="card total">
                            <h3>⏱ Charge Totale</h3>
                            <p class="value"><?php echo $total_charge; ?>h</p>
                        </div>
                        <div class="card minimal">
                            <h3>📏 Charge Minimale</h3>
                            <p class="value"><?php echo $charge_minimale; ?>h</p>
                        </div>
                        <div class="card status <?php echo ($total_charge < $charge_minimale) ? 'deficit' : ''; ?>">
                            <h3>📊 Statut</h3>
                            <p class="value">
                                <?php echo ($total_charge >= $charge_minimale) ? "✅ OK" : "⚠ Déficit"; ?>
                            </p>
                        </div>
                        <div class="card">
                            <h3>📈 Différence</h3>
                            <p class="value" style="color: <?php echo ($total_charge - $charge_minimale >= 0) ? 'var(--success-green)' : 'var(--danger-red)'; ?>">
                                <?php echo ($total_charge - $charge_minimale >= 0 ? '+' : '') . ($total_charge - $charge_minimale); ?>h
                            </p>
                        </div>
                    </div>

                    <div class="table-container">
                        <table>
                            <thead>
                                <tr>
                                    <th>📚 Matière</th>
                                    <th>🎓 Filière</th>
                                    <th>📊 Niveau</th>
                                    <th>🏷 Type</th>
                                    <th>⏰ Volume Horaire</th>
                                    <th>📅 Année Scolaire</th>
                                    <th>📋 Date Affectation</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($charges_horaires as $charge): ?>
                                    <tr>
                                        <td><?php echo Validator::sanitizeInput($charge['nom_matiere']); ?></td>
                                        <td><?php echo Validator::sanitizeInput($charge['filiere']); ?></td>
                                        <td><?php echo Validator::sanitizeInput($charge['niveau']); ?></td>
                                        <td>
                                            <span class="type-<?php echo strtolower($charge['type_enseignement']); ?>">
                                                <?php echo Validator::sanitizeInput($charge['type_enseignement']); ?>
                                            </span>
                                        </td>
                                        <td><strong><?php echo $charge['volume_horaire']; ?>h</strong></td>
                                        <td><?php echo Validator::sanitizeInput($charge['annee_scolaire']); ?></td>
                                        <td><?php echo date('d/m/Y H:i', strtotime($charge['date_affectation'])); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                            <tfoot>
                                <tr>
                                    <td colspan="4"><strong>Total Charge Horaire:</strong></td>
                                    <td colspan="3"><strong><?php echo $total_charge; ?>h</strong></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>

                    <!-- Formulaire de sauvegarde -->
                    <div class="save-form">
                        <h3 style="color: var(--success-green); margin-top: 0;">💾 Sauvegarder la charge horaire</h3>
                        <form method="post" style="display: flex; gap: 1rem; align-items: end;">
                            <input type="hidden" name="id_enseignant" value="<?php echo htmlspecialchars($_GET['id_enseignant']); ?>">
                            <input type="hidden" name="annee_scolaire" value="<?php echo htmlspecialchars($_GET['annee_scolaire']); ?>">
                            <input type="hidden" name="total_charge" value="<?php echo $total_charge; ?>">
                            
                            <div style="flex: 1;">
                                <label style="color: white; margin-bottom: 5px; display: block;">Confirmer la charge totale:</label>
                                <input type="number" 
                                       value="<?php echo $total_charge; ?>" 
                                       readonly 
                                       style="background-color: rgba(255,255,255,0.1); color: white; padding: 10px; border: 1px solid rgba(255,255,255,0.3); border-radius: 5px; width: 100%;">
                            </div>
                            
                            <button type="submit" name="sauvegarder" class="btn" style="width: auto; padding: 10px 20px; margin: 0;">
                                💾 Sauvegarder
                            </button>
                        </form>
                    </div>
                </div>

            <?php else: ?>
                <div class="no-data">
                    <h3>📭 Aucune donnée trouvée</h3>
                    <p>Aucune charge horaire n'a été trouvée pour l'enseignant ID <strong><?php echo Validator::sanitizeInput($_GET['id_enseignant']); ?></strong> 
                       pour l'année scolaire <strong><?php echo Validator::sanitizeInput($_GET['annee_scolaire']); ?></strong>.</p>
                    <p>Vérifiez que l'enseignant a bien des affectations pour cette période.</p>
                </div>
            <?php endif; ?>

        <?php endif; ?>
    </div>

    <script>
        // Script pour améliorer l'expérience utilisateur
        document.addEventListener('DOMContentLoaded', function() {
            // Animation des cartes de résumé
            const cards = document.querySelectorAll('.card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    card.style.transition = 'all 0.5s ease';
                    
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 150);
            });

            // Animation des lignes du tableau
            const tableRows = document.querySelectorAll('tbody tr');
            tableRows.forEach((row, index) => {
                setTimeout(() => {
                    row.style.opacity = '0';
                    row.style.transform = 'translateX(-20px)';
                    row.style.transition = 'all 0.3s ease';
                    
                    setTimeout(() => {
                        row.style.opacity = '1';
                        row.style.transform = 'translateX(0)';
                    }, 50);
                }, index * 100);
            });

            // Validation du formulaire
            const form = document.querySelector('form');
            if (form) {
                form.addEventListener('submit', function(e) {
                    const idEnseignant = document.getElementById('id_enseignant');
                    const anneeScolaire = document.getElementById('annee_scolaire');
                    
                    if (idEnseignant && idEnseignant.value < 1) {
                        e.preventDefault();
                        alert('⚠ L\'ID enseignant doit être supérieur à 0');
                        idEnseignant.focus();
                        return;
                    }
                    
                    if (anneeScolaire && anneeScolaire.value === '') {
                        e.preventDefault();
                        alert('⚠ Veuillez sélectionner une année scolaire');
                        anneeScolaire.focus();
                        return;
                    }
                });
            }

            // Confirmation avant sauvegarde
            const saveButton = document.querySelector('button[name="sauvegarder"]');
            if (saveButton) {
                saveButton.addEventListener('click', function(e) {
                    const totalCharge = this.form.querySelector('input[name="total_charge"]').value;
                    if (!confirm(🤔 Êtes-vous sûr de vouloir sauvegarder la charge horaire de ${totalCharge}h ?)) {
                        e.preventDefault();
                    }
                });
            }

            // Messages automatiques
            const messages = document.querySelectorAll('.message');
            messages.forEach(message => {
                setTimeout(() => {
                    message.style.opacity = '0';
                    message.style.transform = 'translateY(-10px)';
                    setTimeout(() => {
                        message.style.display = 'none';
                    }, 300);
                }, 5000);
            });

            // Effet de survol pour les liens de la sidebar
            const sidebarLinks = document.querySelectorAll('.sidebar a');
            sidebarLinks.forEach(link => {
                link.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateX(10px)';
                });
                
                link.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateX(0)';
                });
            });
        });

        // Fonction pour imprimer les résultats
        function printResults() {
            const printWindow = window.open('', '', 'height=600,width=800');
            const resultsSection = document.querySelector('.results-section');
            const infoHeader = document.querySelector('.info-header');
            
            printWindow.document.write('<html><head><title>Charge Horaire - Impression</title>');
            printWindow.document.write('<style>');
            printWindow.document.write('body { font-family: Arial, sans-serif; margin: 20px; }');
            printWindow.document.write('table { width: 100%; border-collapse: collapse; margin-top: 20px; }');
            printWindow.document.write('th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }');
            printWindow.document.write('th { background-color: #f2f2f2; }');
            printWindow.document.write('.summary-cards { display: flex; justify-content: space-around; margin: 20px 0; }');
            printWindow.document.write('.card { text-align: center; padding: 10px; border: 1px solid #ddd; }');
            printWindow.document.write('</style>');
            printWindow.document.write('</head><body>');
            
            if (infoHeader) {
                printWindow.document.write('<h2>' + infoHeader.textContent + '</h2>');
            }
            
            if (resultsSection) {
                printWindow.document.write(resultsSection.innerHTML);
            }
            
            printWindow.document.write('</body></html>');
            printWindow.document.close();
            printWindow.print();
        }

        // Fonction pour exporter en CSV
        function exportToCSV() {
            const table = document.querySelector('table');
            if (!table) return;
            
            let csv = [];
            const rows = table.querySelectorAll('tr');
            
            for (let i = 0; i < rows.length; i++) {
                const row = [], cols = rows[i].querySelectorAll('td, th');
                
                for (let j = 0; j < cols.length; j++) {
                    let cellText = cols[j].innerText.replace(/"/g, '""');
                    row.push('"' + cellText + '"');
                }
                
                csv.push(row.join(','));
            }
            
            const csvFile = new Blob([csv.join('\n')], { type: 'text/csv' });
            const downloadLink = document.createElement('a');
            downloadLink.download = 'charge_horaire_' + new Date().toISOString().slice(0,10) + '.csv';
            downloadLink.href = window.URL.createObjectURL(csvFile);
            downloadLink.style.display = 'none';
            document.body.appendChild(downloadLink);
            downloadLink.click();
            document.body.removeChild(downloadLink);
        }
    </script>
</body>
</html>