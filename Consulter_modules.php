<?php
session_start();
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// ===================== CONFIGURATION BASE DE DONNÉES =====================
$host = 'localhost';
$dbname = 'gestion_coordinteur';
$user = 'root';
$pass = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $user, $pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Erreur de connexion : " . $e->getMessage());
}


// Récupérer l'ID de l'enseignant depuis la session ou les paramètres
$id_enseignant = $_SESSION['id_enseignant'] ?? $_GET['id_enseignant'] ?? 19; // Valeur par défaut : 19

// Vérifier que l'ID est valide
if (!is_numeric($id_enseignant) || $id_enseignant <= 0) {
    $id_enseignant = 19; // Valeur par défaut
}

// ===================== REQUÊTE POUR RÉCUPÉRER LES MODULES =====================
$sql = "
SELECT
    ue.id_ue,
    m.nom AS nom_matiere,
    ue.filiere,
    ue.niveau,
    ue.type_enseignement,
    ue.annee_scolaire,
    ue.volume_horaire,
    a.date_affectation
FROM affectations a
JOIN unites_enseignements ue ON a.ue_id = ue.id_ue
JOIN matieres m ON ue.id_matiere = m.id_matiere
WHERE a.professeur_id = :id_enseignant
ORDER BY ue.annee_scolaire DESC, ue.filiere ASC
";

try {
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':id_enseignant', $id_enseignant, PDO::PARAM_INT);
    $stmt->execute();
    $modules = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    die("Erreur de requête : " . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modules Affectés</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --couleur-principale: #1E90FF;
            --couleur-secondaire: #0a192f;
            --couleur-texte: #ffffff;
        }

        body {
            background: url('fond.png') no-repeat center center fixed;
            background-size: cover;
            min-height: 100vh;
            font-family: 'Segoe UI', system-ui, sans-serif;
            margin-left: 280px;
            color: var(--couleur-texte);
        }

        .sidebar {
            width: 280px;
            background: rgba(10, 25, 47, 0.98);
            height: 100vh;
            position: fixed;
            left: 0;
            top: 0;
            padding: 1.5rem;
            border-right: 2px solid var(--couleur-principale);
            display: flex;
            flex-direction: column;
            overflow-y: auto;
            z-index: 1000;
        }

        .sidebar img {
            width: 180px;
            margin: 0 auto 2rem;
            padding: 10px;
            filter: brightness(0) invert(1);
        }

        .sidebar a {
            color: #ffffffcc;
            padding: 0.8rem 1.5rem;
            margin: 0.2rem 0;
            border-radius: 8px;
            transition: all 0.3s ease;
            text-decoration: none;
            position: relative;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .sidebar a:hover {
            background: rgba(30, 144, 255, 0.15);
            transform: translateX(5px);
            color: #fff !important;
        }

        .sidebar a.active {
            background: rgba(30, 144, 255, 0.3);
            color: #fff !important;
        }

        .main-content {
            padding: 30px;
            color: var(--couleur-texte);
        }

        .card {
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(10px);
            border: 1px solid var(--couleur-principale);
            margin-bottom: 20px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            overflow: hidden;
        }

        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        th {
            background: var(--couleur-principale);
            font-weight: 600;
            color: white;
        }

        tr:hover {
            background: rgba(255, 255, 255, 0.08);
        }

        .no-data {
            text-align: center;
            padding: 2rem;
            color: #ccc;
            font-style: italic;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            margin: 2rem 0;
        }

        .badge {
            font-size: 0.85em;
            padding: 0.5em 0.75em;
        }
    </style>
</head>
<body>

    <div class="sidebar">
        <img src="image copy 5.png" alt="Logo établissement">

        <nav class="nav flex-column">
            <a href="dashboard_enseignant.php">
                <i class="fas fa-tachometer-alt"></i>
                Tableau de bord
            </a>
            <a href="Affichage_liste_UE.php">
                <i class="fas fa-list-ul"></i>
                Liste des UE
            </a>
            <a href="souhaits_enseignants.php">
                <i class="fas fa-hand-paper"></i>
                Souhaits enseignants
            </a>
            <a href="Calcul_automatique_charge_horaire.php">
                <i class="fas fa-calculator"></i>
                Charge horaire
            </a>
            <a href="Notification.php">
                <i class="fas fa-bell"></i>
                Notifications
            </a>
            <a href="Consulter_modules.php" class="active">
                <i class="fas fa-book-open"></i>
                Modules assurés
            </a>
            <a href="Uploader_notes_session_normale_rattrapage.php">
                <i class="fas fa-upload"></i>
                Upload notes
            </a>
            <a href="Consulter_historique_années_passées">
                <i class="fas fa-history"></i>
                Historique
            </a>

            <a href="?logout=true" class="btn btn-danger">
                <i class="fas fa-sign-out-alt"></i>
                Déconnexion
            </a>
        </nav>
    </div>

    <div class="main-content">
        <div class="container-fluid">
            <div class="card mb-4 p-4">
                <h1 class="mb-3">Mes Modules Affectés</h1>
                <p class="text-muted">Enseignant ID : <?= htmlspecialchars($id_enseignant) ?></p>
            </div>

            <div class="card p-4">
                <?php if (count($modules) > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th><i class="fas fa-book"></i> Matière</th>
                                    <th><i class="fas fa-graduation-cap"></i> Filière</th>
                                    <th><i class="fas fa-layer-group"></i> Niveau</th>
                                    <th><i class="fas fa-chalkboard"></i> Type</th>
                                    <th><i class="fas fa-clock"></i> Volume Horaire</th>
                                    <th><i class="fas fa-calendar"></i> Année Scolaire</th>
                                    <th><i class="fas fa-calendar-plus"></i> Date Affectation</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($modules as $module): ?>
                                <tr>
                                    <td><strong><?= htmlspecialchars($module['nom_matiere']) ?></strong></td>
                                    <td><?= htmlspecialchars($module['filiere']) ?></td>
                                    <td><?= htmlspecialchars($module['niveau']) ?></td>
                                    <td>
                                        <span class="badge bg-primary">
                                            <?= htmlspecialchars($module['type_enseignement']) ?>
                                        </span>
                                    </td>
                                    <td><strong><?= htmlspecialchars($module['volume_horaire']) ?>h</strong></td>
                                    <td><?= htmlspecialchars($module['annee_scolaire']) ?></td>
                                    <td><?= date('d/m/Y H:i', strtotime($module['date_affectation'])) ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="no-data">
                        <i class="fas fa-inbox fa-3x mb-3"></i>
                        <h4>Aucun module affecté</h4>
                        <p>Aucun module n'a été affecté à votre compte pour le moment.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>