<?php
session_start();
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// ===================== CONFIGURATION BASE DE DONNÉES =====================
$host = 'localhost';
$dbname = 'gestion_coordinteur';
$user = 'root';
$pass = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $user, $pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Erreur de connexion : " . $e->getMessage());
}


$id_enseignant = $_SESSION['id_enseignant'];

// ===================== REQUÊTE POUR RÉCUPÉRER LES MODULES =====================
$sql = "
SELECT 
    ue.id_ue,
    m.nom AS nom_matiere,
    ue.filiere,
    ue.niveau,
    ue.type_enseignement,
    ue.annee_scolaire,
    ue.volume_horaire,
    a.date_affectation
FROM affectations a
JOIN unites_enseignements ue ON a.ue_id = ue.id_ue
JOIN matieres m ON ue.id_matiere = m.id_matiere
WHERE a.professeur_id = :id_enseignant
ORDER BY ue.annee_scolaire DESC, ue.filiere ASC
";

try {
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':id_enseignant', $id_enseignant, PDO::PARAM_INT);
    $stmt->execute();
    $modules = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    die("Erreur de requête : " . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modules Affectés</title>
    <style>
        :root {
            --primary-blue: #1E90FF;
            --primary-magenta: #FF00FF;
            --background-dark: rgba(10, 25, 47, 0.95);
            --text-light: #FFFFFF;
        }

        body {
            font-family: 'Segoe UI', sans-serif;
            margin: 0;
            background: var(--background-dark);
            color: var(--text-light);
            min-height: 100vh;
        }

        .sidebar {
            width: 250px;
            height: 100vh;
            position: fixed;
            background: rgba(0, 0, 0, 0.85);
            padding: 20px;
            border-right: 2px solid var(--primary-magenta);
        }

        .container {
            margin-left: 250px;
            padding: 2rem;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            overflow: hidden;
        }

        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        th {
            background: var(--primary-blue);
            font-weight: 600;
        }

        tr:hover {
            background: rgba(255, 255, 255, 0.03);
        }

        .no-data {
            text-align: center;
            padding: 2rem;
            color: #ccc;
            font-style: italic;
        }

        .last-update {
            color: #888;
            font-size: 0.9rem;
            margin-top: 10px;
        }
    </style>
</head>
<body>

    <div class="sidebar">
        <h2>Menu Enseignant</h2>
        <a href="dashboard.php">Tableau de bord</a>
        <a href="consulter_modules.php" style="background: var(--primary-blue);">Mes Modules</a>
        <a href="saisie_notes.php">Saisie des Notes</a>
        <a href="deconnexion.php" style="color: #ff4444;">Déconnexion</a>
    </div>

    <div class="container">
        <h1>Mes Modules Affectés</h1>
        
        <?php if (count($modules) > 0): ?>
            <table>
                <thead>
                    <tr>
                        <th>Matière</th>
                        <th>Filière</th>
                        <th>Niveau</th>
                        <th>Type</th>
                        <th>Volume Horaire</th>
                        <th>Année Scolaire</th>
                        <th>Date Affectation</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($modules as $module): ?>
                    <tr>
                        <td><?= htmlspecialchars($module['nom_matiere']) ?></td>
                        <td><?= htmlspecialchars($module['filiere']) ?></td>
                        <td><?= htmlspecialchars($module['niveau']) ?></td>
                        <td><?= htmlspecialchars($module['type_enseignement']) ?></td>
                        <td><?= htmlspecialchars($module['volume_horaire']) ?>h</td>
                        <td><?= htmlspecialchars($module['annee_scolaire']) ?></td>
                        <td><?= date('d/m/Y H:i', strtotime($module['date_affectation'])) ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php else: ?>
            <div class="no-data">
                Aucun module n'a été affecté à votre compte.
            </div>
        <?php endif; ?>
    </div>

</body>
</html>