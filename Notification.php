<?php
// Connexion à la base de données
$host = "localhost";
$dbname = "gestion_coordinteur";
$user = "root";
$pass = "";
$charge_minimale_attendue = 192; // seuil défini

try {
    // Établir la connexion avec la base de données
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $user, $pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // CORRECTION : Changement de 'enseignants' vers 'enseignant' (sans 's')
    // Récupérer les enseignants avec leur charge horaire depuis la table utilisateurs
    $stmt = $pdo->prepare("
        SELECT u.nom, u.prenom, ch.id_utilisateur, ch.annee_scolaire, ch.charge_min
        FROM charge_horaire_minimale ch
        JOIN utilisateurs u ON ch.id_utilisateur = u.id
        WHERE u.type_utilisateur = 'enseignant'
        ORDER BY u.nom, u.prenom
    ");
    $stmt->execute();
    $enseignants = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // DEBUG : Ajouter cette requête pour vérifier les données
    $debug_stmt = $pdo->prepare("
        SELECT u.id, u.nom, u.prenom, u.type_utilisateur,
               ch.id_utilisateur, ch.charge_min, ch.annee_scolaire
        FROM utilisateurs u
        LEFT JOIN charge_horaire_minimale ch ON u.id = ch.id_utilisateur
        WHERE u.type_utilisateur = 'enseignant'
    ");
    $debug_stmt->execute();
    $debug_results = $debug_stmt->fetchAll(PDO::FETCH_ASSOC);



} catch (PDOException $e) {
    // Afficher une erreur en cas de connexion échouée
    die("Erreur de connexion : " . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Notification de non-respect de charge</title>
    <style>
        /* Variables CSS */
        :root {
            --primary-blue: #1E90FF;
            --primary-magenta: magenta;
            --blue-transparent: rgba(30, 144, 255, 0.3);
            --success-green: #28a745;
            --warning-orange: #ffc107;
            --danger-red: #dc3545;
        }

        /* Styles généraux */
        body {
            background: url('image copy 4.png') no-repeat center center fixed;
            background-size: cover;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: white;
            margin: 0;
            padding: 0;
            display: flex;
        }

        /* Fond sombre pour l'arrière-plan */
        body::before {
            content: "";
            position: fixed;
            top: 0; left: 0; right: 0; bottom: 0;
            background-color: rgba(10, 25, 47, 0.85);
            z-index: -1;
        }

        /* Sidebar */
        .sidebar {
            width: 250px;
            background-color: rgba(10, 25, 47, 0.95);
            padding: 2rem 1rem;
            height: 100vh;
            position: fixed;
            top: 0;
            left: 0;
            border-right: 2px solid var(--primary-magenta);
            box-shadow: 2px 0 10px rgba(0,0,0,0.2);
        }

        .sidebar img {
            max-width: 150px;
            width: 100%;
            height: auto;
            display: block;
            margin: 0 auto 20px;
        }

        .sidebar a {
            display: block;
            padding: 10px 15px;
            color: white;
            text-decoration: none;
            margin-bottom: 10px;
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 6px;
            transition: all 0.3s;
        }

        .sidebar a:hover {
            background-color: var(--primary-blue);
            color: white;
        }

        /* Conteneur principal */
        .main-content {
            margin-left: 250px;
            padding: 2rem;
            width: calc(100% - 250px);
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background-color: rgba(255, 255, 255, 0.05);
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(30, 144, 255, 0.2);
        }

        /* Titre de la page */
        h2 {
            text-align: center;
            font-size: 2rem;
            color: var(--primary-blue);
            text-shadow: 0 0 10px var(--blue-transparent);
            border-bottom: 2px solid var(--primary-magenta);
            padding-bottom: 1rem;
            margin-bottom: 2rem;
        }

        /* Statistiques */
        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background-color: rgba(255, 255, 255, 0.1);
            padding: 1.5rem;
            border-radius: 10px;
            text-align: center;
            border: 2px solid transparent;
        }

        .stat-card.total {
            border-color: var(--primary-blue);
        }

        .stat-card.ok {
            border-color: var(--success-green);
        }

        .stat-card.danger {
            border-color: var(--danger-red);
        }

        .stat-card h3 {
            margin: 0 0 0.5rem 0;
            font-size: 1rem;
        }

        .stat-card .value {
            font-size: 2rem;
            font-weight: bold;
            margin: 0;
        }

        /* Tableau */
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            overflow: hidden;
        }

        th, td {
            padding: 12px;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        th {
            background-color: var(--primary-blue);
            color: white;
            font-weight: bold;
        }

        tr:hover {
            background-color: rgba(255, 255, 255, 0.05);
        }

        /* Styles pour les états */
        .danger {
            background-color: rgba(220, 53, 69, 0.2);
            color: #ff6b6b;
            font-weight: bold;
        }

        .ok {
            background-color: rgba(40, 167, 69, 0.2);
            color: #51cf66;
            font-weight: bold;
        }

        .no-data {
            text-align: center;
            padding: 2rem;
            color: #ffc107;
            font-style: italic;
            font-size: 1.1rem;
        }

        /* Badge pour les états */
        .badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: bold;
        }

        .badge-danger {
            background-color: var(--danger-red);
            color: white;
        }

        .badge-success {
            background-color: var(--success-green);
            color: white;
        }


    </style>
</head>
<body>
    <!-- Sidebar avec navigation -->
    <div class="sidebar">
        <img src="image copy 6.png" alt="Logo">
        <a href="Affichage_liste_UE.php">Affichage la liste de UE</a>
        <a href="souhaits_enseignants.php">Souhaits Enseignants</a>
        <a href="Calcul_automatique_charge_horaire.php">Calcul automatique de la charge horaire</a>
        <a href="Notification_non-respect_charge_minimale.php">Notification en cas de non-respect de la charge minimale</a>
        <a href="Consulter_modules_assurés_assure.php">Consulter la liste des modules assurés et qu'il assure.</a>
        <a href="Uploader_notes_session_normale_rattrapage.php">Uploader les notes de la session normale et rattrapage.</a>
        <a href="Consulter_historique_années_passées.php">Consulter l'historique des années passées.</a>
        <a href="?logout=true" class="btn btn-danger w-100 mt-3">Déconnexion</a>
    </div>

    <div class="main-content">
        <div class="container">
            <!-- Titre de la page -->
            <h2>Notification - Non-respect de la charge horaire minimale (<?= $charge_minimale_attendue ?> h)</h2>



            <?php if (count($enseignants) > 0): ?>
                <?php
                    // Calculer les statistiques
                    $total_enseignants = count($enseignants);
                    $enseignants_ok = 0;
                    $enseignants_deficit = 0;

                    foreach ($enseignants as $enseignant) {
                        if ($enseignant['charge_min'] >= $charge_minimale_attendue) {
                            $enseignants_ok++;
                        } else {
                            $enseignants_deficit++;
                        }
                    }
                ?>

                <!-- Cartes de statistiques -->
                <div class="stats-container">
                    <div class="stat-card ok">
                        <h3>Charge Respectée</h3>
                        <p class="value"><?= $enseignants_ok ?></p>
                    </div>
                    <div class="stat-card danger">
                        <h3>En Déficit</h3>
                        <p class="value"><?= $enseignants_deficit ?></p>
                    </div>
                    <div class="stat-card">
                        <h3>Taux de Conformité</h3>
                        <p class="value"><?= round(($enseignants_ok / $total_enseignants) * 100, 1) ?>%</p>
                    </div>
                </div>

                <!-- Tableau des enseignants -->
                <table>
                    <thead>
                        <tr>
                            <th>ID Enseignant</th>
                            <th>Nom</th>
                            <th>Prénom</th>
                            <th>Année scolaire</th>
                            <th>Charge horaire</th>
                            <th>Écart</th>
                            <th>État</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($enseignants as $enseignant): ?>
                            <?php
                                // Déterminer l'état de l'enseignant
                                $est_en_deficit = ($enseignant['charge_min'] < $charge_minimale_attendue);
                                $etat = $est_en_deficit ? "Non-respect" : "OK";
                                $class = $est_en_deficit ? "danger" : "ok";
                                $ecart = $enseignant['charge_min'] - $charge_minimale_attendue;
                            ?>
                            <tr class="<?= $class ?>">
                                <td><?= htmlspecialchars($enseignant['id_utilisateur']) ?></td>
                                <td><?= htmlspecialchars($enseignant['nom']) ?></td>
                                <td><?= htmlspecialchars($enseignant['prenom']) ?></td>
                                <td><?= htmlspecialchars($enseignant['annee_scolaire']) ?></td>
                                <td><strong><?= htmlspecialchars($enseignant['charge_min']) ?> h</strong></td>
                                <td style="color: <?= $ecart >= 0 ? 'var(--success-green)' : 'var(--danger-red)' ?>">
                                    <?= ($ecart >= 0 ? '+' : '') . $ecart ?> h
                                </td>
                                <td>
                                    <span class="badge <?= $est_en_deficit ? 'badge-danger' : 'badge-success' ?>">
                                        <?= $est_en_deficit ? '⚠️ ' . $etat : '✅ ' . $etat ?>
                                    </span>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php else: ?>
                <!-- Message en cas d'absence d'enseignants -->
                <div class="no-data">
                    <p>Aucun enseignant trouvé dans la base de données.</p>
                    <p><small>Vérifiez que des utilisateurs avec type_utilisateur="enseignant" existent et ont des charges horaires définies.</small></p>

                    <!-- Requête de diagnostic supplémentaire -->
                    <?php
                    try {
                        // Vérifier tous les types d'utilisateurs
                        $check_stmt = $pdo->prepare("SELECT DISTINCT type_utilisateur, COUNT(*) as count FROM utilisateurs GROUP BY type_utilisateur");
                        $check_stmt->execute();
                        $types = $check_stmt->fetchAll(PDO::FETCH_ASSOC);

                        echo "<div class='debug-info'>";
                        echo "<h4>🔍 Types d'utilisateurs dans la base :</h4>";
                        foreach ($types as $type) {
                            echo "<p>• <strong>" . htmlspecialchars($type['type_utilisateur']) . "</strong> : " . $type['count'] . " utilisateur(s)</p>";
                        }
                        echo "</div>";
                    } catch (Exception $e) {
                        echo "<p style='color: var(--danger-red);'>Erreur lors de la vérification : " . $e->getMessage() . "</p>";
                    }
                    ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>