<?php
// Connexion à la base de données
$host = "localhost";
$dbname = "gestion_coordinteur";
$user = "root";
$pass = "";
$charge_minimale_attendue = 192; // seuil défini

try {
    // Établir la connexion avec la base de données
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $user, $pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Récupérer l'ID de l'enseignant connecté (vous pouvez adapter selon votre système de session)
    $id_enseignant_connecte = 19; // ID de l'enseignant connecté

    // Récupérer uniquement les informations de l'enseignant connecté
    $stmt = $pdo->prepare("
        SELECT u.nom, u.prenom, ch.id_utilisateur, ch.annee_scolaire, ch.charge_min
        FROM charge_horaire_minimale ch
        JOIN utilisateurs u ON ch.id_utilisateur = u.id
        WHERE u.type_utilisateur = 'enseignant' AND u.id = ?
        ORDER BY ch.annee_scolaire DESC
    ");
    $stmt->execute([$id_enseignant_connecte]);
    $enseignants = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Récupérer les informations de base de l'enseignant connecté
    $enseignant_info_stmt = $pdo->prepare("
        SELECT nom, prenom FROM utilisateurs WHERE id = ? AND type_utilisateur = 'enseignant'
    ");
    $enseignant_info_stmt->execute([$id_enseignant_connecte]);
    $enseignant_info = $enseignant_info_stmt->fetch(PDO::FETCH_ASSOC);



} catch (PDOException $e) {
    // Afficher une erreur en cas de connexion échouée
    die("Erreur de connexion : " . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Notification de non-respect de charge</title>
    <style>
        /* Variables CSS */
        :root {
            --primary-blue: #1E90FF;
            --primary-magenta: magenta;
            --blue-transparent: rgba(30, 144, 255, 0.3);
            --success-green: #28a745;
            --warning-orange: #ffc107;
            --danger-red: #dc3545;
        }

        /* Styles généraux */
        body {
            background: url('image copy 4.png') no-repeat center center fixed;
            background-size: cover;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: white;
            margin: 0;
            padding: 0;
            display: flex;
        }

        /* Fond sombre pour l'arrière-plan */
        body::before {
            content: "";
            position: fixed;
            top: 0; left: 0; right: 0; bottom: 0;
            background-color: rgba(10, 25, 47, 0.85);
            z-index: -1;
        }

        /* Sidebar */
        .sidebar {
            width: 250px;
            background-color: rgba(10, 25, 47, 0.95);
            padding: 2rem 1rem;
            height: 100vh;
            position: fixed;
            top: 0;
            left: 0;
            border-right: 2px solid var(--primary-magenta);
            box-shadow: 2px 0 10px rgba(0,0,0,0.2);
        }

        .sidebar img {
            max-width: 150px;
            width: 100%;
            height: auto;
            display: block;
            margin: 0 auto 20px;
        }

        .sidebar a {
            display: block;
            padding: 10px 15px;
            color: white;
            text-decoration: none;
            margin-bottom: 10px;
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 6px;
            transition: all 0.3s;
        }

        .sidebar a:hover {
            background-color: var(--primary-blue);
            color: white;
        }

        /* Conteneur principal */
        .main-content {
            margin-left: 250px;
            padding: 2rem;
            width: calc(100% - 250px);
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background-color: rgba(255, 255, 255, 0.05);
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(30, 144, 255, 0.2);
        }

        /* Titre de la page */
        h2 {
            text-align: center;
            font-size: 2rem;
            color: var(--primary-blue);
            text-shadow: 0 0 10px var(--blue-transparent);
            border-bottom: 2px solid var(--primary-magenta);
            padding-bottom: 1rem;
            margin-bottom: 2rem;
        }

        /* Statistiques */
        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background-color: rgba(255, 255, 255, 0.1);
            padding: 1.5rem;
            border-radius: 10px;
            text-align: center;
            border: 2px solid transparent;
        }

        .stat-card.total {
            border-color: var(--primary-blue);
        }

        .stat-card.ok {
            border-color: var(--success-green);
        }

        .stat-card.danger {
            border-color: var(--danger-red);
        }

        .stat-card h3 {
            margin: 0 0 0.5rem 0;
            font-size: 1rem;
        }

        .stat-card .value {
            font-size: 2rem;
            font-weight: bold;
            margin: 0;
        }

        /* Tableau */
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            overflow: hidden;
        }

        th, td {
            padding: 12px;
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        th {
            background-color: var(--primary-blue);
            color: white;
            font-weight: bold;
        }

        tr:hover {
            background-color: rgba(255, 255, 255, 0.05);
        }

        /* Styles pour les états */
        .danger {
            background-color: rgba(220, 53, 69, 0.2);
            color: #ff6b6b;
            font-weight: bold;
        }

        .ok {
            background-color: rgba(40, 167, 69, 0.2);
            color: #51cf66;
            font-weight: bold;
        }

        .no-data {
            text-align: center;
            padding: 2rem;
            color: #ffc107;
            font-style: italic;
            font-size: 1.1rem;
        }

        /* Badge pour les états */
        .badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: bold;
        }

        .badge-danger {
            background-color: var(--danger-red);
            color: white;
        }

        .badge-success {
            background-color: var(--success-green);
            color: white;
        }


    </style>
</head>
<body>
    <!-- Sidebar avec navigation -->
    <div class="sidebar">
        <img src="image copy 6.png" alt="Logo">
        <a href="Affichage_liste_UE.php">Affichage la liste de UE</a>
        <a href="souhaits_enseignants.php">Souhaits Enseignants</a>
        <a href="Calcul_automatique_charge_horaire.php">Calcul automatique de la charge horaire</a>
        <a href="Notification_non-respect_charge_minimale.php">Notification en cas de non-respect de la charge minimale</a>
        <a href="Consulter_modules_assurés_assure.php">Consulter la liste des modules assurés et qu'il assure.</a>
        <a href="Uploader_notes_session_normale_rattrapage.php">Uploader les notes de la session normale et rattrapage.</a>
        <a href="Consulter_historique_années_passées.php">Consulter l'historique des années passées.</a>
        <a href="?logout=true" class="btn btn-danger w-100 mt-3">Déconnexion</a>
    </div>

    <div class="main-content">
        <div class="container">
            <!-- Titre de la page -->
            <h2>Ma Charge Horaire - <?= $enseignant_info ? htmlspecialchars($enseignant_info['prenom'] . ' ' . $enseignant_info['nom']) : 'Enseignant' ?></h2>
            <p style="text-align: center; color: var(--warning-orange); font-size: 1.1rem; margin-bottom: 2rem;">
                Charge horaire minimale requise : <strong><?= $charge_minimale_attendue ?> heures</strong>
            </p>



            <?php if (count($enseignants) > 0): ?>
                <?php
                    // Calculer les statistiques pour l'enseignant connecté
                    $total_charges = count($enseignants);
                    $charges_ok = 0;
                    $charges_deficit = 0;
                    $charge_totale = 0;

                    foreach ($enseignants as $enseignant) {
                        $charge_totale += $enseignant['charge_min'];
                        if ($enseignant['charge_min'] >= $charge_minimale_attendue) {
                            $charges_ok++;
                        } else {
                            $charges_deficit++;
                        }
                    }

                    // Calculer la charge moyenne si plusieurs années
                    $charge_moyenne = $total_charges > 0 ? round($charge_totale / $total_charges, 1) : 0;
                ?>

                <!-- Cartes de statistiques personnelles -->
                <div class="stats-container">
                    <div class="stat-card">
                        <h3>Charge Moyenne</h3>
                        <p class="value"><?= $charge_moyenne ?> h</p>
                    </div>
                    <div class="stat-card <?= $charges_ok > 0 ? 'ok' : '' ?>">
                        <h3>Années Conformes</h3>
                        <p class="value"><?= $charges_ok ?></p>
                    </div>
                    <div class="stat-card <?= $charges_deficit > 0 ? 'danger' : '' ?>">
                        <h3>Années en Déficit</h3>
                        <p class="value"><?= $charges_deficit ?></p>
                    </div>
                </div>

                <!-- Tableau de mes charges horaires -->
                <h3 style="color: var(--primary-blue); margin-bottom: 1rem;">📊 Historique de mes charges horaires</h3>
                <table>
                    <thead>
                        <tr>
                            <th>Année scolaire</th>
                            <th>Charge horaire</th>
                            <th>Écart par rapport au minimum</th>
                            <th>État</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($enseignants as $enseignant): ?>
                            <?php
                                // Déterminer l'état de l'enseignant
                                $est_en_deficit = ($enseignant['charge_min'] < $charge_minimale_attendue);
                                $etat = $est_en_deficit ? "En déficit" : "Conforme";
                                $class = $est_en_deficit ? "danger" : "ok";
                                $ecart = $enseignant['charge_min'] - $charge_minimale_attendue;
                            ?>
                            <tr class="<?= $class ?>">
                                <td><strong><?= htmlspecialchars($enseignant['annee_scolaire']) ?></strong></td>
                                <td><strong><?= htmlspecialchars($enseignant['charge_min']) ?> h</strong></td>
                                <td style="color: <?= $ecart >= 0 ? 'var(--success-green)' : 'var(--danger-red)' ?>">
                                    <strong><?= ($ecart >= 0 ? '+' : '') . $ecart ?> h</strong>
                                </td>
                                <td>
                                    <span class="badge <?= $est_en_deficit ? 'badge-danger' : 'badge-success' ?>">
                                        <?= $est_en_deficit ? '⚠️ ' . $etat : '✅ ' . $etat ?>
                                    </span>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php else: ?>
                <!-- Message en cas d'absence de charges horaires -->
                <div class="no-data">
                    <h3>📭 Aucune charge horaire définie</h3>
                    <p>Aucune charge horaire n'a été trouvée pour votre compte.</p>
                    <p><small>Contactez l'administration pour définir vos charges horaires minimales.</small></p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>