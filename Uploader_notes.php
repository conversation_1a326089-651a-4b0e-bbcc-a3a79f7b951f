<?php
// Connexion à la base de données
$conn = new mysqli("localhost", "root", "", "gestion-coordinteur");

// Définir des variables pour les messages d'erreur et de succès
$error_message = "";
$success_message = "";

// Vérifier la connexion
if ($conn->connect_error) {
    $error_message = "Erreur de connexion à la base de données : " . $conn->connect_error;
}

// Traitement du formulaire
if ($_SERVER["REQUEST_METHOD"] === "POST") {
    try {
        $id_ue = $_POST['id_ue'];
        $session = $_POST['session'];
        $notes = $_POST['notes']; // tableau associatif : [id_etudiant => note]

        // Vérifier si la table notes_etudiants existe
        $tables_check = $conn->query("SHOW TABLES LIKE 'notes_etudiants'");
        $notes_table_exists = $tables_check->num_rows > 0;

        if (!$notes_table_exists) {
            // Créer la table si elle n'existe pas
            $conn->query("CREATE TABLE IF NOT EXISTS notes_etudiants (
                id_note INT AUTO_INCREMENT PRIMARY KEY,
                id_etudiant INT NOT NULL,
                id_ue INT NOT NULL,
                session VARCHAR(20) NOT NULL,
                note DECIMAL(5,2),
                date_saisie DATETIME DEFAULT CURRENT_TIMESTAMP
            )");
        }

        // Enregistrer les notes dans la base de données
        $success = true;
        foreach ($notes as $id_etudiant => $note) {
            $note = floatval($note);
            $stmt = $conn->prepare("INSERT INTO notes_etudiants (id_etudiant, id_ue, session, note, date_saisie) VALUES (?, ?, ?, ?, NOW())");
            $stmt->bind_param("iisd", $id_etudiant, $id_ue, $session, $note);
            if (!$stmt->execute()) {
                $success = false;
                $error_message = "Erreur lors de l'enregistrement des notes: " . $stmt->error;
            }
            $stmt->close();
        }

        if ($success) {
            $success_message = "Les notes ont été enregistrées avec succès.";
        }
    } catch (Exception $e) {
        $error_message = "Une erreur est survenue: " . $e->getMessage();
    }

    // Générer le PDF directement en HTML et JavaScript (pas besoin de bibliothèque externe)
    try {
        // Récupérer les informations sur l'UE
        $nom_ue = "UE #".$id_ue;

        // Essayer de récupérer le nom de l'UE si la table existe
        $tables_check = $conn->query("SHOW TABLES LIKE 'unites_enseignements'");
        if ($tables_check->num_rows > 0) {
            // Vérifier si la colonne nom_ue existe
            $columns_check = $conn->query("SHOW COLUMNS FROM unites_enseignements LIKE 'nom_ue'");
            if ($columns_check->num_rows > 0) {
                $stmt_ue = $conn->prepare("SELECT nom_ue FROM unites_enseignements WHERE id_ue = ?");
                $stmt_ue->bind_param("i", $id_ue);
                $stmt_ue->execute();
                $result_ue = $stmt_ue->get_result();
                $ue_info = $result_ue->fetch_assoc();
                if ($ue_info) {
                    $nom_ue = $ue_info['nom_ue'];
                }
                $stmt_ue->close();
            } else {
                // Essayer avec une jointure
                $tables_check = $conn->query("SHOW TABLES LIKE 'matieres'");
                if ($tables_check->num_rows > 0) {
                    $stmt_ue = $conn->prepare("SELECT m.nom AS nom_ue
                                              FROM unites_enseignements ue
                                              JOIN matieres m ON ue.id_matiere = m.id_matiere
                                              WHERE ue.id_ue = ?");
                    $stmt_ue->bind_param("i", $id_ue);
                    $stmt_ue->execute();
                    $result_ue = $stmt_ue->get_result();
                    $ue_info = $result_ue->fetch_assoc();
                    if ($ue_info) {
                        $nom_ue = $ue_info['nom_ue'];
                    }
                    $stmt_ue->close();
                }
            }
        }

        // Récupérer les notes des étudiants
        $notes_data = [];

        // Vérifier si la table notes_etudiants existe
        $tables_check = $conn->query("SHOW TABLES LIKE 'notes_etudiants'");
        if ($tables_check->num_rows > 0) {
            // Vérifier si la table etudiants existe
            $tables_check = $conn->query("SHOW TABLES LIKE 'etudiants'");
            if ($tables_check->num_rows > 0) {
                // Vérifier si la colonne nom_etudiant existe
                $columns_check = $conn->query("SHOW COLUMNS FROM etudiants LIKE 'nom_etudiant'");
                if ($columns_check->num_rows > 0) {
                    $sql = "SELECT e.id_etudiant, e.nom_etudiant, n.note
                            FROM notes_etudiants n
                            JOIN etudiants e ON n.id_etudiant = e.id_etudiant
                            WHERE n.session = ? AND n.id_ue = ?";
                } else {
                    // Vérifier si les colonnes nom et prenom existent
                    $columns_check_nom = $conn->query("SHOW COLUMNS FROM etudiants LIKE 'nom'");
                    $columns_check_prenom = $conn->query("SHOW COLUMNS FROM etudiants LIKE 'prenom'");
                    if ($columns_check_nom->num_rows > 0 && $columns_check_prenom->num_rows > 0) {
                        $sql = "SELECT e.id_etudiant, CONCAT(e.nom, ' ', e.prenom) AS nom_etudiant, n.note
                                FROM notes_etudiants n
                                JOIN etudiants e ON n.id_etudiant = e.id_etudiant
                                WHERE n.session = ? AND n.id_ue = ?";
                    } else {
                        $sql = "SELECT n.id_etudiant, CONCAT('Étudiant #', n.id_etudiant) AS nom_etudiant, n.note
                                FROM notes_etudiants n
                                WHERE n.session = ? AND n.id_ue = ?";
                    }
                }

                $stmt = $conn->prepare($sql);
                $stmt->bind_param("si", $session, $id_ue);
                $stmt->execute();
                $result = $stmt->get_result();

                while ($row = $result->fetch_assoc()) {
                    $notes_data[] = $row;
                }

                $stmt->close();
            }
        }

        // Générer le HTML pour le PDF en utilisant des variables pour éviter les problèmes de guillemets
        $css = "
            :root {
                --primary-blue: #1E90FF;
                --primary-magenta: magenta;
                --blue-transparent: rgba(30, 144, 255, 0.3);
            }
            body {
                font-family: Segoe UI, Tahoma, Geneva, Verdana, sans-serif;
                margin: 20px;
                color: #333;
                background-color: #f9f9f9;
            }
            h1, h2 {
                text-align: center;
                color: var(--primary-blue);
            }
            h1 {
                font-size: 28px;
                margin-bottom: 5px;
                text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
            }
            h2 {
                font-size: 20px;
                margin-top: 5px;
                color: #555;
            }
            .header {
                margin-bottom: 30px;
                padding-bottom: 15px;
                border-bottom: 2px solid var(--primary-magenta);
            }
            table {
                width: 100%;
                border-collapse: collapse;
                margin: 20px 0;
                box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                border-radius: 5px;
                overflow: hidden;
            }
            th, td {
                border: 1px solid #ddd;
                padding: 12px;
                text-align: center;
            }
            th {
                background-color: var(--primary-blue);
                color: white;
                font-weight: bold;
            }
            tr:nth-child(even) {
                background-color: #f2f2f2;
            }
            tr:hover {
                background-color: #e9f5ff;
            }
            .footer {
                margin-top: 50px;
                text-align: right;
                color: #555;
                font-style: italic;
                border-top: 1px solid #ddd;
                padding-top: 15px;
            }
            .signature-line {
                display: inline-block;
                width: 200px;
                border-bottom: 1px solid #555;
                margin-left: 10px;
            }
            @media print {
                body {
                    margin: 0;
                    background-color: white;
                }
                button { display: none; }
                .header {
                    border-bottom-color: #333;
                }
            }
        ";

        $session_title = htmlspecialchars(ucfirst($session));
        $ue_title = htmlspecialchars($nom_ue);

        // Début du HTML
        $html_content = "<!DOCTYPE html>
        <html>
        <head>
            <meta charset=\"UTF-8\">
            <title>Relevé de Notes - Session {$session_title}</title>
            <style>{$css}</style>
        </head>
        <body>
            <div class=\"header\">
                <h1>Relevé de Notes</h1>
                <h2>Session: {$session_title}</h2>
                <h2>Unité d'Enseignement: {$ue_title}</h2>
            </div>

            <table>
                <thead>
                    <tr>
                        <th>ID Étudiant</th>
                        <th>Nom Étudiant</th>
                        <th>Note</th>
                    </tr>
                </thead>
                <tbody>";

        // Ajouter les données des étudiants
        foreach ($notes_data as $note) {
            $html_content .= '
                    <tr>
                        <td>' . htmlspecialchars($note['id_etudiant']) . '</td>
                        <td>' . htmlspecialchars($note['nom_etudiant']) . '</td>
                        <td>' . htmlspecialchars($note['note']) . '</td>
                    </tr>';
        }

        // Si aucune donnée, ajouter un message
        if (empty($notes_data)) {
            $html_content .= '
                    <tr>
                        <td colspan="3">Aucune note enregistrée pour cette session et cette UE.</td>
                    </tr>';
        }

        // Fermeture du tableau et ajout du pied de page
        $date_generation = date('d/m/Y');
        $html_content .= "
                </tbody>
            </table>

            <div class=\"footer\">
                <p>Document généré le {$date_generation}</p>
                <p>Signature de l'enseignant: <span class=\"signature-line\"></span></p>
            </div>

            <div style=\"text-align: center; margin-top: 30px;\">
                <button onclick=\"window.print()\">Imprimer ce document</button>
            </div>

            <script>
                // Imprimer automatiquement
                window.onload = function() {
                    window.print();
                };
            </script>
        </body>
        </html>";

        // Enregistrer le HTML dans un fichier temporaire
        $temp_file = 'notes_session_' . $session . 'ue' . $id_ue . '_' . time() . '.html';
        file_put_contents($temp_file, $html_content);

        // Rediriger vers le fichier HTML
        header('Location: ' . $temp_file);
        exit;

    } catch (Exception $e) {
        $error_message = "Erreur lors de la génération du PDF: " . $e->getMessage();
    }
}

// Vérifier si les tables existent
$tables_check = $conn->query("SHOW TABLES LIKE 'unites_enseignements'");
$ue_table_exists = $tables_check->num_rows > 0;

$tables_check = $conn->query("SHOW TABLES LIKE 'etudiants'");
$etudiants_table_exists = $tables_check->num_rows > 0;

// Récupérer les UE avec plus d'informations
if ($ue_table_exists) {
    // Vérifier si la colonne nom_ue existe
    $columns_check = $conn->query("SHOW COLUMNS FROM unites_enseignements LIKE 'nom_ue'");
    if ($columns_check->num_rows > 0) {
        $res_ue = $conn->query("SELECT id_ue, nom_ue FROM unites_enseignements ORDER BY nom_ue");
    } else {
        // Si la colonne nom_ue n'existe pas, utiliser une jointure avec la table matieres
        $res_ue = $conn->query("SELECT ue.id_ue, m.nom AS nom_ue
                               FROM unites_enseignements ue
                               JOIN matieres m ON ue.id_matiere = m.id_matiere
                               ORDER BY m.nom");
    }
} else {
    // Créer des données fictives pour permettre de tester le formulaire
    $res_ue = $conn->query("SELECT 1 as id_ue, 'Exemple UE 1' as nom_ue FROM DUAL UNION SELECT 2 as id_ue, 'Exemple UE 2' as nom_ue FROM DUAL");
    $error_message = "Attention: Aucune unité d'enseignement trouvée dans la base de données. Des exemples sont affichés pour tester le formulaire.";
}

// Récupérer les étudiants avec plus d'informations
if ($etudiants_table_exists) {
    // Vérifier si la colonne nom_etudiant existe
    $columns_check = $conn->query("SHOW COLUMNS FROM etudiants LIKE 'nom_etudiant'");
    if ($columns_check->num_rows > 0) {
        $res_etudiants = $conn->query("SELECT id_etudiant, nom_etudiant FROM etudiants ORDER BY nom_etudiant");
    } else {
        // Si la colonne nom_etudiant n'existe pas, utiliser les colonnes nom et prenom
        $res_etudiants = $conn->query("SELECT id_etudiant, CONCAT(nom, ' ', prenom) AS nom_etudiant FROM etudiants ORDER BY nom, prenom");
    }
} else {
    // Créer des données fictives pour permettre de tester le formulaire
    $res_etudiants = $conn->query("SELECT 1 as id_etudiant, 'Jean Dupont' as nom_etudiant FROM DUAL
                                  UNION SELECT 2 as id_etudiant, 'Marie Martin' as nom_etudiant FROM DUAL
                                  UNION SELECT 3 as id_etudiant, 'Pierre Durand' as nom_etudiant FROM DUAL");
    if (empty($error_message)) {
        $error_message = "Attention: Aucun étudiant trouvé dans la base de données. Des exemples sont affichés pour tester le formulaire.";
    }
}

?>

<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <title>Saisie des Notes</title>
  <style>
    :root {
      --primary-blue: #1E90FF;
      --primary-magenta: magenta;
      --blue-transparent: rgba(30, 144, 255, 0.3);
    }

    body {
      background: url('image copy 4.png') no-repeat center center fixed;
      background-size: cover;
      color: white;
      min-height: 100vh;
      font-family: \"Segoe UI\", Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      display: flex;
    }

    body::before {
      content: "";
      position: fixed;
      top: 0; left: 0; right: 0; bottom: 0;
      background-color: rgba(10, 25, 47, 0.85);
      z-index: -1;
    }

    .sidebar {
      width: 250px;
      background-color: rgba(10, 25, 47, 0.95);
      padding: 2rem 1rem;
      height: 100vh;
      position: fixed;
      top: 0;
      left: 0;
      border-right: 2px solid var(--primary-magenta);
      box-shadow: 2px 0 10px rgba(0, 0, 0, 0.2);
    }

    .sidebar img {
      max-width: 150px;
      width: 100%;
      height: auto;
      display: block;
      margin: 0 auto 20px;
    }

    .sidebar a {
      display: block;
      padding: 10px 15px;
      color: white;
      text-decoration: none;
      margin-bottom: 10px;
      background-color: rgba(255, 255, 255, 0.05);
      border-radius: 6px;
      transition: all 0.3s;
    }

    .sidebar a:hover, .sidebar a.active {
      background-color: var(--primary-blue);
      color: white;
    }

    .sidebar a.active {
      border-left: 4px solid var(--primary-magenta);
      font-weight: bold;
    }

    .container {
      margin-left: 250px;
      padding: 2rem;
      width: calc(100% - 250px);
    }

    h2 {
      text-align: center;
      font-size: 2rem;
      color: var(--primary-blue);
      text-shadow: 0 0 10px var(--blue-transparent);
      border-bottom: 2px solid var(--primary-magenta);
      padding-bottom: 1rem;
      margin-bottom: 2rem;
    }

    form {
      background-color: rgba(255, 255, 255, 0.05);
      padding: 20px;
      border-radius: 10px;
      width: 800px;
      margin: 20px auto;
      box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
      border: 1px solid var(--blue-transparent);
    }

    input, select {
      width: 100%;
      padding: 10px;
      margin: 8px 0;
      background-color: rgba(255, 255, 255, 0.1);
      border: 1px solid var(--blue-transparent);
      border-radius: 5px;
      color: white;
    }

    input::placeholder {
      color: rgba(255, 255, 255, 0.6);
    }

    input:focus, select:focus {
      outline: none;
      border-color: var(--primary-blue);
      box-shadow: 0 0 5px var(--primary-blue);
    }

    table {
      width: 100%;
      border-collapse: collapse;
      background-color: rgba(255, 255, 255, 0.05);
      border-radius: 10px;
      overflow: hidden;
      box-shadow: 0 0 10px rgba(30, 144, 255, 0.2);
      margin-top: 20px;
    }

    th, td {
      padding: 15px;
      text-align: center;
      border-bottom: 1px solid var(--blue-transparent);
      color: white;
    }

    th {
      background-color: var(--primary-blue);
      color: white;
    }

    tr:nth-child(even) {
      background-color: rgba(255, 255, 255, 0.05);
    }

    button {
      padding: 12px 25px;
      background-color: var(--primary-blue);
      color: white;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      font-weight: bold;
      transition: all 0.3s;
    }

    button:hover {
      background-color: #0056b3;
      box-shadow: 0 0 10px var(--primary-blue);
    }

    #test-pdf-btn {
      background-color: var(--primary-magenta);
    }

    #test-pdf-btn:hover {
      background-color: #cc00cc;
      box-shadow: 0 0 10px var(--primary-magenta);
    }

    .form-section {
      margin-bottom: 20px;
    }

    .info-box {
      background-color: rgba(30, 144, 255, 0.1);
      padding: 15px;
      border-radius: 5px;
      margin: 15px 0;
      border-left: 4px solid var(--primary-blue);
      color: white;
    }

    input[type="number"] {
      width: 80px;
      text-align: center;
      background-color: rgba(255, 255, 255, 0.1);
    }

    .error-message {
      background-color: rgba(211, 47, 47, 0.2);
      color: #ff6b6b;
      padding: 15px;
      border-radius: 5px;
      margin: 15px auto;
      max-width: 800px;
      border-left: 4px solid #d32f2f;
    }

    .success-message {
      background-color: rgba(56, 142, 60, 0.2);
      color: #69f0ae;
      padding: 15px;
      border-radius: 5px;
      margin: 15px auto;
      max-width: 800px;
      border-left: 4px solid #388e3c;
    }

    .no-data {
      text-align: center;
      font-size: 1.2rem;
      color: #ccc;
      margin-top: 40px;
    }

    label {
      color: white;
      font-weight: 500;
      display: block;
      margin-bottom: 5px;
    }
  </style>
</head>
<body>

<div class="sidebar">
  <img src="image copy 8.png" alt="Logo">
  <a href="Affichage_liste_UE.php">Affichage la liste de UE</a>
  <a href="souhaits_enseignants.php">Souhaits Enseignants</a>
  <a href="Calcul_automatique_charge_horaire.php">Calcul automatique de la charge horaire</a>
  <a href="Notification_non-respect_charge_minimale.php">Notification en cas de non-respect de la charge minimale</a>
  <a href="Consulter_modules_assurés_assure.php">Consulter la liste des modules assurés</a>
  <a href="Uploader_notes_session_normale_rattrapage.php" class="active">Uploader les notes</a>
</div>

<div class="container">
  <h2>Saisie des Notes des Étudiants</h2>

<?php if (!empty($error_message)): ?>
  <div class="error-message">
    <?php echo $error_message; ?>
  </div>
<?php endif; ?>

<?php if (!empty($success_message)): ?>
  <div class="success-message">
    <?php echo $success_message; ?>
  </div>
<?php endif; ?>

<form method="POST">
  <div class="form-section">
    <label for="id_ue">Unité d'Enseignement :</label>
    <select name="id_ue" required>
      <?php
      while ($row = $res_ue->fetch_assoc()) {
          echo "<option value='{$row['id_ue']}'>{$row['nom_ue']} (UE n°{$row['id_ue']})</option>";
      }
      ?>
    </select>
  </div>

  <div class="form-section">
    <label for="session">Session :</label>
    <select name="session" required>
      <option value="normale">Normale</option>
      <option value="rattrapage">Rattrapage</option>
    </select>
  </div>

  <div class="info-box">
    <p><i>Remplissez les notes pour chaque étudiant. Après avoir cliqué sur "Enregistrer les notes", un PDF sera automatiquement généré et téléchargé.</i></p>
  </div>

  <table>
    <thead>
      <tr>
        <th>ID Étudiant</th>
        <th>Nom Étudiant</th>
        <th>Note</th>
      </tr>
    </thead>
    <tbody>
      <?php
      while ($row = $res_etudiants->fetch_assoc()) {
          echo "<tr>";
          echo "<td>{$row['id_etudiant']}</td>";
          echo "<td>{$row['nom_etudiant']}</td>";
          echo "<td><input type='number' step='0.01' min='0' max='20' name='notes[{$row['id_etudiant']}]' required></td>";
          echo "</tr>";
      }
      ?>
    </tbody>
  </table>

  <div style="display: flex; justify-content: space-between; margin-top: 20px;">
    <button type="submit" name="action" value="enregistrer">Enregistrer les notes</button>
    <button type="button" id="test-pdf-btn" style="background-color: #28a745;">Tester le PDF (sans enregistrer)</button>
  </div>
</form>

<script>
  // Script pour gérer le téléchargement du PDF
  document.querySelector('form').addEventListener('submit', function(e) {
    // Le formulaire sera soumis normalement, le serveur générera le PDF
    // Aucune action JavaScript supplémentaire n'est nécessaire car le PHP gère le téléchargement
  });

  // Script pour tester le PDF sans enregistrer les données
  document.getElementById('test-pdf-btn').addEventListener('click', function() {
    // Récupérer les valeurs du formulaire
    const ueSelect = document.querySelector('select[name="id_ue"]');
    const sessionSelect = document.querySelector('select[name="session"]');
    const ueText = ueSelect.options[ueSelect.selectedIndex].text;
    const sessionText = sessionSelect.options[sessionSelect.selectedIndex].text;

    // Créer le contenu HTML pour l'impression
    const css = `
      :root {
        --primary-blue: #1E90FF;
        --primary-magenta: magenta;
        --blue-transparent: rgba(30, 144, 255, 0.3);
      }
      body {
        font-family: Segoe UI, Tahoma, Geneva, Verdana, sans-serif;
        margin: 20px;
        color: #333;
        background-color: #f9f9f9;
      }
      h1, h2 {
        text-align: center;
        color: var(--primary-blue);
      }
      h1 {
        font-size: 28px;
        margin-bottom: 5px;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
      }
      h2 {
        font-size: 20px;
        margin-top: 5px;
        color: #555;
      }
      .header {
        margin-bottom: 30px;
        padding-bottom: 15px;
        border-bottom: 2px solid var(--primary-magenta);
      }
      table {
        width: 100%;
        border-collapse: collapse;
        margin: 20px 0;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        border-radius: 5px;
        overflow: hidden;
      }
      th, td {
        border: 1px solid #ddd;
        padding: 12px;
        text-align: center;
      }
      th {
        background-color: var(--primary-blue);
        color: white;
        font-weight: bold;
      }
      tr:nth-child(even) {
        background-color: #f2f2f2;
      }
      tr:hover {
        background-color: #e9f5ff;
      }
      .footer {
        margin-top: 50px;
        text-align: right;
        color: #555;
        font-style: italic;
        border-top: 1px solid #ddd;
        padding-top: 15px;
      }
      .signature-line {
        display: inline-block;
        width: 200px;
        border-bottom: 1px solid #555;
        margin-left: 10px;
      }
      .watermark {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) rotate(-45deg);
        font-size: 100px;
        color: rgba(30, 144, 255, 0.1);
        z-index: -1;
        font-weight: bold;
      }
      button {
        background-color: var(--primary-blue);
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        font-weight: bold;
      }
      button:hover {
        background-color: #0056b3;
      }
      @media print {
        body {
          margin: 0;
          background-color: white;
        }
        button { display: none; }
        .header {
          border-bottom-color: #333;
        }
      }
    `;

    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>Relevé de Notes - Test</title>
        <style>${css}</style>
      </head>
      <body>
        <div class="watermark">APERÇU</div>
        <div class="header">
          <h1>Relevé de Notes</h1>
          <h2>Session: ${sessionText}</h2>
          <h2>Unité d'Enseignement: ${ueText}</h2>
        </div>

        <table>
          <thead>
            <tr>
              <th>ID Étudiant</th>
              <th>Nom Étudiant</th>
              <th>Note</th>
            </tr>
          </thead>
          <tbody>
    `;

    // Récupérer les données du formulaire
    const rows = document.querySelectorAll('tbody tr');
    let hasData = false;

    rows.forEach(row => {
      const id = row.cells[0].textContent;
      const name = row.cells[1].textContent;
      const noteInput = row.querySelector('input[type="number"]');
      const note = noteInput.value || '-';

      if (note !== '-') {
        hasData = true;
      }

      htmlContent += `
            <tr>
              <td>${id}</td>
              <td>${name}</td>
              <td>${note}</td>
            </tr>
      `;
    });

    // Si aucune note n'est saisie
    if (!hasData) {
      htmlContent += `
            <tr>
              <td colspan="3" style="font-style: italic; color: #777;">
                Aucune note n'a été saisie. Ceci est un aperçu du document.
              </td>
            </tr>
      `;
    }

    htmlContent += `
          </tbody>
        </table>

        <div class="footer">
          <p>Document généré le ${new Date().toLocaleDateString()}</p>
          <p>Signature de l'enseignant: <span class="signature-line"></span></p>
        </div>

        <div style="text-align: center; margin-top: 30px;">
          <button onclick="window.print()">Imprimer cet aperçu</button>
        </div>

        <script>
          // Imprimer automatiquement
          window.onload = function() {
            window.print();
          };
        </script>
      </body>
      </html>
    `;

    // Ouvrir dans une nouvelle fenêtre pour impression
    const win = window.open('', '_blank');
    win.document.write(htmlContent);
    win.document.close();
  });
</script>

</div> <!-- Fermeture de la div container -->
</body>
</html>