<?php
require_once 'config.php';

// Activer l'affichage des erreurs pour le débogage
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

try {
    // Connexion à la base de données
    $pdo = new PDO(
        'mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8',
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    echo "<h1>Ajout de la colonne id_specialite à la table utilisateurs</h1>";
    
    // Vérifier si la table utilisateurs existe
    $tables = $pdo->query("SHOW TABLES LIKE 'utilisateurs'")->fetchAll();
    if (count($tables) === 0) {
        echo "<p>La table 'utilisateurs' n'existe pas.</p>";
        exit;
    }
    
    // Vérifier si la colonne id_specialite existe déjà
    $columns = $pdo->query("DESCRIBE utilisateurs")->fetchAll();
    $hasSpecialiteColumn = false;
    
    foreach ($columns as $column) {
        if ($column['Field'] === 'id_specialite') {
            $hasSpecialiteColumn = true;
            break;
        }
    }
    
    if ($hasSpecialiteColumn) {
        echo "<p>La colonne 'id_specialite' existe déjà dans la table utilisateurs.</p>";
    } else {
        // Ajouter la colonne id_specialite
        $pdo->exec("ALTER TABLE utilisateurs ADD COLUMN id_specialite INT NULL AFTER id_departement");
        echo "<p>La colonne 'id_specialite' a été ajoutée avec succès à la table utilisateurs.</p>";
    }
    
    // Afficher la structure mise à jour de la table utilisateurs
    $columns = $pdo->query("DESCRIBE utilisateurs")->fetchAll();
    echo "<h2>Structure mise à jour de la table utilisateurs</h2>";
    echo "<table border='1'>";
    echo "<tr><th>Nom</th><th>Type</th><th>Null</th><th>Clé</th><th>Défaut</th><th>Extra</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "<td>{$column['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<p><a href='gestion_chef_departement.php'>Retourner à la gestion des chefs de département</a></p>";
    
} catch (PDOException $e) {
    echo "<h1>Erreur</h1>";
    echo "<p>Erreur de base de données: " . $e->getMessage() . "</p>";
}
?>
