<?php
require_once 'config.php';
session_start();

if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'admin') {
    header("Location: login_coordinateur.php");
    exit;
}

try {
    $pdo = new PDO(
        "mysql:host=".DB_HOST.";dbname=".DB_NAME.";charset=utf8",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );

    // Statistiques globales
    try {
        $stats = $pdo->query("
            SELECT
                (SELECT COUNT(*) FROM enseignants) AS total_professeurs,
                (SELECT COUNT(*) FROM vacataires) AS total_vacataires,
                (SELECT COUNT(*) FROM unites_enseignements) AS total_departements,
                (SELECT COUNT(*) FROM matieres) AS total_specialites,
                (SELECT COUNT(*) FROM affectations_vacataires) AS total_affectations
        ")->fetch();
    } catch (PDOException $e) {
        // En cas d'erreur, créer un tableau avec des valeurs par défaut
        $stats = [
            'total_professeurs' => 0,
            'total_vacataires' => 0,
            'total_departements' => 0,
            'total_specialites' => 0,
            'total_affectations' => 0
        ];
    }

    // Dernières affectations
    try {
        // Vérifier si la colonne date_affectation existe
        $check_column = $pdo->query("SHOW COLUMNS FROM affectations_vacataires LIKE 'date_affectation'");
        $column_exists = $check_column->rowCount() > 0;

        if ($column_exists) {
            $affectations = $pdo->query("
                SELECT
                    v.nom,
                    v.prenom,
                    m.nom_matiere AS specialite,
                    ue.nom_ue AS departement,
                    av.date_affectation
                FROM affectations_vacataires av
                JOIN vacataires v ON av.id_vacataire = v.id_vacataire
                JOIN matieres m ON av.id_matiere = m.id_matiere
                JOIN unites_enseignements ue ON m.id_ue = ue.id_ue
                ORDER BY av.date_affectation DESC
                LIMIT 5
            ")->fetchAll();
        } else {
            // Version alternative sans date_affectation
            $affectations = $pdo->query("
                SELECT
                    v.nom,
                    v.prenom,
                    m.nom_matiere AS specialite,
                    ue.nom_ue AS departement,
                    NOW() AS date_affectation
                FROM affectations_vacataires av
                JOIN vacataires v ON av.id_vacataire = v.id_vacataire
                JOIN matieres m ON av.id_matiere = m.id_matiere
                JOIN unites_enseignements ue ON m.id_ue = ue.id_ue
                LIMIT 5
            ")->fetchAll();
        }
    } catch (PDOException $e) {
        // En cas d'erreur, créer un tableau vide
        $affectations = [];
    }

    // Données pour les graphiques
    $chart_data = [
        'labels' => ['Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi'],
        'affectations' => [12, 19, 3, 5, 2]
    ];

} catch(PDOException $e) {
    die("Erreur de base de données : " . htmlspecialchars($e->getMessage()));
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Admin - ENSAH</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #1E90FF;
            --primary-magenta: magenta;
            --blue-transparent: rgba(30, 144, 255, 0.3);
            --dark-bg: #0a192f;
        }

        body {
            background: linear-gradient(rgba(10, 25, 47, 0.85), rgba(108, 27, 145, 0.85)),
            url('images/background.jpg') center center/cover fixed;
            color: white;
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .sidebar {
            background: rgba(44, 62, 80, 0.95);
            backdrop-filter: blur(5px);
            border-right: 2px solid var(--primary-blue);
            box-shadow: 4px 0 15px var(--blue-transparent);
            animation: borderGlow 8s infinite alternate;
        }

        @keyframes borderGlow {
            0% { border-color: var(--primary-blue); }
            50% { border-color: var(--primary-magenta); }
            100% { border-color: var(--primary-blue); }
        }

        .sidebar .nav-link {
            color: white;
            padding: 12px 15px;
            margin: 8px 0;
            border-radius: 8px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
x le desin
        .sidebar .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(18, 84, 151, 0.2), transparent);
            transition: 0.5s;
        }

        .sidebar .nav-link:hover {
            background: rgba(30, 144, 255, 0.1);
            transform: translateX(10px);
        }

        .sidebar .nav-link:hover::before {
            left: 100%;
        }

        /* Styles pour le menu déroulant */
        .sidebar .nav-link.dropdown-toggle::after {
            display: inline-block;
            margin-left: 0.5em;
            vertical-align: 0.15em;
            content: "";
            border-top: 0.3em solid;
            border-right: 0.3em solid transparent;
            border-bottom: 0;
            border-left: 0.3em solid transparent;
            transition: transform 0.3s ease;
        }

        .sidebar .nav-link.dropdown-toggle[aria-expanded="true"]::after {
            transform: rotate(180deg);
        }

        .sidebar #userSubmenu {
            padding-left: 0;
            list-style: none;
            transition: all 0.3s ease;
        }

        .sidebar #userSubmenu .nav-link {
            padding-left: 2.5rem;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            margin: 4px 0;
        }

        .sidebar #userSubmenu .nav-link:hover {
            background-color: rgba(30, 144, 255, 0.2);
        }

        .sidebar #userSubmenu .nav-link.active {
            background-color: rgba(30, 144, 255, 0.3);
            border-left: 3px solid var(--primary-blue);
        }

        .stat-card {
            background: rgba(10, 25, 47, 0.9);
            border: 2px solid var(--primary-blue);
            border-radius: 10px;
            transition: all 0.3s ease;
            animation: cardBorderPulse 10s infinite;
        }

        @keyframes cardBorderPulse {
            0% { border-color: var(--primary-blue); box-shadow: 0 5px 15px var(--blue-transparent); }
            50% { border-color: var(--primary-magenta); box-shadow: 0 5px 20px rgba(255, 0, 255, 0.3); }
            100% { border-color: var(--primary-blue); box-shadow: 0 5px 15px var(--blue-transparent); }
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(255, 0, 255, 0.4) !important;
        }

        .chart-container {
            background: rgba(10, 25, 47, 0.9);
            border: 2px solid var(--primary-blue);
            border-radius: 10px;
            animation: cardBorderPulse 10s infinite;
        }

        #affectationsTable {
            background: rgba(10, 25, 47, 0.9);
            border: 1px solid var(--primary-blue);
            color: white;
        }

        #affectationsTable thead {
            background: linear-gradient(90deg, var(--primary-blue) 0%, var(--dark-bg) 100%);
            border-bottom: 2px solid var(--primary-magenta);
        }

        .btn-outline-primary {
            border-color: var(--primary-blue);
            color: var(--primary-blue);
        }

        .btn-outline-primary:hover {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-magenta) 100%);
            border-color: transparent;
        }
    </style>
</head>
<body>
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar Modifiée -->
        <nav class="col-md-3 col-lg-2 sidebar">
        <div class="text-center mb-4">
    <img src="images/logo.png" alt="ENSAH" class="img-fluid mb-3" style="filter: drop-shadow(0 0 5px var(--primary-blue));">
    <h5 class="text-white" style="text-shadow: 0 0 10px var(--primary-blue);">Administration ENSAH</h5>
</div>
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link active" href="admin_dashboard.php" style="
                        background: rgba(30, 144, 255, 0.2);
                        border-left: 4px solid var(--primary-blue);
                        transform: translateX(8px);
                    ">
                        <i class="fas fa-chart-line me-2"></i>Tableau de bord
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link dropdown-toggle" href="#userSubmenu" data-bs-toggle="collapse" aria-expanded="false">
                        <i class="fas fa-users-cog me-2"></i>
                        Gestion Utilisateurs
                    </a>
                    <ul class="collapse" id="userSubmenu">
                        <li class="nav-item">
                            <a class="nav-link ms-3" href="gestion_chef_departement.php">
                                <i class="fas fa-user-tie me-2"></i> Chefs de département
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link ms-3" href="gestion_coordinateur.php">
                                <i class="fas fa-user-cog me-2"></i> Coordinateurs
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link ms-3" href="gestion_enseignant.php">
                                <i class="fas fa-chalkboard-teacher me-2"></i> Enseignants
                            </a>
                        </li>
                    </ul>
                </li>



                <li class="nav-item mt-4">
                    <a class="nav-link text-danger" href="logout.php">
                        <i class="fas fa-sign-out-alt me-2"></i>Déconnexion
                    </a>
                </li>
            </ul>
        </nav>

        <!-- Main Content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 p-4">
            <!-- Cartes de résumé -->
            <div class="row g-4 mb-4">
                <div class="col-6 col-lg-3">
                    <div class="stat-card">
                        <h5>Professeurs</h5>
                        <h2 class="text-primary"><?= $stats['total_professeurs'] ?? 0 ?></h2>
                        <small class="text-muted">+12% vs mois dernier</small>
                    </div>
                </div>
                <div class="col-6 col-lg-3">
                    <div class="stat-card">
                        <h5>Vacataires</h5>
                        <h2 class="text-success"><?= $stats['total_vacataires'] ?? 0 ?></h2>
                        <small class="text-muted">+3 nouveaux ce mois</small>
                    </div>
                </div>
                <div class="col-6 col-lg-3">
                    <div class="stat-card">
                        <h5>Départements</h5>
                        <h2 class="text-warning"><?= $stats['total_departements'] ?? 0 ?></h2>
                        <small class="text-muted">5 spécialités actives</small>
                    </div>
                </div>
                <div class="col-6 col-lg-3">
                    <div class="stat-card">
                        <h5>Affectations</h5>
                        <h2 class="text-info"><?= $stats['total_affectations'] ?? 0 ?></h2>
                        <small class="text-muted">15 en attente</small>
                    </div>
                </div>
            </div>

            <!-- Tableau des dernières affectations -->
            <div class="card shadow mb-4">
                <div class="card-header">
                    <h5 class="m-0"><i class="fas fa-table me-2"></i>Dernières affectations</h5>
                </div>
                <div class="card-body">
                    <table class="table table-hover" id="affectationsTable">
                        <thead>
                            <tr>
                                <th>Nom</th>
                                <th>Prénom</th>
                                <th>Spécialité</th>
                                <th>Département</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($affectations as $aff): ?>
                            <tr>
                                <td><?= htmlspecialchars($aff['nom']) ?></td>
                                <td><?= htmlspecialchars($aff['prenom']) ?></td>
                                <td><?= htmlspecialchars($aff['specialite']) ?></td>
                                <td><?= htmlspecialchars($aff['departement']) ?></td>
                                <td><?= date('d/m/Y', strtotime($aff['date_affectation'])) ?></td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Graphiques -->
            <div class="row">
                <div class="col-md-6">
                    <div class="chart-container">
                        <canvas id="affectationsChart"></canvas>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="chart-container">
                        <canvas id="specialitesChart"></canvas>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
<script>
    // Script pour le menu déroulant
    document.addEventListener('DOMContentLoaded', function() {
        // Gestion du clic sur les liens du sous-menu
        const subMenuLinks = document.querySelectorAll('#userSubmenu .nav-link');
        subMenuLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                // Empêcher la fermeture du menu déroulant lors du clic sur un sous-élément
                e.stopPropagation();
            });
        });

        // Gestion de l'animation de la flèche du menu déroulant
        const dropdownToggle = document.querySelector('.nav-link.dropdown-toggle');
        if (dropdownToggle) {
            dropdownToggle.addEventListener('click', function() {
                const isExpanded = this.getAttribute('aria-expanded') === 'true';
                this.setAttribute('aria-expanded', !isExpanded);
            });
        }
    });
</script>
<script>
    // Configuration DataTable
    $('#affectationsTable').DataTable({
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/fr-FR.json'
        },
        columnDefs: [
            { orderable: false, targets: [5] },
            { className: "text-center", targets: [5] }
        ]
    });

    // Configuration Chart.js
    new Chart(document.getElementById('affectationsChart'), {
        type: 'bar',
        data: {
            labels: <?= json_encode($chart_data['labels']) ?>,
            datasets: [{
                label: 'Affectations par jour',
                data: <?= json_encode($chart_data['affectations']) ?>,
                backgroundColor: '#1abc9c',
                borderColor: 'rgba(255, 255, 255, 0.8)',
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    labels: { color: 'white' }
                }
            },
            scales: {
                y: {
                    ticks: { color: 'white' },
                    grid: { color: 'rgba(255, 255, 255, 0.1)' }
                },
                x: {
                    ticks: { color: 'white' },
                    grid: { color: 'rgba(255, 255, 255, 0.1)' }
                }
            }
        }
    });
</script>
</body>
</html>