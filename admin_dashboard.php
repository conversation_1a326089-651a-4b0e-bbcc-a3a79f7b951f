<?php
require_once 'config.php';
session_start();

if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'admin') {
    header("Location: login_coordinateur.php");
    exit;
}

try {
    $pdo = new PDO(
        "mysql:host=".DB_HOST.";dbname=".DB_NAME.";charset=utf8",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );

    // Statistiques globales
    try {
        $stats = $pdo->query("
            SELECT
                (SELECT COUNT(*) FROM enseignants) AS total_professeurs,
                (SELECT COUNT(*) FROM vacataires) AS total_vacataires,
                (SELECT COUNT(*) FROM unites_enseignements) AS total_departements,
                (SELECT COUNT(*) FROM matieres) AS total_specialites,
                (SELECT COUNT(*) FROM affectations_vacataires) AS total_affectations
        ")->fetch();
    } catch (PDOException $e) {
        // En cas d'erreur, créer un tableau avec des valeurs par défaut
        $stats = [
            'total_professeurs' => 0,
            'total_vacataires' => 0,
            'total_departements' => 0,
            'total_specialites' => 0,
            'total_affectations' => 0
        ];
    }

    // Dernières affectations
    try {
        // Vérifier si la colonne date_affectation existe
        $check_column = $pdo->query("SHOW COLUMNS FROM affectations_vacataires LIKE 'date_affectation'");
        $column_exists = $check_column->rowCount() > 0;

        if ($column_exists) {
            $affectations = $pdo->query("
                SELECT
                    v.nom,
                    v.prenom,
                    m.nom_matiere AS specialite,
                    ue.nom_ue AS departement,
                    av.date_affectation
                FROM affectations_vacataires av
                JOIN vacataires v ON av.id_vacataire = v.id_vacataire
                JOIN matieres m ON av.id_matiere = m.id_matiere
                JOIN unites_enseignements ue ON m.id_ue = ue.id_ue
                ORDER BY av.date_affectation DESC
                LIMIT 5
            ")->fetchAll();
        } else {
            // Version alternative sans date_affectation
            $affectations = $pdo->query("
                SELECT
                    v.nom,
                    v.prenom,
                    m.nom_matiere AS specialite,
                    ue.nom_ue AS departement,
                    NOW() AS date_affectation
                FROM affectations_vacataires av
                JOIN vacataires v ON av.id_vacataire = v.id_vacataire
                JOIN matieres m ON av.id_matiere = m.id_matiere
                JOIN unites_enseignements ue ON m.id_ue = ue.id_ue
                LIMIT 5
            ")->fetchAll();
        }
    } catch (PDOException $e) {
        // En cas d'erreur, créer un tableau vide
        $affectations = [];
    }

    // Données pour les graphiques
    $chart_data = [
        'labels' => ['Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi'],
        'affectations' => [12, 19, 3, 5, 2]
    ];

} catch(PDOException $e) {
    die("Erreur de base de données : " . htmlspecialchars($e->getMessage()));
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Admin - ENSAH</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary-color: #6a0dad;
            --secondary-color: #8a2be2;
            --light-purple: #e6e6fa;
            --dark-purple: #4b0082;
            --accent-color: #00bfff;
        }

        body {
            background-color: #f0f0f5;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
        }

        .header {
            background-color: var(--primary-color);
            color: white;
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .header-left {
            display: flex;
            align-items: center;
        }

        .header img {
            width: 50px;
            height: 50px;
            margin-right: 15px;
        }

        .header h1 {
            margin: 0;
            font-size: 24px;
        }

        .header-right {
            display: flex;
            flex-direction: column;
            gap: 5px;
            text-align: right;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .user-info i {
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            width: 20px;
            text-align: center;
        }

        .user-info-label {
            font-weight: bold;
            color: rgba(255, 255, 255, 0.8);
        }

        .user-info-value {
            color: white;
        }

        .content {
            display: flex;
            padding: 20px;
            gap: 20px;
        }

        .sidebar {
            width: 220px;
            background-color: var(--light-purple);
            border-radius: 10px;
            padding: 15px;
        }

        .sidebar-title {
            background-color: var(--primary-color);
            color: white;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            font-weight: bold;
        }

        .sidebar-menu {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .menu-item {
            display: flex;
            align-items: center;
            background-color: #fff;
            padding: 10px;
            border-radius: 5px;
            color: var(--dark-purple);
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .menu-item:hover {
            background-color: var(--secondary-color);
            color: white;
            transform: translateX(5px);
        }

        .menu-item i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }

        .logout-btn {
            margin-top: auto;
            background-color: #ff4757;
            color: white;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .stats-cards {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .stat-card {
            background-color: #fff;
            border-radius: 10px;
            padding: 15px;
            display: flex;
            align-items: center;
            width: 200px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: white;
        }

        .professeurs-icon {
            background-color: var(--primary-color);
        }

        .vacataires-icon {
            background-color: var(--secondary-color);
        }

        .departements-icon {
            background-color: var(--accent-color);
        }

        .affectations-icon {
            background-color: #ff6b6b;
        }

        .stat-info {
            display: flex;
            flex-direction: column;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }

        .stat-label {
            font-size: 12px;
            color: #777;
        }

        .chart-container {
            background-color: #fff;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            height: 300px; /* Hauteur fixe pour les graphiques */
        }

        .chart-title {
            color: var(--dark-purple);
            font-weight: bold;
            margin-bottom: 15px;
        }

        .table-container {
            background-color: #fff;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .table-title {
            color: var(--dark-purple);
            font-weight: bold;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .table-title i {
            margin-right: 10px;
            color: var(--primary-color);
        }

        /* Styles pour le menu déroulant */
        .dropdown-menu {
            display: none;
            padding-left: 20px;
        }

        .dropdown-menu.show {
            display: block;
        }

        .dropdown-toggle::after {
            display: inline-block;
            margin-left: 0.5em;
            vertical-align: 0.15em;
            content: "";
            border-top: 0.3em solid;
            border-right: 0.3em solid transparent;
            border-bottom: 0;
            border-left: 0.3em solid transparent;
            transition: transform 0.3s ease;
        }

        .dropdown-toggle[aria-expanded="true"]::after {
            transform: rotate(180deg);
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-left">
            <img src="images/logo.png" alt="ENSAH Logo" onerror="this.src='https://img.icons8.com/color/48/000000/dashboard.png'">
            <h1>Bienvenue Administrateur</h1>
        </div>
        <div class="header-right">
            <div class="user-info">
                <i class="fas fa-envelope"></i>
                <span class="user-info-label">Email :</span>
                <span class="user-info-value"><?= $_SESSION['email'] ?? '<EMAIL>' ?></span>
            </div>
            <div class="user-info">
                <i class="fas fa-user-shield"></i>
                <span class="user-info-label">Rôle :</span>
                <span class="user-info-value">Administrateur</span>
            </div>
            <div class="user-info">
                <i class="fas fa-calendar-alt"></i>
                <span class="user-info-label">Année scolaire :</span>
                <span class="user-info-value">2024-2025</span>
            </div>
        </div>
    </div>

    <div class="content">
        <div class="sidebar">
            <div class="sidebar-title">Menu Principal</div>
            <div class="sidebar-menu">
                <a href="admin_dashboard.php" class="menu-item">
                    <i class="fas fa-tachometer-alt"></i> Tableau de bord
                </a>
                <a href="#" class="menu-item dropdown-toggle" id="userDropdown" data-bs-toggle="collapse" data-bs-target="#userSubmenu" aria-expanded="false">
                    <i class="fas fa-users-cog"></i> Gestion Utilisateurs
                </a>
                <div class="dropdown-menu collapse" id="userSubmenu">
                    <a href="gestion_chef_departement.php" class="menu-item">
                        <i class="fas fa-user-tie"></i> Chefs de département
                    </a>
                    <a href="gestion_coordinateur.php" class="menu-item">
                        <i class="fas fa-user-cog"></i> Coordinateurs
                    </a>
                    <a href="gestion_enseignant.php" class="menu-item">
                        <i class="fas fa-chalkboard-teacher"></i> Enseignants
                    </a>
                </div>
                <a href="logout.php" class="menu-item logout-btn">
                    <i class="fas fa-sign-out-alt"></i> Déconnexion
                </a>
            </div>
        </div>

        <div class="main-content">
            <!-- Cartes de statistiques -->
            <div class="stats-cards">
                <div class="stat-card">
                    <div class="stat-icon professeurs-icon">
                        <i class="fas fa-chalkboard-teacher"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-number"><?= $stats['total_professeurs'] ?? 0 ?></div>
                        <div class="stat-label">Professeurs</div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon vacataires-icon">
                        <i class="fas fa-user-tie"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-number"><?= $stats['total_vacataires'] ?? 0 ?></div>
                        <div class="stat-label">Vacataires</div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon departements-icon">
                        <i class="fas fa-building"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-number"><?= $stats['total_departements'] ?? 0 ?></div>
                        <div class="stat-label">Départements</div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon affectations-icon">
                        <i class="fas fa-link"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-number"><?= $stats['total_affectations'] ?? 0 ?></div>
                        <div class="stat-label">Affectations</div>
                    </div>
                </div>
            </div>

            <!-- Espace supplémentaire pour les graphiques -->
            <div class="mb-4"></div>

            <!-- Graphiques -->
            <div class="row">
                <div class="col-md-5">
                    <div class="chart-container">
                        <div class="chart-title">Affectations par jour</div>
                        <canvas id="affectationsChart"></canvas>
                    </div>
                </div>
                <div class="col-md-7">
                    <div class="chart-container">
                        <div class="chart-title">Répartition par spécialité</div>
                        <canvas id="specialitesChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

<script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>

    // Initialisation des graphiques
    document.addEventListener('DOMContentLoaded', function() {
        // Graphique des affectations
        var affectationsCtx = document.getElementById('affectationsChart').getContext('2d');
        var affectationsChart = new Chart(affectationsCtx, {
            type: 'bar',
            data: {
                labels: <?= json_encode($chart_data['labels']) ?>,
                datasets: [{
                    label: 'Affectations par jour',
                    data: <?= json_encode($chart_data['affectations']) ?>,
                    backgroundColor: 'rgba(106, 13, 173, 0.2)',
                    borderColor: 'rgba(106, 13, 173, 1)',
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });

        // Graphique des spécialités (exemple)
        var specialitesCtx = document.getElementById('specialitesChart').getContext('2d');
        var specialitesChart = new Chart(specialitesCtx, {
            type: 'pie',
            data: {
                labels: ['Informatique', 'Génie Civil', 'Électronique', 'Mécanique', 'Autres'],
                datasets: [{
                    data: [30, 20, 15, 25, 10],
                    backgroundColor: [
                        'rgba(106, 13, 173, 0.7)',
                        'rgba(138, 43, 226, 0.7)',
                        'rgba(0, 191, 255, 0.7)',
                        'rgba(255, 107, 107, 0.7)',
                        'rgba(100, 149, 237, 0.7)'
                    ],
                    borderColor: '#ffffff',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right'
                    }
                }
            }
        });
    });

    // Gestion du menu déroulant
    document.addEventListener('DOMContentLoaded', function() {
        const dropdownToggle = document.querySelector('.dropdown-toggle');
        if (dropdownToggle) {
            dropdownToggle.addEventListener('click', function() {
                const submenu = document.querySelector(this.getAttribute('data-bs-target'));
                submenu.classList.toggle('show');
                const isExpanded = this.getAttribute('aria-expanded') === 'true';
                this.setAttribute('aria-expanded', !isExpanded);
            });
        }
    });
</script>
</body>
</html>