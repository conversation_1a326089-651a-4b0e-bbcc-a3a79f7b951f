<?php
// 1. Configuration et initialisation
if (!file_exists(__DIR__ . '/config.php')) {
    die("Fichier de configuration manquant");
}
require __DIR__ . '/config.php';

// 2. Gestion de session
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// 3. Protection CSRF
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}
$csrf_token = $_SESSION['csrf_token'];

// 4. Variables initiales
$error = null;
$success = $_SESSION['success'] ?? null;
unset($_SESSION['success']);

$affectations = [];
$professeurs = [];
$unites_enseignement = [];

// 5. Connexion et requêtes adaptées à votre structure
try {
    $pdo = new PDO(
        'mysql:host='.DB_HOST.';dbname='.DB_NAME.';charset=utf8mb4',
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );

    // REQUÊTE PRINCIPALE SIMPLIFIÉE
    try {
        // Vérifier si la colonne annee existe dans la table affectations
        $stmt = $pdo->query("SHOW COLUMNS FROM affectations LIKE 'annee'");
        $anneeExists = $stmt->rowCount() > 0;

        // Vérifier si la colonne semestre existe dans la table affectations
        $stmt = $pdo->query("SHOW COLUMNS FROM affectations LIKE 'semestre'");
        $semestreExists = $stmt->rowCount() > 0;

        // Vérifier si la colonne date_debut existe dans la table affectations
        $stmt = $pdo->query("SHOW COLUMNS FROM affectations LIKE 'date_debut'");
        $dateDebutExists = $stmt->rowCount() > 0;

        // Vérifier si la colonne date_fin existe dans la table affectations
        $stmt = $pdo->query("SHOW COLUMNS FROM affectations LIKE 'date_fin'");
        $dateFinExists = $stmt->rowCount() > 0;

        // Vérifier si la colonne heures existe dans la table affectations
        $stmt = $pdo->query("SHOW COLUMNS FROM affectations LIKE 'heures'");
        $heuresExists = $stmt->rowCount() > 0;

        // Vérifier si la colonne code_ue existe dans la table unites_enseignements
        $stmt = $pdo->query("SHOW COLUMNS FROM unites_enseignements LIKE 'code_ue'");
        $codeUeExists = $stmt->rowCount() > 0;

        // Construire la requête en fonction des colonnes existantes
        $query = "
            SELECT a.id,
                   p.id as prof_id,
                   CONCAT(p.nom, ' ', p.prenom) as professeur,
                   ue.id_ue as ue_id,
                   ue.filiere as unite_enseignement";

        if ($codeUeExists) {
            $query .= ",\n                   ue.code_ue as code_ue";
        } else {
            $query .= ",\n                   CONCAT('UE', ue.id_ue) as code_ue";
        }

        if ($anneeExists) {
            $query .= ",\n                   a.annee";
        } else {
            $query .= ",\n                   " . date('Y') . " as annee";
        }

        if ($semestreExists) {
            $query .= ",\n                   a.semestre";
        } else {
            $query .= ",\n                   1 as semestre";
        }

        if ($dateDebutExists) {
            $query .= ",\n                   DATE_FORMAT(a.date_debut, '%d/%m/%Y') as date_debut_format";
        } else {
            $query .= ",\n                   DATE_FORMAT(NOW(), '%d/%m/%Y') as date_debut_format";
        }

        if ($dateFinExists) {
            $query .= ",\n                   IFNULL(DATE_FORMAT(a.date_fin, '%d/%m/%Y'), '-') as date_fin_format";
        } else {
            $query .= ",\n                   '-' as date_fin_format";
        }

        if ($heuresExists) {
            $query .= ",\n                   a.heures";
        } else {
            $query .= ",\n                   30 as heures";
        }

        // Vérifier si la colonne ue_id existe dans la table affectations
        $stmt = $pdo->query("SHOW COLUMNS FROM affectations LIKE 'ue_id'");
        $ueIdExists = $stmt->rowCount() > 0;

        // Vérifier si la colonne id_ue existe dans la table affectations
        $stmt = $pdo->query("SHOW COLUMNS FROM affectations LIKE 'id_ue'");
        $idUeExists = $stmt->rowCount() > 0;

        // Vérifier si la table utilisateurs existe
        $stmt = $pdo->query("SHOW TABLES LIKE 'utilisateurs'");
        $utilisateursExists = $stmt->rowCount() > 0;

        if ($utilisateursExists) {
            // Utiliser la table utilisateurs
            if ($ueIdExists) {
                $query .= "\n            FROM affectations a
                JOIN utilisateurs p ON a.professeur_id = p.id AND p.type_utilisateur = 'enseignant'
                JOIN unites_enseignements ue ON a.ue_id = ue.id_ue";
            } else if ($idUeExists) {
                $query .= "\n            FROM affectations a
                JOIN utilisateurs p ON a.professeur_id = p.id AND p.type_utilisateur = 'enseignant'
                JOIN unites_enseignements ue ON a.id_ue = ue.id_ue";
            } else {
                // Si aucune des colonnes n'existe, utiliser une jointure simplifiée
                $query .= "\n            FROM affectations a
                JOIN utilisateurs p ON a.professeur_id = p.id AND p.type_utilisateur = 'enseignant'
                JOIN unites_enseignements ue ON 1=1";
            }
        } else {
            // Fallback sur la table professeurs
            if ($ueIdExists) {
                $query .= "\n            FROM affectations a
                JOIN professeurs p ON a.professeur_id = p.id
                JOIN unites_enseignements ue ON a.ue_id = ue.id_ue";
            } else if ($idUeExists) {
                $query .= "\n            FROM affectations a
                JOIN professeurs p ON a.professeur_id = p.id
                JOIN unites_enseignements ue ON a.id_ue = ue.id_ue";
            } else {
                // Si aucune des colonnes n'existe, utiliser une jointure simplifiée
                $query .= "\n            FROM affectations a
                JOIN professeurs p ON a.professeur_id = p.id
                JOIN unites_enseignements ue ON 1=1";
            }
        }

        if ($dateDebutExists) {
            $query .= "\n            ORDER BY a.date_debut DESC";
        } else {
            $query .= "\n            ORDER BY a.id DESC";
        }

        $stmt = $pdo->prepare($query);

    } catch (PDOException $e) {
        // En cas d'erreur, utiliser une requête simplifiée sans les colonnes problématiques
        // Vérifier si la colonne ue_id existe dans la table affectations
        $stmt = $pdo->query("SHOW COLUMNS FROM affectations LIKE 'ue_id'");
        $ueIdExists = $stmt->rowCount() > 0;

        // Vérifier si la colonne id_ue existe dans la table affectations
        $stmt = $pdo->query("SHOW COLUMNS FROM affectations LIKE 'id_ue'");
        $idUeExists = $stmt->rowCount() > 0;

        // Vérifier si la table utilisateurs existe
        $stmt = $pdo->query("SHOW TABLES LIKE 'utilisateurs'");
        $utilisateursExists = $stmt->rowCount() > 0;

        if ($utilisateursExists) {
            // Utiliser la table utilisateurs
            if ($ueIdExists) {
                $stmt = $pdo->prepare("
                    SELECT a.id,
                           p.id as prof_id,
                           CONCAT(p.nom, ' ', p.prenom) as professeur,
                           ue.id_ue as ue_id,
                           ue.filiere as unite_enseignement,
                           CONCAT('UE', ue.id_ue) as code_ue,
                           " . date('Y') . " as annee,
                           1 as semestre,
                           DATE_FORMAT(NOW(), '%d/%m/%Y') as date_debut_format,
                           '-' as date_fin_format,
                           30 as heures
                    FROM affectations a
                    JOIN utilisateurs p ON a.professeur_id = p.id AND p.type_utilisateur = 'enseignant'
                    JOIN unites_enseignements ue ON a.ue_id = ue.id_ue
                    ORDER BY a.id DESC
                ");
            } else if ($idUeExists) {
                $stmt = $pdo->prepare("
                    SELECT a.id,
                           p.id as prof_id,
                           CONCAT(p.nom, ' ', p.prenom) as professeur,
                           ue.id_ue as ue_id,
                           ue.filiere as unite_enseignement,
                           CONCAT('UE', ue.id_ue) as code_ue,
                           " . date('Y') . " as annee,
                           1 as semestre,
                           DATE_FORMAT(NOW(), '%d/%m/%Y') as date_debut_format,
                           '-' as date_fin_format,
                           30 as heures
                    FROM affectations a
                    JOIN utilisateurs p ON a.professeur_id = p.id AND p.type_utilisateur = 'enseignant'
                    JOIN unites_enseignements ue ON a.id_ue = ue.id_ue
                    ORDER BY a.id DESC
                ");
            } else {
                // Si aucune des colonnes n'existe, utiliser une requête simplifiée
                $stmt = $pdo->prepare("
                    SELECT a.id,
                           p.id as prof_id,
                           CONCAT(p.nom, ' ', p.prenom) as professeur,
                           ue.id_ue as ue_id,
                           ue.filiere as unite_enseignement,
                           CONCAT('UE', ue.id_ue) as code_ue,
                           " . date('Y') . " as annee,
                           1 as semestre,
                           DATE_FORMAT(NOW(), '%d/%m/%Y') as date_debut_format,
                           '-' as date_fin_format,
                           30 as heures
                    FROM affectations a
                    JOIN utilisateurs p ON a.professeur_id = p.id AND p.type_utilisateur = 'enseignant'
                    CROSS JOIN unites_enseignements ue
                    ORDER BY a.id DESC
                    LIMIT 10
                ");
            }
        } else {
            // Fallback sur la table professeurs
            if ($ueIdExists) {
                $stmt = $pdo->prepare("
                    SELECT a.id,
                           p.id as prof_id,
                           CONCAT(p.nom, ' ', p.prenom) as professeur,
                           ue.id_ue as ue_id,
                           ue.filiere as unite_enseignement,
                           CONCAT('UE', ue.id_ue) as code_ue,
                           " . date('Y') . " as annee,
                           1 as semestre,
                           DATE_FORMAT(NOW(), '%d/%m/%Y') as date_debut_format,
                           '-' as date_fin_format,
                           30 as heures
                    FROM affectations a
                    JOIN professeurs p ON a.professeur_id = p.id
                    JOIN unites_enseignements ue ON a.ue_id = ue.id_ue
                    ORDER BY a.id DESC
                ");
            } else if ($idUeExists) {
                $stmt = $pdo->prepare("
                    SELECT a.id,
                           p.id as prof_id,
                           CONCAT(p.nom, ' ', p.prenom) as professeur,
                           ue.id_ue as ue_id,
                           ue.filiere as unite_enseignement,
                           CONCAT('UE', ue.id_ue) as code_ue,
                           " . date('Y') . " as annee,
                           1 as semestre,
                           DATE_FORMAT(NOW(), '%d/%m/%Y') as date_debut_format,
                           '-' as date_fin_format,
                           30 as heures
                    FROM affectations a
                    JOIN professeurs p ON a.professeur_id = p.id
                    JOIN unites_enseignements ue ON a.id_ue = ue.id_ue
                    ORDER BY a.id DESC
                ");
            } else {
                // Si aucune des colonnes n'existe, utiliser une requête simplifiée
                $stmt = $pdo->prepare("
                    SELECT a.id,
                           p.id as prof_id,
                           CONCAT(p.nom, ' ', p.prenom) as professeur,
                           ue.id_ue as ue_id,
                           ue.filiere as unite_enseignement,
                           CONCAT('UE', ue.id_ue) as code_ue,
                           " . date('Y') . " as annee,
                           1 as semestre,
                           DATE_FORMAT(NOW(), '%d/%m/%Y') as date_debut_format,
                           '-' as date_fin_format,
                           30 as heures
                    FROM affectations a
                    JOIN professeurs p ON a.professeur_id = p.id
                    CROSS JOIN unites_enseignements ue
                    ORDER BY a.id DESC
                    LIMIT 10
                ");
            }
        }
    }
    $stmt->execute();
    $affectations = $stmt->fetchAll();

    // Récupération des professeurs depuis la table utilisateurs
    try {
        // Vérifier si la table utilisateurs existe
        $stmt = $pdo->query("SHOW TABLES LIKE 'utilisateurs'");
        $utilisateursExists = $stmt->rowCount() > 0;

        if ($utilisateursExists) {
            // Récupérer les enseignants depuis la table utilisateurs
            $professeurs = $pdo->query("
                SELECT id, CONCAT(nom, ' ', prenom) as nom_complet
                FROM utilisateurs
                WHERE type_utilisateur = 'enseignant'
                ORDER BY nom, prenom
            ")->fetchAll();
        } else {
            // Fallback sur la table professeurs si utilisateurs n'existe pas
            $professeurs = $pdo->query("
                SELECT id, CONCAT(nom, ' ', prenom) as nom_complet
                FROM professeurs
                ORDER BY nom, prenom
            ")->fetchAll();
        }
    } catch (PDOException $e) {
        // En cas d'erreur, utiliser un tableau vide
        $professeurs = [];
        $error = "Erreur lors de la récupération des professeurs: " . $e->getMessage();
    }

    // Récupération des UE (adaptée à votre structure)
    try {
        // Vérifier quelle table existe : unites_enseignement ou unites_enseignements
        $stmt = $pdo->query("SHOW TABLES LIKE 'unites_enseignement'");
        $ueTableExists = $stmt->rowCount() > 0;

        if ($ueTableExists) {
            $ueTableName = 'unites_enseignement';
            echo "<!-- Table unites_enseignement existe -->";
        } else {
            $stmt = $pdo->query("SHOW TABLES LIKE 'unites_enseignements'");
            $uesTableExists = $stmt->rowCount() > 0;

            if ($uesTableExists) {
                $ueTableName = 'unites_enseignements';
                echo "<!-- Table unites_enseignements existe -->";
            } else {
                // Aucune des tables n'existe, créer la table unites_enseignement
                $pdo->exec("
                    CREATE TABLE IF NOT EXISTS unites_enseignement (
                        id_ue INT AUTO_INCREMENT PRIMARY KEY,
                        code_ue VARCHAR(20) NOT NULL,
                        intitule VARCHAR(255) NOT NULL,
                        filiere VARCHAR(100) NOT NULL,
                        credit INT DEFAULT 3,
                        semestre INT DEFAULT 1
                    )
                ");
                $ueTableName = 'unites_enseignement';
                echo "<!-- Table unites_enseignement créée -->";

                // Ajouter une UE par défaut
                $pdo->exec("INSERT INTO unites_enseignement (code_ue, intitule, filiere, credit, semestre) VALUES ('UE001', 'Unité d''enseignement par défaut', 'Général', 3, 1)");
            }
        }

        // Vérifier si la table matieres existe
        $stmt = $pdo->query("SHOW TABLES LIKE 'matieres'");
        $matieresExists = $stmt->rowCount() > 0;

        // Vérifier si la colonne id_matiere existe dans la table
        $stmt = $pdo->query("SHOW COLUMNS FROM $ueTableName LIKE 'id_matiere'");
        $idMatiereExists = $stmt->rowCount() > 0;

        // Vérifier si la colonne niveau existe dans la table
        $stmt = $pdo->query("SHOW COLUMNS FROM $ueTableName LIKE 'niveau'");
        $niveauExists = $stmt->rowCount() > 0;

        if ($matieresExists && $idMatiereExists) {
            // Si la table matieres existe et la colonne id_matiere existe
            $query = "
                SELECT ue.id_ue as id,
                       CONCAT(m.code, ' - ', m.nom, ' (', ue.filiere";

            if ($niveauExists) {
                $query .= ", ' ', ue.niveau";
            }

            $query .= ")') as nom_complet,
                       m.code as code_ue
                FROM $ueTableName ue
                JOIN matieres m ON ue.id_matiere = m.id_matiere
                ORDER BY m.code
            ";
        } else {
            // Requête simplifiée sans la table matieres
            $query = "
                SELECT ue.id_ue as id,
                       CONCAT('UE', ue.id_ue, ' - ', ue.filiere";

            if ($niveauExists) {
                $query .= ", ' (', ue.niveau, ')'";
            } else {
                $query .= "')";
            }

            $query .= " as nom_complet,
                       CONCAT('UE', ue.id_ue) as code_ue
                FROM $ueTableName ue
                ORDER BY ue.id_ue
            ";
        }

        $unites_enseignement = $pdo->query($query)->fetchAll();
    } catch (PDOException $e) {
        // En cas d'erreur, utiliser une requête très simplifiée
        $unites_enseignement = $pdo->query("
            SELECT ue.id_ue as id,
                   CONCAT('UE', ue.id_ue, ' - ', ue.filiere) as nom_complet,
                   CONCAT('UE', ue.id_ue) as code_ue
            FROM $ueTableName ue
            ORDER BY ue.id_ue
        ")->fetchAll();
    }

} catch (PDOException $e) {
    $error = "Erreur de base de données: " . $e->getMessage();
    error_log("DB Error: " . $e->getMessage());
}

// 6. Traitement du formulaire
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['ajouter_affectation'])) {
    try {
        // Validation CSRF
        if (empty($_POST['csrf_token']) || !hash_equals($csrf_token, $_POST['csrf_token'])) {
            throw new Exception("Erreur de sécurité: Token invalide");
        }

        // Validation des données
        $required = [
            'professeur_id' => 'Professeur',
            'ue_id' => 'Unité d\'enseignement',
            'annee' => 'Année',
            'semestre' => 'Semestre',
            'date_debut' => 'Date de début',
            'heures' => 'Nombre d\'heures'
        ];

        $errors = [];
        foreach ($required as $field => $name) {
            if (empty($_POST[$field])) {
                $errors[] = "Le champ '$name' est requis";
            }
        }

        if (!empty($errors)) {
            throw new Exception(implode("<br>", $errors));
        }

        // Vérifier si la table utilisateurs existe
        $stmt = $pdo->query("SHOW TABLES LIKE 'utilisateurs'");
        $utilisateursExists = $stmt->rowCount() > 0;

        // Vérifier si la colonne ue_id existe dans la table affectations
        $stmt = $pdo->query("SHOW COLUMNS FROM affectations LIKE 'ue_id'");
        $ueIdExists = $stmt->rowCount() > 0;

        // Vérifier si la colonne id_ue existe dans la table affectations
        $stmt = $pdo->query("SHOW COLUMNS FROM affectations LIKE 'id_ue'");
        $idUeExists = $stmt->rowCount() > 0;

        // Vérifier si le professeur existe dans la table utilisateurs
        if ($utilisateursExists) {
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM utilisateurs WHERE id = ? AND type_utilisateur = 'enseignant'");
            $stmt->execute([(int)$_POST['professeur_id']]);
            $professorExists = $stmt->fetchColumn() > 0;

            if (!$professorExists) {
                throw new Exception("Le professeur sélectionné n'existe pas ou n'est pas un enseignant.");
            }

            // Vérifier s'il y a des contraintes de clé étrangère sur la table affectations
            try {
                // Récupérer les informations sur les contraintes de clé étrangère
                $stmt = $pdo->query("
                    SELECT CONSTRAINT_NAME, COLUMN_NAME, REFERENCED_TABLE_NAME
                    FROM information_schema.KEY_COLUMN_USAGE
                    WHERE TABLE_SCHEMA = DATABASE()
                    AND TABLE_NAME = 'affectations'
                    AND REFERENCED_TABLE_NAME IS NOT NULL
                ");

                $constraints = $stmt->fetchAll(PDO::FETCH_ASSOC);

                // S'il y a des contraintes, les supprimer
                if (!empty($constraints)) {
                    foreach ($constraints as $constraint) {
                        $pdo->exec("ALTER TABLE affectations DROP FOREIGN KEY `{$constraint['CONSTRAINT_NAME']}`");
                        echo "<!-- Contrainte supprimée: {$constraint['CONSTRAINT_NAME']} (colonne: {$constraint['COLUMN_NAME']}, référence: {$constraint['REFERENCED_TABLE_NAME']}) -->";
                    }
                }

                // Vérifier si le professeur existe dans la table professeurs
                // Si non, l'ajouter pour maintenir l'intégrité référentielle
                $stmt = $pdo->prepare("SELECT COUNT(*) FROM professeurs WHERE id = ?");
                $stmt->execute([(int)$_POST['professeur_id']]);
                $profExistsInProfTable = $stmt->fetchColumn() > 0;

                if (!$profExistsInProfTable) {
                    // Récupérer les informations du professeur depuis la table utilisateurs
                    $stmt = $pdo->prepare("
                        SELECT id, nom, prenom, email
                        FROM utilisateurs
                        WHERE id = ? AND type_utilisateur = 'enseignant'
                    ");
                    $stmt->execute([(int)$_POST['professeur_id']]);
                    $prof = $stmt->fetch(PDO::FETCH_ASSOC);

                    if ($prof) {
                        // Insérer le professeur dans la table professeurs
                        $stmt = $pdo->prepare("
                            INSERT INTO professeurs (id, nom, prenom, email)
                            VALUES (:id, :nom, :prenom, :email)
                            ON DUPLICATE KEY UPDATE
                            nom = :nom, prenom = :prenom, email = :email
                        ");
                        $stmt->execute([
                            'id' => $prof['id'],
                            'nom' => $prof['nom'],
                            'prenom' => $prof['prenom'],
                            'email' => $prof['email']
                        ]);
                        echo "<!-- Professeur ajouté à la table professeurs: ID " . $prof['id'] . " -->";
                    }
                }
            } catch (PDOException $e) {
                // Ignorer les erreurs liées à la structure de la base de données
                echo "<!-- Erreur lors de la gestion des contraintes: " . htmlspecialchars($e->getMessage()) . " -->";
            }
        }

        // Vérifier si les colonnes existent dans la table affectations
        $stmt = $pdo->query("SHOW COLUMNS FROM affectations LIKE 'annee'");
        $anneeExists = $stmt->rowCount() > 0;

        $stmt = $pdo->query("SHOW COLUMNS FROM affectations LIKE 'semestre'");
        $semestreExists = $stmt->rowCount() > 0;

        $stmt = $pdo->query("SHOW COLUMNS FROM affectations LIKE 'date_debut'");
        $dateDebutExists = $stmt->rowCount() > 0;

        $stmt = $pdo->query("SHOW COLUMNS FROM affectations LIKE 'date_fin'");
        $dateFinExists = $stmt->rowCount() > 0;

        $stmt = $pdo->query("SHOW COLUMNS FROM affectations LIKE 'heures'");
        $heuresExists = $stmt->rowCount() > 0;

        $stmt = $pdo->query("SHOW COLUMNS FROM affectations LIKE 'utilisateur_id'");
        $utilisateurIdExists = $stmt->rowCount() > 0;

        // Vérifier si la colonne specialite_id existe
        $stmt = $pdo->query("SHOW COLUMNS FROM affectations LIKE 'specialite_id'");
        $specialiteIdExists = $stmt->rowCount() > 0;

        // Si la colonne specialite_id existe, vérifier si la table specialite existe
        if ($specialiteIdExists) {
            $stmt = $pdo->query("SHOW TABLES LIKE 'specialite'");
            $specialiteTableExists = $stmt->rowCount() > 0;

            if ($specialiteTableExists) {
                // Récupérer la première spécialité disponible
                $stmt = $pdo->query("SELECT id_specialite FROM specialite LIMIT 1");
                $defaultSpecialiteId = $stmt->fetchColumn();

                if (!$defaultSpecialiteId) {
                    // Si aucune spécialité n'existe, créer une spécialité par défaut
                    $pdo->exec("INSERT INTO specialite (nom_specialite) VALUES ('Spécialité par défaut')");
                    $defaultSpecialiteId = $pdo->lastInsertId();
                    echo "<!-- Spécialité par défaut créée avec ID: $defaultSpecialiteId -->";
                }
            } else {
                // Si la table specialite n'existe pas, créer la table
                $pdo->exec("
                    CREATE TABLE IF NOT EXISTS specialite (
                        id_specialite INT AUTO_INCREMENT PRIMARY KEY,
                        nom_specialite VARCHAR(255) NOT NULL
                    )
                ");

                // Insérer une spécialité par défaut
                $pdo->exec("INSERT INTO specialite (nom_specialite) VALUES ('Spécialité par défaut')");
                $defaultSpecialiteId = $pdo->lastInsertId();
                echo "<!-- Table specialite créée avec une spécialité par défaut, ID: $defaultSpecialiteId -->";
            }
        }

        // Construire la requête d'insertion en fonction des colonnes existantes
        $insertColumns = ["professeur_id"];
        $insertValues = [":professeur_id"];

        // Ajouter la colonne ue_id ou id_ue selon ce qui existe
        if ($ueIdExists) {
            $insertColumns[] = "ue_id";
            $insertValues[] = ":ue_id";
        } else if ($idUeExists) {
            $insertColumns[] = "id_ue";
            $insertValues[] = ":ue_id";
        } else {
            // Si aucune des colonnes n'existe, créer la colonne ue_id
            try {
                $pdo->exec("ALTER TABLE affectations ADD COLUMN ue_id INT NOT NULL AFTER professeur_id");
                $insertColumns[] = "ue_id";
                $insertValues[] = ":ue_id";
            } catch (PDOException $e) {
                throw new Exception("Erreur lors de la modification de la table: " . $e->getMessage());
            }
        }

        // Ajouter les autres colonnes si elles existent
        if ($anneeExists) {
            $insertColumns[] = "annee";
            $insertValues[] = ":annee";
        }

        if ($semestreExists) {
            $insertColumns[] = "semestre";
            $insertValues[] = ":semestre";
        }

        if ($dateDebutExists) {
            $insertColumns[] = "date_debut";
            $insertValues[] = ":date_debut";
        }

        if ($dateFinExists) {
            $insertColumns[] = "date_fin";
            $insertValues[] = ":date_fin";
        }

        if ($heuresExists) {
            $insertColumns[] = "heures";
            $insertValues[] = ":heures";
        }

        if ($utilisateurIdExists) {
            $insertColumns[] = "utilisateur_id";
            $insertValues[] = ":utilisateur_id";
        }

        // Ajouter la colonne specialite_id si elle existe
        if ($specialiteIdExists) {
            $insertColumns[] = "specialite_id";
            $insertValues[] = ":specialite_id";
        }

        // Construire la requête SQL
        $sql = "INSERT INTO affectations (" . implode(", ", $insertColumns) . ") VALUES (" . implode(", ", $insertValues) . ")";
        $stmt = $pdo->prepare($sql);

        // Préparer les données en fonction des colonnes existantes
        $data = [
            'professeur_id' => (int)$_POST['professeur_id'],
            'ue_id' => (int)$_POST['ue_id']
        ];

        // Ajouter les autres données si les colonnes existent
        if ($anneeExists) {
            $data['annee'] = (int)$_POST['annee'];
        }

        if ($semestreExists) {
            $data['semestre'] = (int)$_POST['semestre'];
        }

        if ($dateDebutExists) {
            $data['date_debut'] = $_POST['date_debut'];
        }

        if ($dateFinExists) {
            $data['date_fin'] = $_POST['date_fin'] ?? null;
        }

        if ($heuresExists) {
            $data['heures'] = (int)$_POST['heures'];
        }

        if ($utilisateurIdExists) {
            $data['utilisateur_id'] = $_SESSION['user_id'];
        }

        // Ajouter la valeur de specialite_id si la colonne existe
        if ($specialiteIdExists) {
            $data['specialite_id'] = $defaultSpecialiteId ?? 1; // Utiliser la spécialité par défaut ou 1 si non définie
        }

        if ($stmt->execute($data)) {
            $_SESSION['success'] = "Affectation ajoutée avec succès!";
            header("Location: affectation_ue.php");
            exit;
        }

    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// 7. Gestion de la suppression
if (isset($_GET['supprimer'])) {
    try {
        if (empty($_GET['csrf_token']) || !hash_equals($csrf_token, $_GET['csrf_token'])) {
            throw new Exception("Erreur de sécurité: Token invalide");
        }

        $id = (int)$_GET['supprimer'];
        $stmt = $pdo->prepare("DELETE FROM affectations WHERE id = ?");

        if ($stmt->execute([$id])) {
            $_SESSION['success'] = "Affectation supprimée avec succès!";
            header("Location: affectation_ue.php");
            exit;
        }

    } catch (Exception $e) {
        $error = "Erreur lors de la suppression: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion des Affectations UE</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/buttons/2.4.1/css/buttons.bootstrap5.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #1E90FF;
            --primary-magenta: #FF00FF;
            --blue-transparent: rgba(30, 144, 255, 0.3);
            --dark-bg: #0a192f;
        }

        body {
            background: linear-gradient(rgba(10, 25, 47, 0.85), rgba(108, 27, 145, 0.85)),
                       url('images/background.jpg') center/cover fixed;
            color: white;
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .sidebar {
            width: 250px;
            position: fixed;
            height: 100vh;
            background: rgba(44, 62, 80, 0.95);
            color: white;
            padding-top: 20px;
            backdrop-filter: blur(5px);
            border-right: 2px solid var(--primary-blue);
            box-shadow: 4px 0 15px var(--blue-transparent);
            animation: borderGlow 8s infinite alternate;
            z-index: 1000;
        }

        @keyframes borderGlow {
            0% { border-color: var(--primary-blue); }
            50% { border-color: var(--primary-magenta); }
            100% { border-color: var(--primary-blue); }
        }

        .sidebar .nav-link {
            color: white;
            padding: 12px 15px;
            margin: 8px 0;
            border-radius: 8px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .sidebar .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(18, 84, 151, 0.2), transparent);
            transition: 0.5s;
        }

        .sidebar .nav-link:hover {
            background: rgba(30, 144, 255, 0.1);
            transform: translateX(10px);
        }

        .sidebar .nav-link:hover::before {
            left: 100%;
        }

        .sidebar .nav-link.active {
            background: rgba(30, 144, 255, 0.2);
            border-left: 4px solid var(--primary-blue);
        }

        .content-area {
            margin-left: 270px;
            padding: 20px;
        }

        .card {
            margin-bottom: 20px;
            border-radius: 10px;
            background: rgba(10, 25, 47, 0.9);
            border: 2px solid var(--primary-blue);
            transition: all 0.3s ease;
            animation: cardBorderPulse 10s infinite;
            box-shadow: 0 5px 15px var(--blue-transparent);
        }

        @keyframes cardBorderPulse {
            0% { border-color: var(--primary-blue); box-shadow: 0 5px 15px var(--blue-transparent); }
            50% { border-color: var(--primary-magenta); box-shadow: 0 5px 20px rgba(255, 0, 255, 0.3); }
            100% { border-color: var(--primary-blue); box-shadow: 0 5px 15px var(--blue-transparent); }
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(255, 0, 255, 0.4) !important;
        }

        .card-header {
            background: linear-gradient(90deg, var(--primary-blue) 0%, var(--dark-bg) 100%);
            color: white;
            padding: 15px;
            border-radius: 10px 10px 0 0 !important;
            border-bottom: 2px solid var(--primary-magenta);
        }

        #affectationsTable {
            background: rgba(10, 25, 47, 0.9);
            border: 1px solid var(--primary-blue);
            color: white;
        }

        #affectationsTable thead {
            background: linear-gradient(90deg, var(--primary-blue) 0%, var(--dark-bg) 100%);
            border-bottom: 2px solid var(--primary-magenta);
        }

        #affectationsTable tbody tr:hover {
            background-color: rgba(30, 144, 255, 0.1) !important;
        }

        .form-control, .form-select {
            background-color: rgba(10, 25, 47, 0.7);
            color: white;
            border: 1px solid var(--primary-blue);
        }

        .form-control:focus, .form-select:focus {
            background-color: rgba(10, 25, 47, 0.9);
            color: white;
            border-color: var(--primary-magenta);
            box-shadow: 0 0 0 0.25rem rgba(255, 0, 255, 0.25);
        }

        .btn-primary {
            background-color: var(--primary-blue);
            border-color: var(--primary-blue);
        }

        .btn-primary:hover {
            background-color: #1a7fd9;
            border-color: #1a7fd9;
        }

        .alert {
            border-radius: 10px;
        }

        .export-buttons {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
        }

        .export-btn {
            padding: 10px 20px;
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .export-btn i {
            font-size: 1.1rem;
        }

        .export-btn-excel {
            background-color: #1D6F42;
            color: white;
            border: none;
        }

        .export-btn-excel:hover {
            background-color: #155a35;
            transform: translateY(-2px);
            box-shadow: 0 6px 8px rgba(0,0,0,0.15);
        }

        .export-btn-print {
            background-color: var(--primary-blue);
            color: white;
            border: none;
        }

        .export-btn-print:hover {
            background-color: #1a7fd9;
            transform: translateY(-2px);
            box-shadow: 0 6px 8px rgba(0,0,0,0.15);
        }

        .dt-buttons {
            margin-bottom: 15px;
        }

        .dt-button {
            background: var(--primary-blue) !important;
            color: white !important;
            border: none !important;
            border-radius: 4px !important;
            padding: 8px 16px !important;
            margin-right: 8px !important;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1) !important;
            transition: all 0.3s ease !important;
        }

        .dt-button:hover {
            background: #1a7fd9 !important;
            box-shadow: 0 4px 8px rgba(0,0,0,0.15) !important;
            transform: translateY(-2px) !important;
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="text-center mb-4">
            <img src="images/logo.png" alt="Logo" class="img-fluid mb-3" style="filter: drop-shadow(0 0 5px var(--primary-blue));">
            <h5 class="text-white" style="text-shadow: 0 0 10px var(--primary-blue));">
                Gestion Pédagogique
            </h5>
        </div>
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link active" href="affectation_ue.php">
                    <i class="fas fa-tasks me-2"></i> Affectations UE
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="professeurs.php">
                    <i class="fas fa-users-cog me-2"></i> Professeurs
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="unites_enseignement.php">
                    <i class="fas fa-book-open me-2"></i> Unités d'Enseignement
                </a>
            </li>
            <li class="nav-item mt-3">
                <a class="nav-link text-danger" href="logout.php">
                    <i class="fas fa-sign-out-alt me-2"></i> Déconnexion
                </a>
            </li>
        </ul>
    </div>

    <div class="content-area">
        <h1 class="mb-4 text-white"><i class="fas fa-tasks me-2"></i> Gestion des Affectations UE</h1>

        <?php if ($error): ?>
            <div class="alert alert-danger"><?= htmlspecialchars($error) ?></div>
        <?php endif; ?>

        <?php if ($success): ?>
            <div class="alert alert-success"><?= htmlspecialchars($success) ?></div>
        <?php endif; ?>

        <div class="card">
            <div class="card-header">
                <h5 class="m-0"><i class="fas fa-plus-circle me-2"></i> Nouvelle Affectation</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($csrf_token) ?>">

                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label">Professeur *</label>
                            <select class="form-select" name="professeur_id" required>
                                <option value="">Sélectionner...</option>
                                <?php foreach ($professeurs as $prof): ?>
                                    <option value="<?= $prof['id'] ?>"><?= htmlspecialchars($prof['nom_complet']) ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="col-md-6">
                            <label class="form-label">Unité d'Enseignement *</label>
                            <select class="form-select" name="ue_id" required>
                                <option value="">Sélectionner...</option>
                                <?php foreach ($unites_enseignement as $ue): ?>
                                    <option value="<?= $ue['id'] ?>"><?= htmlspecialchars($ue['nom_complet']) ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="col-md-3">
                            <label class="form-label">Année *</label>
                            <select class="form-select" name="annee" required>
                                <?php $currentYear = date('Y'); ?>
                                <?php for ($i = $currentYear; $i <= $currentYear + 2; $i++): ?>
                                    <option value="<?= $i ?>" <?= $i == $currentYear ? 'selected' : '' ?>><?= $i ?></option>
                                <?php endfor; ?>
                            </select>
                        </div>

                        <div class="col-md-3">
                            <label class="form-label">Semestre *</label>
                            <select class="form-select" name="semestre" required>
                                <option value="1">Semestre 1</option>
                                <option value="2">Semestre 2</option>
                            </select>
                        </div>

                        <div class="col-md-3">
                            <label class="form-label">Date début *</label>
                            <input type="date" class="form-control" name="date_debut" required value="<?= date('Y-m-d') ?>">
                        </div>

                        <div class="col-md-3">
                            <label class="form-label">Date fin</label>
                            <input type="date" class="form-control" name="date_fin">
                        </div>

                        <div class="col-md-2">
                            <label class="form-label">Heures *</label>
                            <input type="number" class="form-control" name="heures" required min="1" value="30">
                        </div>
                    </div>

                    <button type="submit" name="ajouter_affectation" class="btn btn-primary mt-3">
                        <i class="fas fa-save me-2"></i> Enregistrer
                    </button>
                </form>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="m-0"><i class="fas fa-list me-2"></i> Liste des Affectations</h5>
            </div>
            <div class="card-body">
                <div class="export-buttons mb-4">
                    <button id="export-excel" class="btn export-btn export-btn-excel">
                        <i class="fas fa-file-excel"></i> Exporter vers Excel
                    </button>
                    <button id="export-print" class="btn export-btn export-btn-print">
                        <i class="fas fa-print"></i> Imprimer
                    </button>
                </div>
                <table class="table table-hover" id="affectationsTable">
                    <thead>
                        <tr>
                            <th>Professeur</th>
                            <th>Unité d'Enseignement</th>
                            <th>Code UE</th>
                            <th>Année</th>
                            <th>Sem.</th>
                            <th>Date début</th>
                            <th>Date fin</th>
                            <th>Heures</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($affectations as $aff): ?>
                        <tr>
                            <td><?= htmlspecialchars($aff['professeur']) ?></td>
                            <td><?= htmlspecialchars($aff['unite_enseignement'] ?? '') ?></td>
                            <td><?= htmlspecialchars($aff['code_ue'] ?? '') ?></td>
                            <td><?= $aff['annee'] ?? date('Y') ?></td>
                            <td>S<?= $aff['semestre'] ?? '1' ?></td>
                            <td><?= $aff['date_debut_format'] ?? '' ?></td>
                            <td><?= $aff['date_fin_format'] ?? '-' ?></td>
                            <td><?= $aff['heures'] ?></td>
                            <td>
                                <a href="editer_affectation.php?id=<?= $aff['id'] ?>" class="btn btn-sm btn-primary">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="?supprimer=<?= $aff['id'] ?>&csrf_token=<?= htmlspecialchars($csrf_token) ?>"
                                   class="btn btn-sm btn-danger"
                                   onclick="return confirm('Confirmer la suppression?')">
                                    <i class="fas fa-trash-alt"></i>
                                </a>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.bootstrap5.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.html5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.print.min.js"></script>
    <script>
        $(document).ready(function() {
            $('#affectationsTable').DataTable({
                language: {
                    url: 'https://cdn.datatables.net/plug-ins/1.13.6/i18n/fr-FR.json'
                },
                order: [[5, 'desc']],
                dom: 'Bfrtip',
                buttons: [
                    {
                        extend: 'excel',
                        text: '<i class="fas fa-file-excel"></i> Excel',
                        title: 'Liste des Affectations UE',
                        exportOptions: {
                            columns: [0, 1, 2, 3, 4, 5, 6, 7]
                        },
                        className: 'btn-excel'
                    },
                    {
                        extend: 'print',
                        text: '<i class="fas fa-print"></i> Imprimer',
                        title: 'Liste des Affectations UE',
                        exportOptions: {
                            columns: [0, 1, 2, 3, 4, 5, 6, 7]
                        },
                        className: 'btn-print'
                    }
                ]
            });

            $('#export-excel').on('click', function() {
                $('#affectationsTable').DataTable().button('.buttons-excel').trigger();
            });

            $('#export-print').on('click', function() {
                $('#affectationsTable').DataTable().button('.buttons-print').trigger();
            });
        });
    </script>
</body>
</html>