<?php
require_once 'config.php';
session_start();

// Vérification des droits
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'chef_departement') {
    header("Location: login.php");
    exit;
}

$departement_id = $_SESSION['departement_id'];
$ues_vacantes = [];

try {
    $pdo = new PDO("mysql:host=".DB_HOST.";dbname=".DB_NAME, DB_USER, DB_PASS);

    // Récupérer les UE vacantes du département
    $query = "
        SELECT ue.*
        FROM unites_enseignement ue
        LEFT JOIN choix_professeurs cp ON ue.id = cp.ue_id AND cp.statut = 'valide'
        WHERE ue.departement_id = ?
        AND cp.ue_id IS NULL
        ORDER BY ue.semestre, ue.code_ue
    ";
    $stmt = $pdo->prepare($query);
    $stmt->execute([$departement_id]);
    $ues_vacantes = $stmt->fetchAll();

    // Validation des UE vacantes
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['valider_vacantes'])) {
        // Enregistrer la validation dans l'historique
        $stmt = $pdo->prepare("
            INSERT INTO validation_vacantes 
            (departement_id, date_validation, ues_vacantes)
            VALUES (?, NOW(), ?)
        ");
        $ues_ids = array_column($ues_vacantes, 'id');
        $stmt->execute([$departement_id, implode(',', $ues_ids)]);
        
        $success = "Liste des UE vacantes validée avec succès";
    }

} catch (PDOException $e) {
    $error = "Erreur: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UE Vacantes</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .table-vacante { background-color: #fff3cd; }
        .action-buttons { min-width: 200px; }
    </style>
</head>
<body>
    <?php include 'navbar_chef.php'; ?>
    
    <div class="container mt-4">
        <h2 class="mb-4">
            <i class="fas fa-box-open"></i> Unités d'Enseignement Vacantes
        </h2>

        <?php if (isset($success)): ?>
            <div class="alert alert-success"><?= $success ?></div>
        <?php endif; ?>

        <?php if (isset($error)): ?>
            <div class="alert alert-danger"><?= $error ?></div>
        <?php endif; ?>

        <div class="card">
            <div class="card-header">
                <h4>Liste des UE non attribuées</h4>
            </div>
            
            <div class="card-body">
                <?php if (empty($ues_vacantes)): ?>
                    <div class="alert alert-success">
                        Aucune UE vacante - Toutes les unités sont attribuées !
                    </div>
                <?php else: ?>
                    <form method="post">
                        <table class="table table-hover">
                            <thead class="table-warning">
                                <tr>
                                    <th>Code</th>
                                    <th>Intitulé</th>
                                    <th>Crédits</th>
                                    <th>Semestre</th>
                                    <th class="action-buttons">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($ues_vacantes as $ue): ?>
                                <tr class="table-vacante">
                                    <td><?= htmlspecialchars($ue['code_ue']) ?></td>
                                    <td><?= htmlspecialchars($ue['intitule']) ?></td>
                                    <td><?= htmlspecialchars($ue['credit']) ?></td>
                                    <td>S<?= htmlspecialchars($ue['semestre']) ?></td>
                                    <td>
                                        <div class="d-flex gap-2">
                                            <a href="affectation_manuelle.php?ue_id=<?= $ue['id'] ?>" 
                                               class="btn btn-sm btn-primary">
                                               <i class="fas fa-user-plus"></i> Affecter
                                            </a>
                                            <a href="details_ue.php?id=<?= $ue['id'] ?>" 
                                               class="btn btn-sm btn-info">
                                               <i class="fas fa-info-circle"></i> Détails
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                        
                        <div class="mt-4">
                            <button type="submit" name="valider_vacantes" 
                                    class="btn btn-lg btn-warning"
                                    onclick="return confirm('Confirmer la validation de ces UE comme vacantes ?')">
                                <i class="fas fa-check-circle"></i> Valider la liste
                            </button>
                        </div>
                    </form>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>