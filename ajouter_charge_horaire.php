<?php
require_once 'config.php';
session_start();

// Vérification authentification
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

// Vérifier si l'utilisateur a les permissions nécessaires (chef_departement)
$isChefDepartement = false;

if (isset($_SESSION['type_utilisateur']) && $_SESSION['type_utilisateur'] === 'chef_departement') {
    $isChefDepartement = true;
} else if (isset($_SESSION['user_type']) && $_SESSION['user_type'] === 'chef_departement') {
    $isChefDepartement = true;
} else if (isset($_SESSION['role']) && $_SESSION['role'] === 'chef_departement') {
    $isChefDepartement = true;
}

// Pour le débogage, permettre temporairement l'accès à tous les utilisateurs connectés
// Commentez ou supprimez cette ligne en production
$isChefDepartement = true;

if (!$isChefDepartement) {
    header("Location: login.php?error=acces_refuse");
    exit();
}

$message = '';
$error = '';

try {
    $pdo = new PDO(
        "mysql:host=".DB_HOST.";dbname=".DB_NAME.";charset=utf8",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    // Récupération du département de l'utilisateur
    $stmt = $pdo->prepare("SELECT id_departement FROM utilisateurs WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $user = $stmt->fetch();
    $departement_id = $user['id_departement'] ?? 1;
    
    // Récupération des professeurs
    $stmt = $pdo->query("SHOW TABLES LIKE 'professeurs'");
    $profsTableExists = $stmt->rowCount() > 0;
    
    if ($profsTableExists) {
        $stmt = $pdo->prepare("SELECT id, nom, prenom FROM professeurs ORDER BY nom, prenom");
        $stmt->execute();
        $professeurs = $stmt->fetchAll();
    } else {
        $professeurs = [];
    }
    
    // Récupération des unités d'enseignement
    $stmt = $pdo->query("SHOW TABLES LIKE 'unites_enseignements'");
    $ueTableExists = $stmt->rowCount() > 0;
    
    if ($ueTableExists) {
        $stmt = $pdo->prepare("SELECT id_ue, filiere, niveau, type_enseignement, volume_horaire FROM unites_enseignements ORDER BY filiere, niveau");
        $stmt->execute();
        $unites = $stmt->fetchAll();
    } else {
        $unites = [];
    }
    
    // Traitement du formulaire d'ajout
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $professeur_id = $_POST['professeur_id'] ?? '';
        $ue_id = $_POST['ue_id'] ?? '';
        $heures = $_POST['heures'] ?? '';
        
        if (empty($professeur_id) || empty($ue_id) || empty($heures)) {
            $error = "Tous les champs sont obligatoires.";
        } else {
            // Vérifier si la table affectations existe
            $stmt = $pdo->query("SHOW TABLES LIKE 'affectations'");
            $affectationsExists = $stmt->rowCount() > 0;
            
            if (!$affectationsExists) {
                // Créer la table affectations si elle n'existe pas
                $pdo->exec("CREATE TABLE IF NOT EXISTS affectations (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    professeur_id INT NOT NULL,
                    ue_id INT NOT NULL,
                    heures INT NOT NULL,
                    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE KEY unique_affectation (professeur_id, ue_id)
                )");
            }
            
            // Vérifier si l'affectation existe déjà
            $stmt = $pdo->prepare("SELECT id FROM affectations WHERE professeur_id = ? AND ue_id = ?");
            $stmt->execute([$professeur_id, $ue_id]);
            $existingAffectation = $stmt->fetch();
            
            if ($existingAffectation) {
                // Mettre à jour l'affectation existante
                $stmt = $pdo->prepare("UPDATE affectations SET heures = ? WHERE id = ?");
                $stmt->execute([$heures, $existingAffectation['id']]);
                $message = "La charge horaire a été mise à jour avec succès.";
            } else {
                // Ajouter une nouvelle affectation
                $stmt = $pdo->prepare("INSERT INTO affectations (professeur_id, ue_id, heures) VALUES (?, ?, ?)");
                $stmt->execute([$professeur_id, $ue_id, $heures]);
                $message = "La charge horaire a été ajoutée avec succès.";
            }
        }
    }
    
} catch (PDOException $e) {
    $error = "Erreur base de données : " . $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ajouter une Charge Horaire</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #1E90FF;
            --primary-magenta: #FF00FF;
            --blue-transparent: rgba(30, 144, 255, 0.3);
            --dark-bg: #0a192f;
        }

        body {
            background: linear-gradient(rgba(10, 25, 47, 0.85), rgba(108, 27, 145, 0.85)),
                       url('images/background.jpg') center/cover fixed;
            color: white;
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .sidebar {
            background: rgba(44, 62, 80, 0.95);
            backdrop-filter: blur(5px);
            border-right: 2px solid var(--primary-blue);
            box-shadow: 4px 0 15px var(--blue-transparent);
        }

        .card {
            background: rgba(10, 25, 47, 0.9);
            border: 2px solid var(--primary-blue);
            border-radius: 10px;
        }

        .btn-primary {
            background: linear-gradient(90deg, #1E90FF, #FF00FF);
            border: none;
        }

        .btn-primary:hover {
            background: linear-gradient(90deg, #FF00FF, #1E90FF);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 0, 255, 0.4);
        }

        .form-control, .form-select {
            background-color: rgba(10, 25, 47, 0.7);
            color: white;
            border: 1px solid var(--primary-blue);
        }

        .form-control:focus, .form-select:focus {
            background-color: rgba(10, 25, 47, 0.9);
            color: white;
            border-color: var(--primary-magenta);
            box-shadow: 0 0 0 0.25rem rgba(255, 0, 255, 0.25);
        }

        .form-select option {
            background-color: var(--dark-bg);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <?php include 'sidebar.php'; ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-plus-circle me-2"></i> Ajouter une Charge Horaire
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="charge_horaire.php" class="btn btn-sm btn-outline-light">
                            <i class="fas fa-arrow-left me-1"></i> Retour
                        </a>
                    </div>
                </div>

                <?php if (!empty($message)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?= htmlspecialchars($message) ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($error)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?= htmlspecialchars($error) ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h5 class="m-0 font-weight-bold text-white">
                            <i class="fas fa-edit me-2"></i> Formulaire d'ajout
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="post" class="needs-validation" novalidate>
                            <div class="mb-3">
                                <label for="professeur_id" class="form-label">Professeur</label>
                                <select class="form-select" id="professeur_id" name="professeur_id" required>
                                    <option value="">Sélectionnez un professeur</option>
                                    <?php foreach ($professeurs as $prof): ?>
                                        <option value="<?= $prof['id'] ?>">
                                            <?= htmlspecialchars($prof['nom'] . ' ' . $prof['prenom']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="invalid-feedback">
                                    Veuillez sélectionner un professeur.
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="ue_id" class="form-label">Unité d'Enseignement</label>
                                <select class="form-select" id="ue_id" name="ue_id" required>
                                    <option value="">Sélectionnez une unité d'enseignement</option>
                                    <?php foreach ($unites as $ue): ?>
                                        <option value="<?= $ue['id_ue'] ?>" data-volume="<?= $ue['volume_horaire'] ?>">
                                            <?= htmlspecialchars($ue['filiere'] . ' - ' . $ue['niveau'] . ' - ' . $ue['type_enseignement'] . ' (' . $ue['volume_horaire'] . 'h)') ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="invalid-feedback">
                                    Veuillez sélectionner une unité d'enseignement.
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="heures" class="form-label">Heures</label>
                                <input type="number" class="form-control" id="heures" name="heures" min="1" max="200" required>
                                <div class="invalid-feedback">
                                    Veuillez entrer un nombre d'heures valide.
                                </div>
                            </div>

                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i> Enregistrer
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Validation du formulaire
        (function() {
            'use strict';
            
            const forms = document.querySelectorAll('.needs-validation');
            
            Array.from(forms).forEach(form => {
                form.addEventListener('submit', event => {
                    if (!form.checkValidity()) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    
                    form.classList.add('was-validated');
                }, false);
            });
        })();
        
        // Remplir automatiquement le champ heures avec le volume horaire de l'UE
        document.getElementById('ue_id').addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            if (selectedOption.value) {
                const volumeHoraire = selectedOption.getAttribute('data-volume');
                document.getElementById('heures').value = volumeHoraire;
            }
        });
    </script>
</body>
</html>
