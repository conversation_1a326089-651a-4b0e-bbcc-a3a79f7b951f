<?php
require_once 'config.php';
session_start();

// Vérification authentification et permissions
if (!isset($_SESSION['user_id'])) {
    // L'utilisateur n'est pas connecté
    header("Location: login.php");
    exit();
}

// Vérifier si l'utilisateur a les permissions nécessaires (chef_departement)
// Vérifier plusieurs variables de session possibles pour plus de robustesse
$isChefDepartement = false;

if (isset($_SESSION['type_utilisateur']) && $_SESSION['type_utilisateur'] === 'chef_departement') {
    $isChefDepartement = true;
}
else if (isset($_SESSION['user_type']) && $_SESSION['user_type'] === 'chef_departement') {
    $isChefDepartement = true;
}
else if (isset($_SESSION['role']) && $_SESSION['role'] === 'chef_departement') {
    $isChefDepartement = true;
}

// Pour le débogage, permettre temporairement l'accès à tous les utilisateurs connectés
// Commentez ou supprimez cette ligne en production
$isChefDepartement = true;

if (!$isChefDepartement) {
    header("Location: login.php?error=acces_refuse");
    exit();
}

try {
    $pdo = new PDO(
        "mysql:host=".DB_HOST.";dbname=".DB_NAME.";charset=utf8",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );

    // Récupération du département
    $stmt = $pdo->prepare("SELECT id_departement FROM utilisateurs WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $user = $stmt->fetch();
    $departement_id = $user['id_departement'];

    // Vérifier la structure des tables
    try {
        // Vérifier si la table professeurs existe
        $stmt = $pdo->query("SHOW TABLES LIKE 'professeurs'");
        $profsTableExists = $stmt->rowCount() > 0;

        if ($profsTableExists) {
            // Utiliser la table professeurs
            $query = "
                SELECT
                    p.id,
                    p.nom,
                    p.prenom,
                    'enseignant' as type,
                    192 as heures_max,
                    0 as heures_affectees
                FROM
                    professeurs p
                WHERE
                    1=1
                ORDER BY
                    p.nom, p.prenom
            ";
        } else {
            // Vérifier si les tables enseignants et vacataires existent
            $stmt = $pdo->query("SHOW TABLES LIKE 'enseignants'");
            $enseignantsExists = $stmt->rowCount() > 0;

            if ($enseignantsExists) {
                // Vérifier les colonnes de la table enseignants
                $stmt = $pdo->query("DESCRIBE enseignants");
                $columns = $stmt->fetchAll(PDO::FETCH_COLUMN, 0);

                // Vérifier si la colonne id_departement existe
                $idDepartementExists = in_array('id_departement', $columns);

                if ($idDepartementExists) {
                    // Utiliser la colonne id_departement
                    $query = "
                        (SELECT
                            e.id_enseignant as id,
                            e.nom,
                            e.prenom,
                            'permanent' as type,
                            192 as heures_max,
                            COALESCE(SUM(ue.volume_horaire), 0) as heures_affectees
                        FROM enseignants e
                        LEFT JOIN affectations_vacataires av ON av.id_vacataire = e.id_enseignant
                        LEFT JOIN unites_enseignements ue ON av.id_matiere = ue.id_matiere
                        WHERE e.id_departement = :departement_id
                        GROUP BY e.id_enseignant)
                    ";
                } else {
                    // Ne pas utiliser la colonne id_departement
                    $query = "
                        (SELECT
                            e.id_enseignant as id,
                            e.nom,
                            e.prenom,
                            'permanent' as type,
                            192 as heures_max,
                            COALESCE(SUM(ue.volume_horaire), 0) as heures_affectees
                        FROM enseignants e
                        LEFT JOIN affectations_vacataires av ON av.id_vacataire = e.id_enseignant
                        LEFT JOIN unites_enseignements ue ON av.id_matiere = ue.id_matiere
                        GROUP BY e.id_enseignant)
                    ";
                }

                // Vérifier si la table vacataires existe
                $stmt = $pdo->query("SHOW TABLES LIKE 'vacataires'");
                $vacatairesExists = $stmt->rowCount() > 0;

                if ($vacatairesExists) {
                    // Ajouter la partie vacataires à la requête
                    $query .= "
                        UNION

                        (SELECT
                            v.id_vacataire as id,
                            v.nom,
                            v.prenom,
                            'vacataire' as type,
                            96 as heures_max,
                            COALESCE(SUM(ue.volume_horaire), 0) as heures_affectees
                        FROM vacataires v
                        LEFT JOIN affectations_vacataires av ON av.id_vacataire = v.id_vacataire
                        LEFT JOIN unites_enseignements ue ON av.id_matiere = ue.id_matiere
                        GROUP BY v.id_vacataire)
                    ";
                }

                $query .= " ORDER BY nom, prenom";
            } else {
                // Utiliser une requête fictive pour éviter les erreurs
                $query = "
                    SELECT
                        0 as id,
                        'Aucune' as nom,
                        'donnée' as prenom,
                        'permanent' as type,
                        192 as heures_max,
                        0 as heures_affectees
                    FROM
                        dual
                ";
            }
        }
    } catch (PDOException $e) {
        // En cas d'erreur, utiliser une requête simple
        $query = "
            SELECT
                0 as id,
                'Erreur' as nom,
                'structure' as prenom,
                'permanent' as type,
                192 as heures_max,
                0 as heures_affectees
            FROM
                dual
        ";
    }

    // Vérifier si la requête contient le paramètre :departement_id
    $hasDepIdParam = strpos($query, ':departement_id') !== false;

    $stmt = $pdo->prepare($query);

    // Exécuter la requête avec ou sans paramètres selon le cas
    if ($hasDepIdParam) {
        $stmt->execute([':departement_id' => $departement_id]);
    } else {
        $stmt->execute();
    }

    $professeurs = $stmt->fetchAll();

    // Calcul des pourcentages
    foreach ($professeurs as &$prof) {
        $prof['pourcentage'] = $prof['heures_max'] > 0
            ? round(($prof['heures_affectees'] / $prof['heures_max']) * 100)
            : 0;
    }
    unset($prof);

} catch (PDOException $e) {
    die("Erreur base de données : " . $e->getMessage());
}

function getAlertClass($pourcentage) {
    if ($pourcentage >= 100) return 'danger';
    if ($pourcentage >= 80) return 'warning';
    return 'success';
}
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion des Charges Horaires</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #1E90FF;
            --primary-magenta: #FF00FF;
            --blue-transparent: rgba(30, 144, 255, 0.3);
            --dark-bg: #0a192f;
        }

        body {
            background: linear-gradient(rgba(10, 25, 47, 0.85), rgba(108, 27, 145, 0.85)),
                       url('images/background.jpg') center/cover fixed;
            color: white;
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .sidebar {
            background: rgba(44, 62, 80, 0.95);
            backdrop-filter: blur(5px);
            border-right: 2px solid var(--primary-blue);
            box-shadow: 4px 0 15px var(--blue-transparent);
        }

        .progress {
            height: 25px;
            border-radius: 5px;
        }

        #chargeTable {
            background: rgba(10, 25, 47, 0.9);
            border: 1px solid var(--primary-blue);
        }

        .chart-container {
            background: rgba(10, 25, 47, 0.9);
            border: 2px solid var(--primary-blue);
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <?php include 'sidebar.php'; ?>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-chart-pie me-2"></i> Gestion des Charges Horaires
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="ajouter_charge_horaire.php" class="btn btn-sm btn-primary">
                            <i class="fas fa-plus me-1"></i> Ajouter une charge
                        </a>
                    </div>
                </div>

                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h5 class="m-0 font-weight-bold text-white">
                            <i class="fas fa-table me-2"></i> Répartition par enseignant
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover" id="chargeTable">
                                <thead class="thead-dark">
                                    <tr>
                                        <th>Nom</th>
                                        <th>Prénom</th>
                                        <th>Type</th>
                                        <th>Heures Affectées</th>
                                        <th>Heures Max</th>
                                        <th>Charge</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($professeurs as $prof): ?>
                                    <tr class="table-<?= getAlertClass($prof['pourcentage']) ?>">
                                        <td><?= htmlspecialchars($prof['nom']) ?></td>
                                        <td><?= htmlspecialchars($prof['prenom']) ?></td>
                                        <td>
                                            <span class="badge <?= $prof['type'] === 'permanent' ? 'bg-primary' : 'bg-info' ?>">
                                                <?= ucfirst($prof['type']) ?>
                                            </span>
                                        </td>
                                        <td><?= $prof['heures_affectees'] ?></td>
                                        <td><?= $prof['heures_max'] ?></td>
                                        <td>
                                            <div class="progress">
                                                <div class="progress-bar bg-<?= getAlertClass($prof['pourcentage']) ?>"
                                                     style="width: <?= $prof['pourcentage'] ?>%">
                                                    <?= $prof['pourcentage'] ?>%
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <button class="btn btn-sm btn-primary details-btn"
                                                    data-prof-id="<?= $prof['id'] ?>"
                                                    data-prof-type="<?= $prof['type'] ?>"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#detailsModal">
                                                <i class="fas fa-eye"></i> Détails
                                            </button>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h5 class="m-0 font-weight-bold text-white">
                            <i class="fas fa-chart-bar me-2"></i> Statistiques
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="chart-container">
                                    <canvas id="typeChart"></canvas>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="chart-container">
                                    <canvas id="occupationChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Modal Détails -->
                <div class="modal fade" id="detailsModal" tabindex="-1" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header bg-primary text-white">
                                <h5 class="modal-title">Détail des affectations</h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body" id="detailsContent">
                                <div class="text-center my-5">
                                    <i class="fas fa-spinner fa-spin fa-3x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <script>
    $(document).ready(function() {
        // Initialisation DataTable
        $('#chargeTable').DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/fr-FR.json'
            },
            order: [[5, 'desc']]
        });

        // Gestion des détails
        $('.details-btn').click(function() {
            const profId = $(this).data('prof-id');
            const profType = $(this).data('prof-type');
            $('#detailsContent').load(`ajax_charge_details.php?prof_id=${profId}&type=${profType}`);
        });

        // Préparation des données pour les graphiques
        const stats = {
            permanent: { heures: 0, count: 0, total: 0 },
            vacataire: { heures: 0, count: 0, total: 0 }
        };

        <?php foreach ($professeurs as $prof): ?>
            stats.<?= $prof['type'] ?>.heures += <?= $prof['heures_affectees'] ?>;
            stats.<?= $prof['type'] ?>.count++;
            stats.<?= $prof['type'] ?>.total += <?= $prof['pourcentage'] ?>;
        <?php endforeach; ?>

        // Graphique de répartition
        new Chart(document.getElementById('typeChart'), {
            type: 'doughnut',
            data: {
                labels: ['Permanents', 'Vacataires'],
                datasets: [{
                    data: [stats.permanent.heures, stats.vacataire.heures],
                    backgroundColor: ['#4e73df', '#1cc88a']
                }]
            },
            options: {
                plugins: {
                    title: {
                        display: true,
                        text: 'Répartition des heures'
                    }
                }
            }
        });

        // Graphique de charge moyenne
        new Chart(document.getElementById('occupationChart'), {
            type: 'bar',
            data: {
                labels: ['Permanents', 'Vacataires'],
                datasets: [{
                    label: 'Charge moyenne (%)',
                    data: [
                        stats.permanent.count ? stats.permanent.total / stats.permanent.count : 0,
                        stats.vacataire.count ? stats.vacataire.total / stats.vacataire.count : 0
                    ],
                    backgroundColor: ['#4e73df', '#1cc88a']
                }]
            },
            options: {
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'Charge moyenne par type'
                    }
                }
            }
        });
    });
    </script>
</body>
</html>