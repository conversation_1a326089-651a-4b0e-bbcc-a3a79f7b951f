<?php
// Script pour vérifier la structure de la table professeurs
require_once 'config.php';

// Afficher les erreurs pour le débogage
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Structure de la table professeurs</h1>";

try {
    // Connexion à la base de données
    $pdo = new PDO(
        "mysql:host=".DB_HOST.";dbname=".DB_NAME.";charset=utf8",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    // Vérifier si la table professeurs existe
    $stmt = $pdo->query("SHOW TABLES LIKE 'professeurs'");
    $tableExists = $stmt->rowCount() > 0;
    
    if ($tableExists) {
        echo "<p style='color:green'>La table 'professeurs' existe dans la base de données.</p>";
        
        // Afficher la structure de la table
        $stmt = $pdo->query("DESCRIBE professeurs");
        $columns = $stmt->fetchAll();
        
        echo "<h2>Colonnes de la table professeurs</h2>";
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>Nom</th><th>Type</th><th>Null</th><th>Clé</th><th>Défaut</th><th>Extra</th></tr>";
        
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Default'] ?? 'NULL') . "</td>";
            echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
        
        // Vérifier si les colonnes heures_max et heures_vacataire existent
        $hasHeuresMaxColumn = false;
        $hasHeuresVacataireColumn = false;
        $hasTypeColumn = false;
        
        foreach ($columns as $column) {
            if ($column['Field'] === 'heures_max') {
                $hasHeuresMaxColumn = true;
            }
            if ($column['Field'] === 'heures_vacataire') {
                $hasHeuresVacataireColumn = true;
            }
            if ($column['Field'] === 'type') {
                $hasTypeColumn = true;
            }
        }
        
        if ($hasHeuresMaxColumn) {
            echo "<p style='color:green'>La colonne 'heures_max' existe dans la table 'professeurs'.</p>";
        } else {
            echo "<p style='color:red'>La colonne 'heures_max' n'existe pas dans la table 'professeurs'.</p>";
        }
        
        if ($hasHeuresVacataireColumn) {
            echo "<p style='color:green'>La colonne 'heures_vacataire' existe dans la table 'professeurs'.</p>";
        } else {
            echo "<p style='color:red'>La colonne 'heures_vacataire' n'existe pas dans la table 'professeurs'.</p>";
        }
        
        if ($hasTypeColumn) {
            echo "<p style='color:green'>La colonne 'type' existe dans la table 'professeurs'.</p>";
        } else {
            echo "<p style='color:red'>La colonne 'type' n'existe pas dans la table 'professeurs'.</p>";
        }
        
        // Proposer d'ajouter les colonnes manquantes
        $columnsToAdd = [];
        
        if (!$hasHeuresMaxColumn) {
            $columnsToAdd[] = "heures_max INT DEFAULT 192";
        }
        
        if (!$hasHeuresVacataireColumn) {
            $columnsToAdd[] = "heures_vacataire INT DEFAULT 96";
        }
        
        if (!$hasTypeColumn) {
            $columnsToAdd[] = "type VARCHAR(20) DEFAULT 'permanent'";
        }
        
        if (!empty($columnsToAdd)) {
            echo "<form method='post'>";
            echo "<input type='hidden' name='action' value='add_columns'>";
            echo "<button type='submit' style='padding: 10px 20px; background-color: #28a745; color: white; border: none; border-radius: 5px; cursor: pointer;'>Ajouter les colonnes manquantes</button>";
            echo "</form>";
        }
        
        // Afficher quelques données de la table
        $stmt = $pdo->query("SELECT * FROM professeurs LIMIT 5");
        $data = $stmt->fetchAll();
        
        if (!empty($data)) {
            echo "<h2>Exemples de données dans la table professeurs</h2>";
            echo "<pre>";
            print_r($data);
            echo "</pre>";
        } else {
            echo "<p>La table 'professeurs' est vide.</p>";
        }
        
    } else {
        echo "<p style='color:red'>La table 'professeurs' n'existe pas dans la base de données.</p>";
    }
    
    // Traiter les actions
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
        if ($_POST['action'] === 'add_columns') {
            try {
                // Ajouter les colonnes manquantes
                foreach ($columnsToAdd as $columnDefinition) {
                    $pdo->exec("ALTER TABLE professeurs ADD COLUMN $columnDefinition");
                    echo "<p style='color:green'>La colonne a été ajoutée à la table 'professeurs' avec succès.</p>";
                }
                
                // Mettre à jour les valeurs par défaut
                if (!$hasTypeColumn) {
                    $pdo->exec("UPDATE professeurs SET type = 'permanent'");
                }
                
                if (!$hasHeuresMaxColumn) {
                    $pdo->exec("UPDATE professeurs SET heures_max = 192");
                }
                
                if (!$hasHeuresVacataireColumn) {
                    $pdo->exec("UPDATE professeurs SET heures_vacataire = 96");
                }
                
                echo "<p>Veuillez rafraîchir la page pour voir les changements.</p>";
            } catch (PDOException $e) {
                echo "<p style='color:red'>Erreur lors de l'ajout des colonnes: " . $e->getMessage() . "</p>";
            }
        }
    }
    
    // Ajouter un lien vers charge_horaire.php
    echo "<p><a href='charge_horaire.php' style='padding: 10px 20px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px;'>Retour à la page des charges horaires</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color:red'>Erreur de connexion à la base de données: " . $e->getMessage() . "</p>";
}
?>
