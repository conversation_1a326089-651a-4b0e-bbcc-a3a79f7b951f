<?php
require_once 'config.php';

try {
    $pdo = new PDO(
        "mysql:host=".DB_HOST.";dbname=".DB_NAME.";charset=utf8",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );

    // Vérifier si la table professeurs existe
    $result = $pdo->query("SHOW TABLES LIKE 'professeurs'");
    if ($result->rowCount() > 0) {
        echo "<h2>La table 'professeurs' existe</h2>";
        
        // Afficher la structure de la table
        $result = $pdo->query("DESCRIBE professeurs");
        echo "<h3>Structure de la table 'professeurs'</h3>";
        echo "<table border='1'>";
        echo "<tr><th>Champ</th><th>Type</th><th>Null</th><th>Clé</th><th>Défaut</th><th>Extra</th></tr>";
        while ($row = $result->fetch()) {
            echo "<tr>";
            echo "<td>" . $row['Field'] . "</td>";
            echo "<td>" . $row['Type'] . "</td>";
            echo "<td>" . $row['Null'] . "</td>";
            echo "<td>" . $row['Key'] . "</td>";
            echo "<td>" . $row['Default'] . "</td>";
            echo "<td>" . $row['Extra'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<h2>La table 'professeurs' n'existe pas</h2>";
        echo "<p>Voici le SQL pour créer la table :</p>";
        echo "<pre>
CREATE TABLE professeurs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(50) NOT NULL,
    prenom VARCHAR(50) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    telephone VARCHAR(20),
    type ENUM('permanent', 'vacataire') NOT NULL DEFAULT 'permanent',
    id_departement INT,
    id_specialite INT,
    date_creation DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (id_departement) REFERENCES departement(id_departement),
    FOREIGN KEY (id_specialite) REFERENCES specialite(id_specialite)
);
        </pre>";
    }

    // Vérifier si la table utilisateurs existe
    $result = $pdo->query("SHOW TABLES LIKE 'utilisateurs'");
    if ($result->rowCount() > 0) {
        echo "<h2>La table 'utilisateurs' existe</h2>";
        
        // Afficher la structure de la table
        $result = $pdo->query("DESCRIBE utilisateurs");
        echo "<h3>Structure de la table 'utilisateurs'</h3>";
        echo "<table border='1'>";
        echo "<tr><th>Champ</th><th>Type</th><th>Null</th><th>Clé</th><th>Défaut</th><th>Extra</th></tr>";
        while ($row = $result->fetch()) {
            echo "<tr>";
            echo "<td>" . $row['Field'] . "</td>";
            echo "<td>" . $row['Type'] . "</td>";
            echo "<td>" . $row['Null'] . "</td>";
            echo "<td>" . $row['Key'] . "</td>";
            echo "<td>" . $row['Default'] . "</td>";
            echo "<td>" . $row['Extra'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<h2>La table 'utilisateurs' n'existe pas</h2>";
    }
} catch(PDOException $e) {
    echo "<h2>Erreur</h2>";
    echo "<p>" . $e->getMessage() . "</p>";
}
?>
