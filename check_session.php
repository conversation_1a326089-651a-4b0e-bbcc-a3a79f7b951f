<?php
// Inclure le fichier de configuration
require_once 'config.php';

// Démarrer la session
session_start();

// Afficher les erreurs pour le débogage
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Vérification de la session</h1>";

// Afficher les informations de session
echo "<h2>Informations de session</h2>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

// Vérifier si l'utilisateur est connecté
if (isset($_SESSION['user_id'])) {
    echo "<p>Vous êtes connecté avec l'ID utilisateur: " . $_SESSION['user_id'] . "</p>";
    
    if (isset($_SESSION['user_type'])) {
        echo "<p>Type d'utilisateur: " . $_SESSION['user_type'] . "</p>";
        
        if ($_SESSION['user_type'] === 'admin') {
            echo "<p class='success'>Vous avez les droits d'administrateur. Vous pouvez accéder à la page de gestion des chefs de département.</p>";
        } else {
            echo "<p class='error'>Vous n'avez pas les droits d'administrateur. Vous ne pouvez pas accéder à la page de gestion des chefs de département.</p>";
        }
    } else {
        echo "<p class='error'>Le type d'utilisateur n'est pas défini dans la session.</p>";
    }
} else {
    echo "<p class='error'>Vous n'êtes pas connecté.</p>";
}

// Formulaire pour définir la session admin (pour les tests)
echo "<h2>Définir la session admin (pour les tests)</h2>";
echo "<form method='post'>";
echo "<input type='hidden' name='set_admin' value='1'>";
echo "<button type='submit'>Définir comme admin</button>";
echo "</form>";

// Traiter le formulaire
if (isset($_POST['set_admin'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['user_type'] = 'admin';
    echo "<p class='success'>Session définie comme admin. Rafraîchissez la page pour voir les changements.</p>";
    echo "<script>window.location.reload();</script>";
}

// Lien vers la page de gestion des chefs de département
echo "<h2>Liens utiles</h2>";
echo "<ul>";
echo "<li><a href='gestion_chef_departement.php'>Page de gestion des chefs de département</a></li>";
echo "<li><a href='test_insert.php'>Page de test d'insertion</a></li>";
echo "<li><a href='check_table.php'>Vérification de la structure de la table</a></li>";
echo "</ul>";
?>

<style>
    body {
        font-family: Arial, sans-serif;
        margin: 20px;
    }
    
    h1, h2 {
        color: #333;
    }
    
    pre {
        background-color: #f5f5f5;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 5px;
        overflow: auto;
    }
    
    .success {
        color: green;
        font-weight: bold;
    }
    
    .error {
        color: red;
        font-weight: bold;
    }
    
    form {
        margin: 20px 0;
    }
    
    button {
        background-color: #4CAF50;
        color: white;
        padding: 10px 15px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    }
    
    button:hover {
        background-color: #45a049;
    }
    
    ul {
        margin-top: 20px;
    }
    
    li {
        margin-bottom: 10px;
    }
    
    a {
        color: #0066cc;
        text-decoration: none;
    }
    
    a:hover {
        text-decoration: underline;
    }
</style>
