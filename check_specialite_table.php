<?php
// Activer l'affichage des erreurs pour le débogage
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Inclure le fichier de configuration
require_once 'config.php';

echo "<h1>Vérification de la table specialite</h1>";

try {
    // Connexion à la base de données
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    echo "<p>Connexion à la base de données réussie</p>";
    
    // Vérifier si la table specialite existe
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<h2>Tables disponibles dans la base de données</h2>";
    echo "<ul>";
    foreach ($tables as $table) {
        echo "<li>$table</li>";
    }
    echo "</ul>";
    
    if (in_array('specialite', $tables)) {
        echo "<p style='color:green;'>✅ La table specialite existe.</p>";
        
        // Afficher la structure de la table specialite
        $columns = $pdo->query("DESCRIBE specialite")->fetchAll();
        
        echo "<h2>Structure de la table specialite</h2>";
        echo "<table border='1'>";
        echo "<tr><th>Champ</th><th>Type</th><th>Null</th><th>Clé</th><th>Défaut</th><th>Extra</th></tr>";
        
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . $column['Default'] . "</td>";
            echo "<td>" . $column['Extra'] . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
        
        // Vérifier si les colonnes nécessaires existent
        $has_id_specialite = false;
        $has_nom_specialite = false;
        $has_id_departement = false;
        
        foreach ($columns as $column) {
            if ($column['Field'] === 'id_specialite') $has_id_specialite = true;
            if ($column['Field'] === 'nom_specialite') $has_nom_specialite = true;
            if ($column['Field'] === 'id_departement') $has_id_departement = true;
        }
        
        if ($has_id_specialite) {
            echo "<p style='color:green;'>✅ La colonne id_specialite existe.</p>";
        } else {
            echo "<p style='color:red;'>❌ La colonne id_specialite n'existe pas.</p>";
        }
        
        if ($has_nom_specialite) {
            echo "<p style='color:green;'>✅ La colonne nom_specialite existe.</p>";
        } else {
            echo "<p style='color:red;'>❌ La colonne nom_specialite n'existe pas.</p>";
        }
        
        if ($has_id_departement) {
            echo "<p style='color:green;'>✅ La colonne id_departement existe.</p>";
        } else {
            echo "<p style='color:red;'>❌ La colonne id_departement n'existe pas.</p>";
        }
        
        // Afficher les données de la table specialite
        $specialites = $pdo->query("SELECT * FROM specialite")->fetchAll();
        
        echo "<h2>Données de la table specialite</h2>";
        
        if (count($specialites) > 0) {
            echo "<table border='1'>";
            echo "<tr>";
            foreach (array_keys($specialites[0]) as $header) {
                echo "<th>$header</th>";
            }
            echo "</tr>";
            
            foreach ($specialites as $specialite) {
                echo "<tr>";
                foreach ($specialite as $value) {
                    echo "<td>" . ($value !== null ? htmlspecialchars($value) : "NULL") . "</td>";
                }
                echo "</tr>";
            }
            
            echo "</table>";
        } else {
            echo "<p>Aucune donnée dans la table specialite.</p>";
            
            // Proposer de créer des données de test
            echo "<h3>Créer des données de test</h3>";
            echo "<form method='post'>";
            echo "<input type='hidden' name='action' value='create_test_data'>";
            echo "<button type='submit'>Créer des données de test</button>";
            echo "</form>";
        }
    } else {
        echo "<p style='color:red;'>❌ La table specialite n'existe pas.</p>";
        
        // Proposer de créer la table
        echo "<h3>Créer la table specialite</h3>";
        echo "<form method='post'>";
        echo "<input type='hidden' name='action' value='create_table'>";
        echo "<button type='submit'>Créer la table specialite</button>";
        echo "</form>";
    }
    
    // Traitement des actions
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (isset($_POST['action'])) {
            if ($_POST['action'] === 'create_table') {
                // Créer la table specialite
                $pdo->exec("
                    CREATE TABLE IF NOT EXISTS specialite (
                        id_specialite INT AUTO_INCREMENT PRIMARY KEY,
                        nom_specialite VARCHAR(255) NOT NULL,
                        id_departement INT NOT NULL,
                        INDEX (id_departement)
                    )
                ");
                
                echo "<p style='color:green;'>✅ Table specialite créée avec succès.</p>";
                echo "<p>Veuillez rafraîchir la page pour voir les changements.</p>";
            } elseif ($_POST['action'] === 'create_test_data') {
                // Créer des données de test
                $data = [
                    // Département 1: Informatique/Mathématiques
                    ['Développement logiciel', 1],
                    ['Intelligence Artificielle', 1],
                    ['Mathématiques Appliquées', 1],
                    ['Développement Web', 1],
                    ['Base de Données', 1],
                    ['Réseaux', 1],
                    
                    // Département 2: Physique
                    ['Physique Fondamentale', 2],
                    ['Physique Appliquée', 2],
                    ['Électronique', 2],
                    ['Physique Nucléaire', 2],
                    
                    // Département 3: Chimie
                    ['Chimie Organique', 3],
                    ['Chimie Inorganique', 3],
                    ['Biochimie', 3],
                    
                    // Département 4: Biologie
                    ['Microbiologie', 4],
                    ['Génétique', 4],
                    ['Écologie', 4]
                ];
                
                $stmt = $pdo->prepare("INSERT INTO specialite (nom_specialite, id_departement) VALUES (?, ?)");
                
                foreach ($data as $item) {
                    $stmt->execute($item);
                }
                
                echo "<p style='color:green;'>✅ Données de test créées avec succès.</p>";
                echo "<p>Veuillez rafraîchir la page pour voir les changements.</p>";
            }
        }
    }
    
} catch (PDOException $e) {
    echo "<p style='color:red;'>Erreur de connexion à la base de données: " . $e->getMessage() . "</p>";
}
?>

<style>
    body {
        font-family: Arial, sans-serif;
        margin: 20px;
        line-height: 1.6;
    }
    
    h1, h2, h3 {
        color: #333;
    }
    
    table {
        border-collapse: collapse;
        width: 100%;
        margin: 20px 0;
    }
    
    th, td {
        border: 1px solid #ddd;
        padding: 8px;
        text-align: left;
    }
    
    th {
        background-color: #f2f2f2;
    }
    
    button {
        background-color: #4CAF50;
        color: white;
        padding: 10px 15px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 16px;
    }
    
    button:hover {
        background-color: #45a049;
    }
</style>
