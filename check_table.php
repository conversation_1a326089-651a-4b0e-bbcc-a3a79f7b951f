<?php
// Inclure le fichier de configuration
require_once 'config.php';

// Afficher les erreurs pour le débogage
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Vérification de la structure de la table utilisateurs</h1>";

try {
    // Connexion à la base de données
    $pdo = new PDO(
        "mysql:host=".DB_HOST.";dbname=".DB_NAME.";charset=utf8",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );

    echo "<p>Connexion réussie à la base de données</p>";

    // Vérifier si la table utilisateurs existe
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);

    echo "<h2>Tables disponibles dans la base de données</h2>";
    echo "<ul>";
    foreach ($tables as $table) {
        echo "<li>$table</li>";
    }
    echo "</ul>";

    if (in_array('utilisateurs', $tables)) {
        echo "<p>La table utilisateurs existe.</p>";

        // Afficher la structure de la table utilisateurs
        $columns = $pdo->query("SHOW COLUMNS FROM utilisateurs")->fetchAll();

        echo "<h2>Structure de la table utilisateurs</h2>";
        echo "<table border='1'>";
        echo "<tr><th>Champ</th><th>Type</th><th>Null</th><th>Clé</th><th>Défaut</th><th>Extra</th></tr>";

        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . $column['Default'] . "</td>";
            echo "<td>" . $column['Extra'] . "</td>";
            echo "</tr>";
        }

        echo "</table>";

        // Vérifier si la colonne type_utilisateur existe
        $hasTypeColumn = false;
        foreach ($columns as $column) {
            if ($column['Field'] === 'type_utilisateur') {
                $hasTypeColumn = true;
                break;
            }
        }

        if ($hasTypeColumn) {
            echo "<p>La colonne type_utilisateur existe dans la table utilisateurs.</p>";

            // Afficher les valeurs distinctes de type_utilisateur
            $types = $pdo->query("SELECT DISTINCT type_utilisateur FROM utilisateurs")->fetchAll(PDO::FETCH_COLUMN);

            echo "<h2>Types d'utilisateurs existants</h2>";
            echo "<ul>";
            foreach ($types as $type) {
                echo "<li>" . ($type ? $type : "NULL") . "</li>";
            }
            echo "</ul>";

            // Compter les utilisateurs par type
            echo "<h2>Nombre d'utilisateurs par type</h2>";
            echo "<table border='1'>";
            echo "<tr><th>Type</th><th>Nombre</th></tr>";

            $stmt = $pdo->query("SELECT type_utilisateur, COUNT(*) as count FROM utilisateurs GROUP BY type_utilisateur");
            while ($row = $stmt->fetch()) {
                echo "<tr>";
                echo "<td>" . ($row['type_utilisateur'] ? $row['type_utilisateur'] : "NULL") . "</td>";
                echo "<td>" . $row['count'] . "</td>";
                echo "</tr>";
            }

            echo "</table>";
        } else {
            echo "<p>La colonne type_utilisateur n'existe pas dans la table utilisateurs.</p>";

            // Essayer d'ajouter la colonne
            echo "<p>Tentative d'ajout de la colonne type_utilisateur...</p>";

            try {
                $pdo->exec("ALTER TABLE utilisateurs ADD COLUMN type_utilisateur VARCHAR(50) DEFAULT 'utilisateur' AFTER mot_de_passe");
                echo "<p>Colonne type_utilisateur ajoutée avec succès.</p>";
            } catch (PDOException $e) {
                echo "<p>Erreur lors de l'ajout de la colonne: " . $e->getMessage() . "</p>";
            }
        }

        // Afficher les utilisateurs de type chef_departement
        $users = $pdo->query("SELECT * FROM utilisateurs WHERE type_utilisateur = 'chef_departement' ORDER BY id")->fetchAll();

        echo "<h2>Chefs de département</h2>";
        echo "<table border='1'>";

        // En-têtes de colonnes
        if (!empty($users)) {
            echo "<tr>";
            foreach (array_keys($users[0]) as $header) {
                echo "<th>$header</th>";
            }
            echo "</tr>";

            // Données
            foreach ($users as $user) {
                echo "<tr>";
                foreach ($user as $value) {
                    echo "<td>" . ($value !== null ? htmlspecialchars($value) : "NULL") . "</td>";
                }
                echo "</tr>";
            }
        } else {
            echo "<tr><td>Aucun utilisateur trouvé</td></tr>";
        }

        echo "</table>";
    } else {
        echo "<p>La table utilisateurs n'existe pas.</p>";
    }

} catch (PDOException $e) {
    echo "<p>Erreur de connexion à la base de données: " . $e->getMessage() . "</p>";
}
?>
