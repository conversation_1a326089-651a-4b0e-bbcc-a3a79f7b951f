<?php
// Script pour vérifier les références aux tables dans tous les fichiers PHP
// Afficher les erreurs pour le débogage
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Vérification des références aux tables dans les fichiers PHP</h1>";

// Fonction pour rechercher une chaîne dans un fichier
function searchInFile($file, $search) {
    $content = file_get_contents($file);
    $lines = explode("\n", $content);
    $results = [];

    foreach ($lines as $lineNumber => $line) {
        if (stripos($line, $search) !== false) {
            $results[] = [
                'line' => $lineNumber + 1,
                'content' => trim($line)
            ];
        }
    }

    return $results;
}

// Récupérer tous les fichiers PHP du répertoire courant
$phpFiles = glob("*.php");

// Rechercher les références à unites_enseignement (singulier)
$searchTerm = "unites_enseignement";
$results = [];

foreach ($phpFiles as $file) {
    $fileResults = searchInFile($file, $searchTerm);
    if (!empty($fileResults)) {
        $results[$file] = $fileResults;
    }
}

// Afficher les résultats
if (empty($results)) {
    echo "<p style='color:green'>Aucune référence à '$searchTerm' trouvée dans les fichiers PHP.</p>";
} else {
    echo "<h2>Références à '$searchTerm' trouvées dans les fichiers suivants :</h2>";
    echo "<ul>";
    foreach ($results as $file => $fileResults) {
        echo "<li><strong>$file</strong> (" . count($fileResults) . " occurrences) :";
        echo "<ul>";
        foreach ($fileResults as $result) {
            echo "<li>Ligne " . $result['line'] . ": <code>" . htmlspecialchars($result['content']) . "</code></li>";
        }
        echo "</ul>";
        echo "</li>";
    }
    echo "</ul>";
}

// Vérifier également les tables dans la base de données
echo "<h2>Vérification des tables dans la base de données</h2>";

// Inclure le fichier de configuration
require_once 'config.php';

try {
    // Connexion à la base de données
    $pdo = new PDO(
        "mysql:host=".DB_HOST.";dbname=".DB_NAME.";charset=utf8",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );

    // Récupérer la liste des tables
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);

    echo "<p>Tables trouvées dans la base de données '" . DB_NAME . "' :</p>";
    echo "<ul>";
    foreach ($tables as $table) {
        echo "<li>$table</li>";
    }
    echo "</ul>";

    // Vérifier si unites_enseignement existe
    if (in_array('unites_enseignement', $tables)) {
        echo "<p style='color:green'>La table 'unites_enseignement' existe dans la base de données.</p>";
    } else {
        echo "<p style='color:red'>La table 'unites_enseignement' n'existe pas dans la base de données.</p>";
    }

    // Vérifier si unites_enseignements existe
    if (in_array('unites_enseignements', $tables)) {
        echo "<p style='color:green'>La table 'unites_enseignements' existe dans la base de données.</p>";
    } else {
        echo "<p style='color:red'>La table 'unites_enseignements' n'existe pas dans la base de données.</p>";
    }

    // Vérifier la structure de la table departement
    echo "<h2>Structure de la table departement</h2>";
    try {
        $stmt = $pdo->query("DESCRIBE departement");
        echo "<pre>";
        print_r($stmt->fetchAll());
        echo "</pre>";
    } catch (PDOException $e) {
        echo "<p style='color:red'>Erreur: " . $e->getMessage() . "</p>";
    }

    // Vérifier la structure de la table departements
    echo "<h2>Structure de la table departements</h2>";
    try {
        $stmt = $pdo->query("DESCRIBE departements");
        echo "<pre>";
        print_r($stmt->fetchAll());
        echo "</pre>";
    } catch (PDOException $e) {
        echo "<p style='color:red'>Erreur: " . $e->getMessage() . "</p>";
    }

} catch (PDOException $e) {
    echo "<p style='color:red'>Erreur de connexion à la base de données: " . $e->getMessage() . "</p>";
}
?>
