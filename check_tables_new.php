<?php
// Connexion à la base de données
$conn = new mysqli("localhost", "root", "", "gestion_coordinteur");

if ($conn->connect_error) {
    die("Connexion échouée : " . $conn->connect_error);
}

// Afficher toutes les tables
echo "<h2>Tables dans la base de données</h2>";
$result = $conn->query("SHOW TABLES");
echo "<ul>";
while ($row = $result->fetch_row()) {
    echo "<li>" . $row[0] . "</li>";
}
echo "</ul>";

// Vérifier si la table filiere existe
$result = $conn->query("SHOW TABLES LIKE 'filiere'");
if ($result->num_rows > 0) {
    // La table existe, afficher sa structure
    echo "<h2>Structure de la table filiere</h2>";
    $result = $conn->query("DESCRIBE filiere");
    echo "<table border='1'>";
    echo "<tr><th>Champ</th><th>Type</th><th>Null</th><th>Clé</th><th>Défaut</th><th>Extra</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Afficher les données de la table filiere
    echo "<h2>Données de la table filiere</h2>";
    $result = $conn->query("SELECT * FROM filiere");
    if ($result->num_rows > 0) {
        echo "<table border='1'>";
        $first = true;
        while ($row = $result->fetch_assoc()) {
            if ($first) {
                echo "<tr>";
                foreach ($row as $key => $value) {
                    echo "<th>" . $key . "</th>";
                }
                echo "</tr>";
                $first = false;
            }
            echo "<tr>";
            foreach ($row as $value) {
                echo "<td>" . $value . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "Aucune donnée dans la table filiere.";
    }
} else {
    // La table n'existe pas, créer la table
    echo "<h2>La table filiere n'existe pas</h2>";
    echo "<p>Création de la table filiere...</p>";
    
    $sql = "CREATE TABLE filiere (
        id_filiere INT AUTO_INCREMENT PRIMARY KEY,
        nom VARCHAR(100) NOT NULL,
        id_departement INT,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    if ($conn->query($sql) === TRUE) {
        echo "<p>Table filiere créée avec succès.</p>";
        
        // Insérer des données de test
        $sql = "INSERT INTO filiere (nom, id_departement, description) VALUES
            ('Génie Informatique', 1, 'Formation en informatique et développement logiciel'),
            ('Génie Civil', 2, 'Formation en construction et infrastructure'),
            ('Génie Électrique', 3, 'Formation en électricité et électronique'),
            ('Génie Mécanique', 4, 'Formation en mécanique et conception')";
        
        if ($conn->query($sql) === TRUE) {
            echo "<p>Données de test insérées avec succès.</p>";
        } else {
            echo "<p>Erreur lors de l'insertion des données : " . $conn->error . "</p>";
        }
    } else {
        echo "<p>Erreur lors de la création de la table : " . $conn->error . "</p>";
    }
}

// Vérifier si la table departement existe
$result = $conn->query("SHOW TABLES LIKE 'departement'");
if ($result->num_rows > 0) {
    // La table existe, afficher sa structure
    echo "<h2>Structure de la table departement</h2>";
    $result = $conn->query("DESCRIBE departement");
    echo "<table border='1'>";
    echo "<tr><th>Champ</th><th>Type</th><th>Null</th><th>Clé</th><th>Défaut</th><th>Extra</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Afficher les données de la table departement
    echo "<h2>Données de la table departement</h2>";
    $result = $conn->query("SELECT * FROM departement");
    if ($result->num_rows > 0) {
        echo "<table border='1'>";
        $first = true;
        while ($row = $result->fetch_assoc()) {
            if ($first) {
                echo "<tr>";
                foreach ($row as $key => $value) {
                    echo "<th>" . $key . "</th>";
                }
                echo "</tr>";
                $first = false;
            }
            echo "<tr>";
            foreach ($row as $value) {
                echo "<td>" . $value . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "Aucune donnée dans la table departement.";
    }
} else {
    // La table n'existe pas, créer la table
    echo "<h2>La table departement n'existe pas</h2>";
    echo "<p>Création de la table departement...</p>";
    
    $sql = "CREATE TABLE departement (
        id_departement INT AUTO_INCREMENT PRIMARY KEY,
        nom VARCHAR(100) NOT NULL,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    if ($conn->query($sql) === TRUE) {
        echo "<p>Table departement créée avec succès.</p>";
        
        // Insérer des données de test
        $sql = "INSERT INTO departement (nom, description) VALUES
            ('Informatique', 'Département d\'informatique et de développement logiciel'),
            ('Génie Civil', 'Département de construction et d\'infrastructure'),
            ('Électricité', 'Département d\'électricité et d\'électronique'),
            ('Mécanique', 'Département de mécanique et de conception')";
        
        if ($conn->query($sql) === TRUE) {
            echo "<p>Données de test insérées avec succès.</p>";
        } else {
            echo "<p>Erreur lors de l'insertion des données : " . $conn->error . "</p>";
        }
    } else {
        echo "<p>Erreur lors de la création de la table : " . $conn->error . "</p>";
    }
}

$conn->close();
?>
