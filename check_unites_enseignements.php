<?php
// Script pour vérifier la structure de la table unites_enseignements
require_once 'config.php';

// Afficher les erreurs pour le débogage
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Structure de la table unites_enseignements</h1>";

try {
    // Connexion à la base de données
    $pdo = new PDO(
        "mysql:host=".DB_HOST.";dbname=".DB_NAME.";charset=utf8",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    // Vérifier si la table unites_enseignements existe
    $stmt = $pdo->query("SHOW TABLES LIKE 'unites_enseignements'");
    $tableExists = $stmt->rowCount() > 0;
    
    if ($tableExists) {
        echo "<p style='color:green'>La table 'unites_enseignements' existe dans la base de données.</p>";
        
        // Afficher la structure de la table
        $stmt = $pdo->query("DESCRIBE unites_enseignements");
        $columns = $stmt->fetchAll();
        
        echo "<h2>Colonnes de la table unites_enseignements</h2>";
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>Nom</th><th>Type</th><th>Null</th><th>Clé</th><th>Défaut</th><th>Extra</th></tr>";
        
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
            echo "<td>" . htmlspecialchars($column['Default'] ?? 'NULL') . "</td>";
            echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
        
        // Vérifier si la colonne code_ue existe
        $hasCodeUeColumn = false;
        foreach ($columns as $column) {
            if ($column['Field'] === 'code_ue') {
                $hasCodeUeColumn = true;
                break;
            }
        }
        
        if ($hasCodeUeColumn) {
            echo "<p style='color:green'>La colonne 'code_ue' existe dans la table 'unites_enseignements'.</p>";
        } else {
            echo "<p style='color:red'>La colonne 'code_ue' n'existe pas dans la table 'unites_enseignements'.</p>";
            
            // Proposer d'ajouter la colonne
            echo "<form method='post'>";
            echo "<input type='hidden' name='action' value='add_column'>";
            echo "<button type='submit' style='padding: 10px 20px; background-color: #28a745; color: white; border: none; border-radius: 5px; cursor: pointer;'>Ajouter la colonne 'code_ue' à la table 'unites_enseignements'</button>";
            echo "</form>";
        }
        
        // Afficher quelques données de la table
        $stmt = $pdo->query("SELECT * FROM unites_enseignements LIMIT 5");
        $data = $stmt->fetchAll();
        
        if (!empty($data)) {
            echo "<h2>Exemples de données dans la table unites_enseignements</h2>";
            echo "<pre>";
            print_r($data);
            echo "</pre>";
        } else {
            echo "<p>La table 'unites_enseignements' est vide.</p>";
        }
        
    } else {
        echo "<p style='color:red'>La table 'unites_enseignements' n'existe pas dans la base de données.</p>";
    }
    
    // Traiter les actions
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
        if ($_POST['action'] === 'add_column') {
            try {
                // Ajouter la colonne code_ue
                $pdo->exec("ALTER TABLE unites_enseignements ADD COLUMN code_ue VARCHAR(20) NOT NULL DEFAULT 'CODE'");
                
                // Mettre à jour les valeurs de code_ue en fonction de id_ue
                $pdo->exec("UPDATE unites_enseignements SET code_ue = CONCAT('UE', id_ue)");
                
                echo "<p style='color:green'>La colonne 'code_ue' a été ajoutée à la table 'unites_enseignements' avec succès.</p>";
                echo "<p>Veuillez rafraîchir la page pour voir les changements.</p>";
            } catch (PDOException $e) {
                echo "<p style='color:red'>Erreur lors de l'ajout de la colonne: " . $e->getMessage() . "</p>";
            }
        }
    }
    
    // Ajouter un lien vers affectation_ue.php
    echo "<p><a href='affectation_ue.php' style='padding: 10px 20px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px;'>Retour à la page des affectations</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color:red'>Erreur de connexion à la base de données: " . $e->getMessage() . "</p>";
}
?>
