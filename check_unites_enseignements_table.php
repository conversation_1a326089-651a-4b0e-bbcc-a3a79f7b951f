<?php
// Paramètres de connexion à la base de données
$host = 'localhost';
$user = 'root';
$pass = '';
$db = 'gestion-coordinteur';

// Connexion à la base de données
$conn = new mysqli($host, $user, $pass, $db);
if ($conn->connect_error) {
    die("Erreur de connexion : " . $conn->connect_error);
}

echo "<h1>Vérification de la table unites_enseignements</h1>";

// Vérifier si la table unites_enseignements existe
$result = $conn->query("SHOW TABLES LIKE 'unites_enseignements'");
if ($result->num_rows == 0) {
    echo "<p style='color:red'>La table 'unites_enseignements' n'existe pas. Création de la table...</p>";
    
    // Créer la table unites_enseignements
    $sql = "CREATE TABLE unites_enseignements (
        id_ue INT AUTO_INCREMENT PRIMARY KEY,
        code_ue VARCHAR(20) NOT NULL UNIQUE,
        intitule VARCHAR(255) NOT NULL,
        credits INT NOT NULL DEFAULT 3,
        heures_cm INT NOT NULL DEFAULT 0,
        heures_td INT NOT NULL DEFAULT 0,
        heures_tp INT NOT NULL DEFAULT 0,
        semestre VARCHAR(20) NOT NULL,
        id_departement INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
    
    if ($conn->query($sql) === TRUE) {
        echo "<p style='color:green'>Table 'unites_enseignements' créée avec succès.</p>";
        
        // Insérer quelques UE de test
        $sql = "INSERT INTO unites_enseignements (code_ue, intitule, credits, heures_cm, heures_td, heures_tp, semestre, id_departement) VALUES
            ('INFO101', 'Introduction à l\'informatique', 3, 20, 10, 10, 'S1', 1),
            ('INFO102', 'Algorithmique', 4, 20, 20, 20, 'S1', 1),
            ('MATH101', 'Mathématiques discrètes', 3, 30, 15, 0, 'S1', 1),
            ('INFO201', 'Programmation orientée objet', 4, 20, 10, 30, 'S2', 1),
            ('INFO202', 'Bases de données', 4, 20, 10, 30, 'S2', 1)";
        
        if ($conn->query($sql) === TRUE) {
            echo "<p style='color:green'>UE de test ajoutées avec succès.</p>";
        } else {
            echo "<p style='color:red'>Erreur lors de l'ajout des UE de test : " . $conn->error . "</p>";
        }
    } else {
        echo "<p style='color:red'>Erreur lors de la création de la table 'unites_enseignements' : " . $conn->error . "</p>";
    }
} else {
    echo "<p style='color:green'>La table 'unites_enseignements' existe déjà.</p>";
    
    // Afficher la structure de la table
    $result = $conn->query("DESCRIBE unites_enseignements");
    if ($result) {
        echo "<h2>Structure de la table 'unites_enseignements'</h2>";
        echo "<table border='1'>";
        echo "<tr><th>Champ</th><th>Type</th><th>Null</th><th>Clé</th><th>Défaut</th><th>Extra</th></tr>";
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['Field'] . "</td>";
            echo "<td>" . $row['Type'] . "</td>";
            echo "<td>" . $row['Null'] . "</td>";
            echo "<td>" . $row['Key'] . "</td>";
            echo "<td>" . $row['Default'] . "</td>";
            echo "<td>" . $row['Extra'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Afficher les données de la table
    $result = $conn->query("SELECT * FROM unites_enseignements");
    if ($result) {
        echo "<h2>Données de la table 'unites_enseignements'</h2>";
        if ($result->num_rows > 0) {
            echo "<table border='1'>";
            echo "<tr><th>ID</th><th>Code UE</th><th>Intitulé</th><th>Crédits</th><th>CM</th><th>TD</th><th>TP</th><th>Semestre</th><th>Département</th></tr>";
            while ($row = $result->fetch_assoc()) {
                echo "<tr>";
                echo "<td>" . $row['id_ue'] . "</td>";
                echo "<td>" . $row['code_ue'] . "</td>";
                echo "<td>" . $row['intitule'] . "</td>";
                echo "<td>" . $row['credits'] . "</td>";
                echo "<td>" . $row['heures_cm'] . "</td>";
                echo "<td>" . $row['heures_td'] . "</td>";
                echo "<td>" . $row['heures_tp'] . "</td>";
                echo "<td>" . $row['semestre'] . "</td>";
                echo "<td>" . $row['id_departement'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>Aucune UE trouvée dans la table.</p>";
            
            // Proposer d'ajouter des UE de test
            echo "<form method='post'>";
            echo "<input type='hidden' name='action' value='add_test_data'>";
            echo "<button type='submit'>Ajouter des UE de test</button>";
            echo "</form>";
        }
    }
}

// Traitement des actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    if ($_POST['action'] === 'add_test_data') {
        $sql = "INSERT INTO unites_enseignements (code_ue, intitule, credits, heures_cm, heures_td, heures_tp, semestre, id_departement) VALUES
            ('INFO101', 'Introduction à l\'informatique', 3, 20, 10, 10, 'S1', 1),
            ('INFO102', 'Algorithmique', 4, 20, 20, 20, 'S1', 1),
            ('MATH101', 'Mathématiques discrètes', 3, 30, 15, 0, 'S1', 1),
            ('INFO201', 'Programmation orientée objet', 4, 20, 10, 30, 'S2', 1),
            ('INFO202', 'Bases de données', 4, 20, 10, 30, 'S2', 1)";
        
        if ($conn->query($sql) === TRUE) {
            echo "<p style='color:green'>UE de test ajoutées avec succès.</p>";
            echo "<meta http-equiv='refresh' content='1'>"; // Rafraîchir la page
        } else {
            echo "<p style='color:red'>Erreur lors de l'ajout des UE de test : " . $conn->error . "</p>";
        }
    }
}

// Fermer la connexion
$conn->close();
?>

<p><a href="emplois_temps_complet.php">Retour à la gestion des emplois du temps</a></p>
