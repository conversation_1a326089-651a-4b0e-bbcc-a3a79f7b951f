<?php
// Connexion à la base de données
$host = '127.0.0.1';
$dbname = 'gestion_coordinteur';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Vérifier si la table utilisateurs existe
    $stmt = $pdo->query("SHOW TABLES LIKE 'utilisateurs'");
    $tableExists = $stmt->rowCount() > 0;
    
    if ($tableExists) {
        echo "<h2>La table 'utilisateurs' existe</h2>";
        
        // Afficher la structure de la table
        $stmt = $pdo->query("DESCRIBE utilisateurs");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>Structure de la table utilisateurs</h3>";
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Champ</th><th>Type</th><th>Null</th><th>Clé</th><th>Défaut</th><th>Extra</th></tr>";
        
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . $column['Null'] . "</td>";
            echo "<td>" . $column['Key'] . "</td>";
            echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
            echo "<td>" . $column['Extra'] . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
        
        // Afficher les valeurs distinctes de type_utilisateur
        $stmt = $pdo->query("SELECT DISTINCT type_utilisateur FROM utilisateurs");
        $types = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        echo "<h3>Types d'utilisateurs disponibles</h3>";
        echo "<ul>";
        foreach ($types as $type) {
            echo "<li>" . htmlspecialchars($type ?? 'NULL') . "</li>";
        }
        echo "</ul>";
        
        // Afficher quelques utilisateurs de type "enseignant"
        $stmt = $pdo->query("SELECT * FROM utilisateurs WHERE type_utilisateur = 'enseignant' LIMIT 5");
        $enseignants = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>Exemples d'utilisateurs de type 'enseignant'</h3>";
        
        if (count($enseignants) > 0) {
            echo "<table border='1' cellpadding='5'>";
            echo "<tr>";
            foreach (array_keys($enseignants[0]) as $key) {
                echo "<th>" . htmlspecialchars($key) . "</th>";
            }
            echo "</tr>";
            
            foreach ($enseignants as $enseignant) {
                echo "<tr>";
                foreach ($enseignant as $value) {
                    echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>";
                }
                echo "</tr>";
            }
            
            echo "</table>";
        } else {
            echo "<p>Aucun utilisateur de type 'enseignant' trouvé.</p>";
        }
        
        // Compter le nombre total d'enseignants
        $stmt = $pdo->query("SELECT COUNT(*) FROM utilisateurs WHERE type_utilisateur = 'enseignant'");
        $count = $stmt->fetchColumn();
        
        echo "<p>Nombre total d'utilisateurs de type 'enseignant': " . $count . "</p>";
        
    } else {
        echo "<h2>La table 'utilisateurs' n'existe pas</h2>";
    }
    
} catch (PDOException $e) {
    echo "<h2>Erreur</h2>";
    echo "<p>Une erreur s'est produite : " . $e->getMessage() . "</p>";
}
?>
