<?php
require_once 'config.php';

// Activer l'affichage des erreurs pour le débogage
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

try {
    // Connexion à la base de données
    $pdo = new PDO(
        'mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8',
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );

    echo "<h1>Structure de la table utilisateurs</h1>";

    // Vérifier si la table utilisateurs existe
    $tables = $pdo->query("SHOW TABLES LIKE 'utilisateurs'")->fetchAll();
    if (count($tables) === 0) {
        echo "<p>La table 'utilisateurs' n'existe pas.</p>";

        // Afficher toutes les tables disponibles
        $allTables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
        echo "<h2>Tables disponibles dans la base de données</h2>";
        echo "<ul>";
        foreach ($allTables as $table) {
            echo "<li>$table</li>";
        }
        echo "</ul>";
    } else {
        // Afficher la structure de la table utilisateurs
        $columns = $pdo->query("DESCRIBE utilisateurs")->fetchAll();
        echo "<h2>Colonnes de la table utilisateurs</h2>";
        echo "<table border='1'>";
        echo "<tr><th>Nom</th><th>Type</th><th>Null</th><th>Clé</th><th>Défaut</th><th>Extra</th></tr>";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>{$column['Field']}</td>";
            echo "<td>{$column['Type']}</td>";
            echo "<td>{$column['Null']}</td>";
            echo "<td>{$column['Key']}</td>";
            echo "<td>{$column['Default']}</td>";
            echo "<td>{$column['Extra']}</td>";
            echo "</tr>";
        }
        echo "</table>";

        // Afficher les données de la table utilisateurs
        $users = $pdo->query("SELECT * FROM utilisateurs")->fetchAll();
        echo "<h2>Données de la table utilisateurs</h2>";
        if (count($users) === 0) {
            echo "<p>Aucune donnée dans la table utilisateurs.</p>";
        } else {
            echo "<table border='1'>";
            // Afficher les en-têtes de colonnes
            echo "<tr>";
            foreach (array_keys($users[0]) as $header) {
                echo "<th>$header</th>";
            }
            echo "</tr>";

            // Afficher les données
            foreach ($users as $user) {
                echo "<tr>";
                foreach ($user as $value) {
                    echo "<td>" . (is_null($value) ? "NULL" : htmlspecialchars($value)) . "</td>";
                }
                echo "</tr>";
            }
            echo "</table>";
        }
    }

} catch (PDOException $e) {
    echo "<h1>Erreur</h1>";
    echo "<p>Erreur de base de données: " . $e->getMessage() . "</p>";
}
?>
