<?php
require_once 'config.php';

// Gestion de la session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Vérification de l'authentification
if (!isset($_SESSION['user_id'])) {
    // Si l'utilisateur n'est pas connecté, on le redirige vers la page de connexion
    header("Location: login.php?error=session_invalide");
    exit();
}

// Pour le débogage
error_log("Session user_id: " . $_SESSION['user_id']);
error_log("Session role: " . ($_SESSION['role'] ?? 'non défini'));
error_log("Session user_type: " . ($_SESSION['user_type'] ?? 'non défini'));

// Forcer le type d'utilisateur à chef_departement pour cette page
$_SESSION['user_type'] = 'chef_departement';
$_SESSION['role'] = 'chef_departement';

try {
    // Connexion à la base de données
    $pdo = new PDO(
        "mysql:host=".DB_HOST.";dbname=".DB_NAME.";charset=utf8",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );

    // Récupération des infos du département - avec gestion des erreurs
    try {
        // Essayer d'abord avec la structure 'departement' (singulier)
        $stmt = $pdo->prepare("
            SELECT d.id_departement, d.nom_departement
            FROM departement d
            JOIN utilisateurs u ON d.id_departement = u.id_departement
            WHERE u.id = ?
        ");
        $stmt->execute([$_SESSION['user_id']]);
        $departement = $stmt->fetch();
    } catch (PDOException $e) {
        error_log("Erreur requête departement: " . $e->getMessage());
        $departement = false;
    }

    // Si la première requête échoue, essayer avec la structure 'departements' (pluriel)
    if (!$departement) {
        try {
            $stmt = $pdo->prepare("
                SELECT d.departement_id as id_departement, d.nom_departement
                FROM departements d
                JOIN utilisateurs u ON d.departement_id = u.id_departement
                WHERE u.id = ?
            ");
            $stmt->execute([$_SESSION['user_id']]);
            $departement = $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Erreur requête departements: " . $e->getMessage());
            $departement = false;
        }
    }

    // Si toujours pas de département, utiliser les valeurs de session ou des valeurs par défaut
    if (!$departement) {
        error_log("Aucun département trouvé pour l'utilisateur " . $_SESSION['user_id']);

        // Utiliser les valeurs de session si elles existent
        if (isset($_SESSION['id_departement']) || isset($_SESSION['departement_id'])) {
            $dept_id = $_SESSION['id_departement'] ?? $_SESSION['departement_id'] ?? 1;
            $dept_nom = $_SESSION['departement_nom'] ?? 'Département par défaut';

            $departement = [
                'id_departement' => $dept_id,
                'nom_departement' => $dept_nom
            ];

            error_log("Utilisation des valeurs de session pour le département: ID=$dept_id, Nom=$dept_nom");
        } else {
            // Valeurs par défaut
            $departement = [
                'id_departement' => 1,
                'nom_departement' => 'Département par défaut'
            ];
            error_log("Utilisation des valeurs par défaut pour le département");
        }
    }

    // Mettre à jour les variables de session avec les deux formats pour assurer la compatibilité
    $_SESSION['id_departement'] = $departement['id_departement'];
    $_SESSION['departement_id'] = $departement['id_departement'];
    $_SESSION['departement_nom'] = $departement['nom_departement'];

    // Statistiques
    $statsQuery = $pdo->prepare("
        SELECT
            (SELECT COUNT(*) FROM utilisateurs WHERE id_departement = ? AND type_utilisateur = 'enseignant') AS total_professeurs,
            (SELECT COUNT(*) FROM utilisateurs WHERE id_departement = ? AND type_utilisateur = 'vacataire') AS total_vacataires,
            (SELECT COUNT(*) FROM specialite WHERE id_departement = ?) AS total_specialites,
            (SELECT COUNT(*) FROM affectations_vacataires WHERE id_vacataire IN
                (SELECT id FROM utilisateurs WHERE id_departement = ? AND type_utilisateur = 'vacataire')) AS total_affectations,
            (SELECT COUNT(*) FROM unites_enseignements WHERE filiere IN
                (SELECT nom_filiere FROM filiere WHERE id_departement = ?)) AS total_modules,
            (SELECT COUNT(*) FROM affectations_vacataires WHERE id_vacataire IN
                (SELECT id FROM utilisateurs WHERE id_departement = ? AND type_utilisateur = 'vacataire')
                AND date_affectation >= DATE_SUB(NOW(), INTERVAL 7 DAY)) AS total_recentes
    ");
    $statsQuery->execute([
        $departement['id_departement'],
        $departement['id_departement'],
        $departement['id_departement'],
        $departement['id_departement'],
        $departement['id_departement'],
        $departement['id_departement']
    ]);
    $stats = $statsQuery->fetch();

    // Dernières affectations
    $affectationsQuery = $pdo->prepare("
        SELECT
            u.nom,
            u.prenom,
            m.nom AS matiere,
            DATE_FORMAT(av.date_affectation, '%d/%m/%Y %H:%i') AS date_affectation
        FROM affectations_vacataires av
        JOIN utilisateurs u ON av.id_vacataire = u.id
        JOIN matieres m ON av.id_matiere = m.id_matiere
        WHERE u.id_departement = ?
        ORDER BY av.date_affectation DESC
        LIMIT 5
    ");
    $affectationsQuery->execute([$departement['id_departement']]);
    $affectations = $affectationsQuery->fetchAll();

    // Dernières validations (simulé car pas de table choix_professeurs dans votre BD)
    $validations = [];

    // Données pour le graphique
    $chartQuery = $pdo->prepare("
        SELECT
            DAYNAME(date_affectation) AS jour,
            COUNT(*) AS total
        FROM affectations_vacataires
        WHERE id_vacataire IN (SELECT id FROM utilisateurs WHERE id_departement = ?)
        AND date_affectation >= DATE_SUB(NOW(), INTERVAL 1 MONTH)
        GROUP BY jour
        ORDER BY FIELD(jour, 'Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi', 'Dimanche')
    ");
    $chartQuery->execute([$departement['id_departement']]);
    $chart_data = $chartQuery->fetchAll();

    // Si pas de données pour le graphique, créer des données vides
    if (empty($chart_data)) {
        $jours = ['Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi', 'Dimanche'];
        foreach ($jours as $jour) {
            $chart_data[] = ['jour' => $jour, 'total' => 0];
        }
    }

} catch(PDOException $e) {
    die("Erreur système : " . htmlspecialchars($e->getMessage()));
}

function sanitize($data) {
    return htmlspecialchars($data ?? '', ENT_QUOTES, 'UTF-8');
}
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Chef Département - <?= sanitize($_SESSION['departement_nom']) ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #1E90FF;
            --primary-magenta: #FF00FF;
            --blue-transparent: rgba(30, 144, 255, 0.3);
            --dark-bg: #0a192f;
        }

        body {
            background: linear-gradient(rgba(10, 25, 47, 0.85), rgba(108, 27, 145, 0.85)),
                       url('images/background.jpg') center/cover fixed;
            color: white;
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .sidebar {
            background: rgba(44, 62, 80, 0.95);
            backdrop-filter: blur(5px);
            border-right: 2px solid var(--primary-blue);
            box-shadow: 4px 0 15px var(--blue-transparent);
            animation: borderGlow 8s infinite alternate;
        }

        @keyframes borderGlow {
            0% { border-color: var(--primary-blue); }
            50% { border-color: var(--primary-magenta); }
            100% { border-color: var(--primary-blue); }
        }

        .sidebar .nav-link {
            color: white;
            padding: 12px 15px;
            margin: 8px 0;
            border-radius: 8px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .sidebar .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(18, 84, 151, 0.2), transparent);
            transition: 0.5s;
        }

        .sidebar .nav-link:hover {
            background: rgba(30, 144, 255, 0.1);
            transform: translateX(10px);
        }

        .stat-card {
            background: rgba(10, 25, 47, 0.9);
            border: 2px solid var(--primary-blue);
            border-radius: 10px;
            transition: all 0.3s ease;
            animation: cardBorderPulse 10s infinite;
        }

        @keyframes cardBorderPulse {
            0% { border-color: var(--primary-blue); box-shadow: 0 5px 15px var(--blue-transparent); }
            50% { border-color: var(--primary-magenta); box-shadow: 0 5px 20px rgba(255, 0, 255, 0.3); }
            100% { border-color: var(--primary-blue); box-shadow: 0 5px 15px var(--blue-transparent); }
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(255, 0, 255, 0.4) !important;
        }

        #affectationsTable {
            background: rgba(10, 25, 47, 0.9);
            border: 1px solid var(--primary-blue);
            color: white;
        }

        #affectationsTable thead {
            background: linear-gradient(90deg, var(--primary-blue) 0%, var(--dark-bg) 100%);
            border-bottom: 2px solid var(--primary-magenta);
        }

        .chart-container {
            background: rgba(10, 25, 47, 0.9);
            border: 2px solid var(--primary-blue);
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <nav class="col-md-3 col-lg-2 sidebar">
            <div class="text-center mb-4">
                <img src="images/logo.png" alt="Logo" class="img-fluid mb-3" style="filter: drop-shadow(0 0 5px var(--primary-blue));">
                <h5 class="text-white" style="text-shadow: 0 0 10px var(--primary-blue);">
                    <?= sanitize($_SESSION['departement_nom']) ?>
                </h5>
            </div>
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link active" href="chef_dashboard.php"
                       style="background: rgba(30, 144, 255, 0.2); border-left: 4px solid var(--primary-blue);">
                        <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="gestion_modules.php">
                        <i class="fas fa-book-open me-2"></i> Unités d'Enseignement
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="gestion_professeurs.php">
                        <i class="fas fa-users-cog me-2"></i> Gestion Professeurs
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="affectation_ue.php">
                        <i class="fas fa-tasks me-2"></i> Affectation UE
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="gestion_choix.php">
                        <i class="fas fa-check-double me-2"></i> Validation Choix
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="charge_horaire.php">
                        <i class="fas fa-chart-pie me-2"></i> Charge Horaire
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="ue_vacantes.php">
                        <i class="fas fa-exclamation-triangle me-2"></i> UE Vacantes
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="historique.php">
                        <i class="fas fa-archive me-2"></i> Historique
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="reporting.php">
                        <i class="fas fa-file-alt me-2"></i> Reporting
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="import_export.php">
                        <i class="fas fa-file-excel me-2"></i> Import/Export
                    </a>
                </li>
                <li class="nav-item mt-4">
                    <a class="nav-link text-danger" href="logout.php">
                        <i class="fas fa-sign-out-alt me-2"></i> Déconnexion
                    </a>
                </li>
            </ul>
        </nav>

        <!-- Contenu principal -->
        <main class="col-md-9 ms-sm-auto col-lg-10 p-4">
            <h2 class="mb-4">Tableau de Bord - <?= sanitize($_SESSION['departement_nom']) ?></h2>

            <!-- Statistiques -->
            <div class="row g-4 mb-4">
                <div class="col-md-4 col-lg-2">
                    <div class="stat-card text-center p-3">
                        <h5><i class="fas fa-users me-2"></i> Enseignants</h5>
                        <h2 class="text-primary"><?= $stats['total_professeurs'] ?? 0 ?></h2>
                    </div>
                </div>
                <div class="col-md-4 col-lg-2">
                    <div class="stat-card text-center p-3">
                        <h5><i class="fas fa-user-clock me-2"></i> Vacataires</h5>
                        <h2 class="text-success"><?= $stats['total_vacataires'] ?? 0 ?></h2>
                    </div>
                </div>
                <div class="col-md-4 col-lg-2">
                    <div class="stat-card text-center p-3">
                        <h5><i class="fas fa-graduation-cap me-2"></i> Spécialités</h5>
                        <h2 class="text-warning"><?= $stats['total_specialites'] ?? 0 ?></h2>
                    </div>
                </div>
                <div class="col-md-4 col-lg-2">
                    <div class="stat-card text-center p-3">
                        <h5><i class="fas fa-tasks me-2"></i> Affectations</h5>
                        <h2 class="text-info"><?= $stats['total_affectations'] ?? 0 ?></h2>
                    </div>
                </div>
                <div class="col-md-4 col-lg-2">
                    <div class="stat-card text-center p-3">
                        <h5><i class="fas fa-book me-2"></i> Matières</h5>
                        <h2 class="text-light"><?= $stats['total_modules'] ?? 0 ?></h2>
                    </div>
                </div>
                <div class="col-md-4 col-lg-2">
                    <div class="stat-card text-center p-3">
                        <h5><i class="fas fa-bell me-2"></i> Récentes</h5>
                        <h2 class="text-danger"><?= $stats['total_recentes'] ?? 0 ?></h2>
                    </div>
                </div>
            </div>

            <!-- Dernières affectations -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card shadow mb-4 border-primary">
                        <div class="card-header bg-dark">
                            <h5 class="m-0"><i class="fas fa-history me-2"></i> Dernières affectations</h5>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($affectations)): ?>
                            <div class="table-responsive">
                                <table class="table table-hover" id="affectationsTable">
                                    <thead>
                                        <tr>
                                            <th>Nom</th>
                                            <th>Prénom</th>
                                            <th>Matière</th>
                                            <th>Date</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($affectations as $affectation): ?>
                                        <tr>
                                            <td><?= sanitize($affectation['nom']) ?></td>
                                            <td><?= sanitize($affectation['prenom']) ?></td>
                                            <td><?= sanitize($affectation['matiere']) ?></td>
                                            <td><?= sanitize($affectation['date_affectation']) ?></td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php else: ?>
                            <div class="alert alert-info">Aucune affectation récente</div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Dernières validations -->
                <div class="col-md-6">
                    <div class="card shadow mb-4 border-success">
                        <div class="card-header bg-dark">
                            <h5 class="m-0"><i class="fas fa-check-circle me-2"></i> Activité récente</h5>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($validations)): ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Vacataire</th>
                                            <th>Action</th>
                                            <th>Date</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($validations as $validation): ?>
                                        <tr>
                                            <td><?= sanitize($validation['vacataire']) ?></td>
                                            <td><?= sanitize($validation['action']) ?></td>
                                            <td><?= sanitize($validation['date']) ?></td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php else: ?>
                            <div class="alert alert-info">Aucune activité récente</div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Graphique -->
            <div class="chart-container">
                <canvas id="activityChart"></canvas>
            </div>
        </main>
    </div>
</div>

<script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
<script>
    $(document).ready(function() {
        // Configuration DataTable
        $('#affectationsTable').DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/fr-FR.json'
            },
            searching: false,
            paging: false,
            info: false
        });

        // Configuration du graphique
        const ctx = document.getElementById('activityChart').getContext('2d');
        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: <?= json_encode(array_column($chart_data, 'jour')) ?>,
                datasets: [{
                    label: "Affectations par jour",
                    data: <?= json_encode(array_column($chart_data, 'total')) ?>,
                    backgroundColor: 'rgba(30, 144, 255, 0.7)',
                    borderColor: 'rgba(255, 255, 255, 0.8)',
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        labels: {
                            color: 'white',
                            font: {
                                size: 14
                            }
                        }
                    },
                    title: {
                        display: true,
                        text: 'Activité hebdomadaire',
                        color: 'white',
                        font: {
                            size: 16
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            color: 'white',
                            stepSize: 1
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    },
                    x: {
                        ticks: {
                            color: 'white'
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    }
                }
            }
        });
    });
</script>
</body>
</html>