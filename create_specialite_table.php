<?php
require_once 'config.php';

// Activer l'affichage des erreurs pour le débogage
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

try {
    // Connexion à la base de données
    $pdo = new PDO(
        'mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8',
        DB_USER,
        DB_PASSWORD,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    echo "<p>Connexion à la base de données réussie</p>";

    // Vérifier si la table specialite existe
    $tables = $pdo->query("SHOW TABLES LIKE 'specialite'")->fetchAll();
    if (count($tables) === 0) {
        echo "<p>La table 'specialite' n'existe pas. Création de la table...</p>";
        
        // Créer la table specialite
        $pdo->exec("
            CREATE TABLE specialite (
                id_specialite INT AUTO_INCREMENT PRIMARY KEY,
                nom_specialite VARCHAR(255) NOT NULL,
                id_departement INT NOT NULL,
                FOREIGN KEY (id_departement) REFERENCES departement(id_departement)
            )
        ");
        echo "<p>Table 'specialite' créée avec succès</p>";
        
        // Vérifier si la table departement existe et contient des données
        $departements = $pdo->query("SELECT id_departement, nom_departement FROM departement")->fetchAll();
        
        if (count($departements) > 0) {
            echo "<p>Ajout de spécialités pour les départements existants...</p>";
            
            // Ajouter des spécialités pour chaque département
            $specialites = [
                // Spécialités pour le département Informatique/Mathématiques (id 1)
                ['Développement logiciel', 1],
                ['Intelligence Artificielle', 1],
                ['Mathématiques Appliquées', 1],
                ['Développement Web', 1],
                ['Base de Données', 1],
                ['Réseaux', 1],
                
                // Spécialités pour le département Physique (id 2)
                ['Physique Fondamentale', 2],
                ['Physique Appliquée', 2],
                ['Électronique', 2],
                ['Physique Nucléaire', 2]
            ];
            
            $stmt = $pdo->prepare("INSERT INTO specialite (nom_specialite, id_departement) VALUES (?, ?)");
            
            foreach ($specialites as $specialite) {
                $stmt->execute($specialite);
                echo "<p>Spécialité '{$specialite[0]}' ajoutée pour le département ID {$specialite[1]}</p>";
            }
            
            echo "<p>Toutes les spécialités ont été ajoutées avec succès</p>";
        } else {
            echo "<p>Aucun département trouvé. Veuillez d'abord créer des départements.</p>";
        }
    } else {
        echo "<p>La table 'specialite' existe déjà</p>";
        
        // Afficher les spécialités existantes
        $specialites = $pdo->query("SELECT id_specialite, nom_specialite, id_departement FROM specialite ORDER BY id_departement, nom_specialite")->fetchAll();
        
        echo "<h2>Spécialités existantes</h2>";
        echo "<table border='1'>";
        echo "<tr><th>ID</th><th>Nom</th><th>Département ID</th></tr>";
        
        foreach ($specialites as $specialite) {
            echo "<tr>";
            echo "<td>{$specialite['id_specialite']}</td>";
            echo "<td>{$specialite['nom_specialite']}</td>";
            echo "<td>{$specialite['id_departement']}</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    }
    
    // Afficher les départements existants
    $departements = $pdo->query("SELECT id_departement, nom_departement FROM departement ORDER BY nom_departement")->fetchAll();
    
    echo "<h2>Départements existants</h2>";
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>Nom</th></tr>";
    
    foreach ($departements as $departement) {
        echo "<tr>";
        echo "<td>{$departement['id_departement']}</td>";
        echo "<td>{$departement['nom_departement']}</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
} catch (PDOException $e) {
    echo "<p>Erreur : " . $e->getMessage() . "</p>";
}
?>
