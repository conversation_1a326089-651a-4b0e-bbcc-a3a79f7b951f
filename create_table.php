<?php
// Inclure le fichier de configuration
require_once 'config.php';

// Afficher les erreurs pour le débogage
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Création de la table unites_enseignements</h1>";

try {
    // Connexion à la base de données
    $pdo = new PDO(
        "mysql:host=".DB_HOST.";dbname=".DB_NAME.";charset=utf8",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    // Vérifier si la table unites_enseignements existe
    $stmt = $pdo->query("SHOW TABLES LIKE 'unites_enseignements'");
    $tableExists = $stmt->rowCount() > 0;
    
    if ($tableExists) {
        echo "<p style='color:green'>La table 'unites_enseignements' existe déjà dans la base de données.</p>";
    } else {
        echo "<p>La table 'unites_enseignements' n'existe pas. Création de la table...</p>";
        
        // Créer la table unites_enseignements
        $pdo->exec("
            CREATE TABLE unites_enseignements (
                id_ue INT AUTO_INCREMENT PRIMARY KEY,
                code_ue VARCHAR(20) NOT NULL,
                intitule VARCHAR(255) NOT NULL,
                filiere VARCHAR(100),
                niveau VARCHAR(50),
                credit INT DEFAULT 3,
                volume_horaire INT DEFAULT 30,
                departement_id INT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ");
        
        echo "<p style='color:green'>La table 'unites_enseignements' a été créée avec succès.</p>";
        
        // Insérer quelques données de test
        $pdo->exec("
            INSERT INTO unites_enseignements (code_ue, intitule, filiere, niveau, credit, volume_horaire, departement_id)
            VALUES
            ('INF101', 'Introduction à l\'informatique', 'Informatique', 'L1', 3, 30, 1),
            ('INF102', 'Algorithmique', 'Informatique', 'L1', 4, 40, 1),
            ('INF201', 'Programmation orientée objet', 'Informatique', 'L2', 5, 50, 1),
            ('MAT101', 'Analyse mathématique', 'Mathématiques', 'L1', 3, 30, 1),
            ('MAT102', 'Algèbre linéaire', 'Mathématiques', 'L1', 4, 40, 1)
        ");
        
        echo "<p style='color:green'>Des données de test ont été insérées dans la table 'unites_enseignements'.</p>";
    }
    
    // Vérifier si la table unites_enseignement existe
    $stmt = $pdo->query("SHOW TABLES LIKE 'unites_enseignement'");
    $singularTableExists = $stmt->rowCount() > 0;
    
    if ($singularTableExists) {
        echo "<p style='color:orange'>La table 'unites_enseignement' (au singulier) existe également dans la base de données.</p>";
        
        // Vérifier si la table unites_enseignement contient des données
        $stmt = $pdo->query("SELECT COUNT(*) FROM unites_enseignement");
        $count = $stmt->fetchColumn();
        
        if ($count > 0) {
            echo "<p>La table 'unites_enseignement' contient $count enregistrements.</p>";
            echo "<p>Voulez-vous copier ces données dans la table 'unites_enseignements' ?</p>";
            echo "<form method='post'>";
            echo "<input type='submit' name='copy_data' value='Copier les données'>";
            echo "</form>";
            
            if (isset($_POST['copy_data'])) {
                // Récupérer les colonnes de la table unites_enseignement
                $stmt = $pdo->query("DESCRIBE unites_enseignement");
                $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
                
                // Récupérer les données de la table unites_enseignement
                $stmt = $pdo->query("SELECT * FROM unites_enseignement");
                $data = $stmt->fetchAll();
                
                // Insérer les données dans la table unites_enseignements
                $insertCount = 0;
                foreach ($data as $row) {
                    $insertColumns = [];
                    $insertValues = [];
                    $params = [];
                    
                    foreach ($row as $column => $value) {
                        $insertColumns[] = $column;
                        $insertValues[] = "?";
                        $params[] = $value;
                    }
                    
                    $sql = "INSERT INTO unites_enseignements (" . implode(", ", $insertColumns) . ") VALUES (" . implode(", ", $insertValues) . ")";
                    $stmt = $pdo->prepare($sql);
                    $stmt->execute($params);
                    $insertCount++;
                }
                
                echo "<p style='color:green'>$insertCount enregistrements ont été copiés de 'unites_enseignement' vers 'unites_enseignements'.</p>";
            }
        } else {
            echo "<p>La table 'unites_enseignement' est vide.</p>";
        }
    } else {
        echo "<p style='color:green'>La table 'unites_enseignement' (au singulier) n'existe pas dans la base de données.</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color:red'>Erreur de base de données: " . $e->getMessage() . "</p>";
}
?>
