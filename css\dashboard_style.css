:root {
    --primary-color: #8e24aa;
    --primary-light: #c158dc;
    --primary-dark: #5c007a;
    --secondary-color: #f06292;
    --text-on-primary: #ffffff;
    --text-on-secondary: #ffffff;
    --background-color: #f5f5f5;
    --card-color: #ffffff;
    --border-radius: 8px;
    --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 0;
    background-color: var(--background-color);
    color: #333;
}

/* Header styles */
.header {
    background-color: var(--primary-color);
    color: var(--text-on-primary);
    padding: 15px 20px;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
}

.header-logo {
    width: 40px;
    height: 40px;
    margin-right: 15px;
    background-color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.header-logo img {
    width: 30px;
    height: 30px;
}

.header-title {
    font-size: 1.5rem;
    font-weight: 600;
}

/* Sidebar styles */
.sidebar {
    background-color: var(--primary-color);
    color: var(--text-on-primary);
    width: 250px;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    padding-top: 70px;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
    z-index: 900;
    transition: var(--transition);
}

.sidebar-header {
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-menu li {
    padding: 0;
}

.sidebar-menu a {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    color: var(--text-on-primary);
    text-decoration: none;
    transition: var(--transition);
}

.sidebar-menu a:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.sidebar-menu a.active {
    background-color: rgba(255, 255, 255, 0.2);
    border-left: 4px solid white;
}

.sidebar-menu i {
    margin-right: 10px;
    font-size: 1.2rem;
}

/* Main content styles */
.main-content {
    margin-left: 250px;
    padding: 90px 20px 20px;
    min-height: calc(100vh - 70px);
}

/* Card styles */
.dashboard-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.card {
    background-color: var(--card-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 20px;
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.card-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    color: white;
}

.card-icon.purple {
    background-color: var(--primary-color);
}

.card-icon.pink {
    background-color: var(--secondary-color);
}

.card-icon.blue {
    background-color: #29b6f6;
}

.card-icon.green {
    background-color: #66bb6a;
}

.card-icon i {
    font-size: 1.5rem;
}

.card-number {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.card-title {
    color: #777;
    font-size: 0.9rem;
}

/* Info card styles */
.info-card {
    background-color: var(--card-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 20px;
    margin-bottom: 20px;
}

.info-card h3 {
    margin-top: 0;
    color: var(--primary-color);
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.info-item {
    margin-bottom: 10px;
}

.info-label {
    font-weight: bold;
    color: #555;
}

/* Quick links styles */
.quick-links {
    background-color: var(--primary-color);
    color: var(--text-on-primary);
    border-radius: var(--border-radius);
    padding: 15px;
    margin-bottom: 20px;
}

.quick-links h3 {
    margin-top: 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding-bottom: 10px;
}

.quick-links-list {
    list-style: none;
    padding: 0;
}

.quick-links-list a {
    display: flex;
    align-items: center;
    padding: 10px;
    color: var(--text-on-primary);
    text-decoration: none;
    border-radius: 4px;
    transition: var(--transition);
}

.quick-links-list a:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.quick-links-list i {
    margin-right: 10px;
}

/* Button styles */
.btn {
    display: inline-block;
    padding: 10px 15px;
    border-radius: 4px;
    text-decoration: none;
    transition: var(--transition);
    border: none;
    cursor: pointer;
    font-weight: 500;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--text-on-primary);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: var(--text-on-secondary);
}

.btn-secondary:hover {
    background-color: #e91e63;
}

.btn-logout {
    background-color: #f44336;
    color: white;
}

.btn-logout:hover {
    background-color: #d32f2f;
}

/* Calendar styles */
.calendar-card {
    background-color: var(--card-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
}

.calendar-header {
    background-color: var(--primary-color);
    color: var(--text-on-primary);
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 5px;
    padding: 15px;
}

.calendar-day {
    text-align: center;
    padding: 10px;
    border-radius: 4px;
}

.calendar-day.header {
    font-weight: bold;
    color: var(--primary-color);
}

.calendar-day.today {
    background-color: var(--primary-color);
    color: var(--text-on-primary);
}

/* Responsive styles */
@media (max-width: 992px) {
    .sidebar {
        width: 70px;
        overflow: hidden;
    }
    
    .sidebar-menu span {
        display: none;
    }
    
    .main-content {
        margin-left: 70px;
    }
    
    .dashboard-cards {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .main-content {
        margin-left: 0;
        padding-left: 80px;
    }
    
    .dashboard-cards {
        grid-template-columns: 1fr;
    }
}
