<?php
session_start();

// Vérifier si le coordinateur est connecté
if (!isset($_SESSION['user_id'])) {
    header("Location: login_coordinateur.php");
    exit();
}

// Connexion à la base de données
$host = 'localhost';
$dbname = 'gestion_coordinteur';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Créer également une connexion mysqli pour la compatibilité
    $conn = new mysqli($host, $username, $password, $dbname);
    if ($conn->connect_error) {
        throw new Exception("Erreur de connexion mysqli: " . $conn->connect_error);
    }
} catch (Exception $e) {
    die("Erreur de connexion : " . $e->getMessage());
}

// Récupération des informations du coordinateur
$id_coordinateur = $_SESSION['user_id'];
$user_type = $_SESSION['user_type'] ?? '';
$coordo = null;

// Chercher dans la table coordinateurs
$stmt = $pdo->prepare("SELECT * FROM coordinateurs WHERE id_coordinateur = ?");
$stmt->execute([$id_coordinateur]);
$coordo = $stmt->fetch();

// Si non trouvé, chercher dans la table utilisateurs
if (!$coordo && $user_type === 'coordinateur') {
    $stmt = $pdo->prepare("SELECT * FROM utilisateurs WHERE id = ? AND type_utilisateur = 'coordinateur'");
    $stmt->execute([$id_coordinateur]);
    $user = $stmt->fetch();

    if ($user) {
        $coordo = [
            'email' => $user['email'],
            'filiere' => 'Non définie',
            'annee_scolaire' => date('Y') . '-' . (date('Y') + 1)
        ];

        if (!empty($user['id_departement'])) {
            $stmt = $pdo->prepare("SELECT f.nom_filiere FROM filiere f WHERE f.id_departement = ? LIMIT 1");
            $stmt->execute([$user['id_departement']]);
            $filiere_result = $stmt->fetch();
            if ($filiere_result) {
                $coordo['filiere'] = $filiere_result['nom_filiere'];
            }
        }
    }
}

if (!$coordo) {
    echo "Aucun coordinateur trouvé.";
    exit();
}

// Récupérer les statistiques
$vacataires_count = $conn->query("SELECT COUNT(*) as total FROM vacataires")->fetch_assoc()['total'] ?? 0;
$ue_count = $conn->query("SELECT COUNT(*) as total FROM unites_enseignements")->fetch_assoc()['total'] ?? 0;
$affectations_count = $conn->query("SELECT COUNT(*) as total FROM affectations_vacataires")->fetch_assoc()['total'] ?? 0;
$creneaux_count = $conn->query("SELECT COUNT(*) as total FROM creneaux")->fetch_assoc()['total'] ?? 0;

// Données pour le graphique (exemple)
$dates = ['01/06', '02/06', '03/06', '04/06', '05/06', '06/06', '07/06'];
$visitCounts = [12, 19, 8, 15, 12, 9, 17];
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Coordinateur</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary: #6a11cb;
            --secondary: #5a0cb2;
            --light: #f5f7ff;
            --dark: #212529;
            --white: #ffffff;
        }
        body {
            background: linear-gradient(135deg, var(--light) 0%, #c3c7f7 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .header {
            background: linear-gradient(90deg, var(--primary) 0%, var(--secondary) 100%);
            color: white;
            padding: 2rem 1rem;
            margin-bottom: 2rem;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            margin-bottom: 1.5rem;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }
        .card-header {
            background: linear-gradient(90deg, var(--primary) 0%, var(--secondary) 100%);
            color: white;
            border-radius: 10px 10px 0 0 !important;
        }
        .stat-card {
            display: flex;
            align-items: center;
            padding: 1.5rem;
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            color: white;
            font-size: 1.5rem;
        }
        @media (max-width: 768px) {
            .main-content {
                flex-direction: column;
            }
            .left-menu, .right-content {
                width: 100%;
                margin-bottom: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- En-tête -->
    <div class="header text-center">
        <div class="container">
            <h1><i class="fas fa-tachometer-alt me-2"></i> Dashboard Coordinateur</h1>
        </div>
    </div>

    <!-- Contenu principal -->
    <div class="container">
        <div class="row">
            <!-- Menu gauche -->
            <div class="col-md-3 left-menu">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-bars me-2"></i>Menu</h5>
                    </div>
                    <div class="card-body">
                        <a href="gerer_groupes.php" class="btn btn-primary w-100 mb-2 text-start">
                            <i class="fas fa-users me-2"></i> Gérer les groupes
                        </a>
                        <a href="emplois_temps_complet.php" class="btn btn-primary w-100 mb-2 text-start">
                            <i class="fas fa-calendar-alt me-2"></i> Emplois du temps
                        </a>
                        <a href="affectation_vacataire.php" class="btn btn-primary w-100 mb-2 text-start">
                            <i class="fas fa-user-tie me-2"></i> Affecter vacataires
                        </a>
                        <a href="gestion_unites_enseignements.php" class="btn btn-primary w-100 mb-2 text-start">
                            <i class="fas fa-book me-2"></i> Unités d'enseignement
                        </a>
                        <a href="Extraire_D_Excel.php" class="btn btn-primary w-100 mb-2 text-start">
                            <i class="fas fa-file-excel me-2"></i> Exporter Excel
                        </a>
                        <button onclick="logout()" class="btn btn-danger w-100 mt-3 text-start">
                            <i class="fas fa-sign-out-alt me-2"></i> Déconnexion
                        </button>
                    </div>
                </div>
            </div>

            <!-- Contenu central -->
            <div class="col-md-6">
                <!-- Statistiques -->
                <div class="row mb-4">
                    <div class="col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div>
                                <h3 class="mb-0"><?= $vacataires_count ?></h3>
                                <p class="mb-0 text-muted">Vacataires</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-book"></i>
                            </div>
                            <div>
                                <h3 class="mb-0"><?= $ue_count ?></h3>
                                <p class="mb-0 text-muted">Unités d'enseignement</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-link"></i>
                            </div>
                            <div>
                                <h3 class="mb-0"><?= $affectations_count ?></h3>
                                <p class="mb-0 text-muted">Affectations</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-calendar-alt"></i>
                            </div>
                            <div>
                                <h3 class="mb-0"><?= $creneaux_count ?></h3>
                                <p class="mb-0 text-muted">Créneaux horaires</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Graphique -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Activité récente</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="activityChart" height="250"></canvas>
                    </div>
                </div>
            </div>

            <!-- Colonne droite -->
            <div class="col-md-3 right-content">
                <!-- Profil -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-user me-2"></i>Profil</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>Email :</strong> <?= htmlspecialchars($coordo['email']) ?></p>
                        <p><strong>Filière :</strong> <?= htmlspecialchars($coordo['filiere']) ?></p>
                        <p><strong>Année scolaire :</strong> <?= htmlspecialchars($coordo['annee_scolaire']) ?></p>
                    </div>
                </div>

                <!-- Calendrier -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-calendar me-2"></i>Calendrier</h5>
                    </div>
                    <div class="card-body">
                        <div id="mini-calendar"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Fonction de déconnexion
        function logout() {
            if (confirm("Voulez-vous vraiment vous déconnecter ?")) {
                window.location.href = "logout.php";
            }
        }

        // Graphique
        document.addEventListener('DOMContentLoaded', function() {
            const ctx = document.getElementById('activityChart').getContext('2d');
            const chart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: <?php echo json_encode($dates) ?>,
                    datasets: [{
                        label: 'Visites',
                        data: <?php echo json_encode($visitCounts) ?>,
                        backgroundColor: 'rgba(106, 17, 203, 0.2)',
                        borderColor: 'rgba(106, 17, 203, 1)',
                        borderWidth: 2,
                        tension: 0.3
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        });
    </script>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>