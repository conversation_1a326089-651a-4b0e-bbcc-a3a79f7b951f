<?php
session_start();

// Vérification de la session
if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] !== 'enseignant') {
    header('Location: login_coordinateur.php');
    exit();
}

try {
    $pdo = new PDO(
        "mysql:host=localhost;dbname=gestion_coordinteur;charset=utf8",
        "root",
        "",
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
} catch (PDOException $e) {
    die("Erreur de connexion : " . $e->getMessage());
}

// Récupération des informations - MODIFIÉ pour utiliser la table utilisateurs
// Débogage temporaire pour voir les variables de session disponibles
/*
echo "<pre>";
print_r($_SESSION);
echo "</pre>";
exit();
*/

// Récupérer l'ID utilisateur depuis la session
$id_utilisateur = $_SESSION['user_id'] ?? $_SESSION['id_utilisateur'] ?? $_SESSION['id'] ?? null;

// Si aucune de ces variables n'existe, utilisez le débogage ci-dessus pour voir ce qui est disponible
if (!$id_utilisateur) {
    $_SESSION['error'] = "Session invalide - ID utilisateur non trouvé";
    header('Location: login_coordinateur.php');
    exit();
}

$info = null;

try {
    // Version simplifiée - vérifiez d'abord si les champs nom/prenom sont dans la table utilisateurs
    $stmt = $pdo->prepare("
        SELECT u.*, s.nom_specialite
        FROM utilisateurs u
        LEFT JOIN specialite s ON u.id_specialite = s.id_specialite
        WHERE u.id = ? AND u.type_utilisateur = 'enseignant'
    ");
    $stmt->execute([$id_utilisateur]);
    $info = $stmt->fetch();

    if (!$info) {
        $_SESSION['error'] = "Profil introuvable";
        header('Location: login_coordinateur.php');
        exit();
    }

} catch (PDOException $e) {
    die("Erreur : " . $e->getMessage());
}

// Récupération des données complémentaires - CORRIGÉ
try {
    // Matières de l'enseignant connecté
    // Vérifier d'abord si la colonne id_utilisateur existe dans la table matieres
    $matieres_stmt = $pdo->prepare("
        SELECT m.code, m.nom, m.credit
        FROM matieres m
        WHERE m.id_utilisateur = ?
        ORDER BY m.code
    ");
    $matieres_stmt->execute([$id_utilisateur]);
    $matieres = $matieres_stmt->fetchAll();

    // Si aucune matière trouvée pour cet enseignant, afficher un message
    if (empty($matieres)) {
        // Optionnel : afficher toutes les matières ou un message vide
        $matieres = [];
    }

    // Groupes encadrés - simplifié car les tables de liaison peuvent ne pas exister
    $groupes = []; // Vide pour l'instant, à adapter selon votre structure
    
    // Emploi du temps - simplifié car les tables de liaison peuvent ne pas exister  
    $emploi = []; // Vide pour l'instant, à adapter selon votre structure

} catch (PDOException $e) {
    die("Erreur de chargement : " . $e->getMessage());
}

// Déconnexion
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: login_coordinateur.php');
    exit();
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Tableau de bord enseignant</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        :root {
            --couleur-principale: #1E90FF;
            --couleur-secondaire: #0a192f;
            --couleur-texte: #ffffff;
        }

        body {
            background: url('fond.png') no-repeat center center fixed;
            background-size: cover;
            min-height: 100vh;
            font-family: 'Segoe UI', system-ui, sans-serif;
        }

        .sidebar {
            width: 250px;
            background: rgba(10, 25, 47, 0.95);
            height: 100vh;
            position: fixed;
            padding: 20px;
            border-right: 2px solid var(--couleur-principale);
        }

        .main-content {
            margin-left: 250px;
            padding: 30px;
            color: var(--couleur-texte);
        }

        .card {
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(10px);
            border: 1px solid var(--couleur-principale);
        }

        table {
            --bs-table-color: var(--couleur-texte);
            --bs-table-border-color: #2d3a4f;
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="text-center mb-4">
            <img src="logo.png" alt="Logo établissement" class="img-fluid" style="max-width: 150px;">
        </div>

        <nav class="nav flex-column">
            <a class="nav-link text-white mb-2" href="souhaits_enseignants.php">Mes souhaits</a>
            <a class="nav-link text-white mb-2" href="emploi_du_temps.php">Emploi du temps</a>
            <a class="nav-link text-white mb-2" href="notes.php">Gestion des notes</a>
            <a class="nav-link text-white mb-2" href="modules.php">Mes modules</a>
            <a class="nav-link text-white mb-2" href="profil.php">Mon profil</a>
            <a class="nav-link text-danger mt-4" href="?logout=true">Déconnexion</a>
        </nav>
    </div>

    <div class="main-content">
        <div class="container-fluid">
            <div class="card mb-4 p-4">
                <h1 class="mb-3">Bienvenue <?= htmlspecialchars($info['prenom'] . ' ' . $info['nom']) ?></h1>
                <div class="row">
                    <div class="col-md-6">
                        <p class="mb-1"><strong>Email :</strong> <?= htmlspecialchars($info['email']) ?></p>
                    </div>
                    <div class="col-md-6">
                        <p class="mb-1"><strong>Spécialité :</strong> <?= htmlspecialchars($info['nom_specialite'] ?? 'Non spécifiée') ?></p>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-4 mb-4">
                    <div class="card h-100 p-3">
                        <h4 class="mb-3">Matières enseignées</h4>
                        <ul class="list-group list-group-flush">
                            <?php foreach ($matieres as $matiere): ?>
                            <li class="list-group-item bg-transparent text-white">
                                <?= htmlspecialchars($matiere['code']) ?> - <?= htmlspecialchars($matiere['nom']) ?>
                            </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                </div>

                <div class="col-lg-8 mb-4">
                    <div class="card h-100 p-3">
                        <h4 class="mb-3">Emploi du temps</h4>
                        <div class="table-responsive">
                            <table class="table align-middle">
                                <thead>
                                    <tr>
                                        <th>Jour</th>
                                        <th>Horaire</th>
                                        <th>Groupe</th>
                                        <th>Salle</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($emploi as $cours): ?>
                                    <tr>
                                        <td><?= htmlspecialchars($cours['jour']) ?></td>
                                        <td><?= $cours['debut'] ?> - <?= $cours['fin'] ?></td>
                                        <td><?= htmlspecialchars($cours['groupe']) ?></td>
                                        <td><?= htmlspecialchars($cours['salle']) ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>