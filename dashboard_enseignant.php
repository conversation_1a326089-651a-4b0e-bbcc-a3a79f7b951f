<?php
session_start();

// Vérification de la session
if (!isset($_SESSION['user_type']) {
    header('Location: login_coordinateur.php');
    exit();
}

if ($_SESSION['user_type'] !== 'enseignant') {
    header('Location: login_coordinateur.php');
    exit();
}

if (!isset($_SESSION['id_enseignant'])) {
    $_SESSION['error'] = "Session invalide";
    header('Location: login_coordinateur.php');
    exit();
}

// Connexion à la base de données
try {
    $pdo = new PDO(
        "mysql:host=localhost;dbname=gestion_coordinteur;charset=utf8",
        "root",
        "",
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
} catch (PDOException $e) {
    die("Erreur de connexion : " . $e->getMessage());
}

// Récupération des informations
$id_enseignant = $_SESSION['id_enseignant'];
$info = null;

try {
    // Requête optimisée avec jointure
    $stmt = $pdo->prepare("
        SELECT e.*, s.nom_specialite 
        FROM enseignants e
        LEFT JOIN specialite s ON e.id_specialite = s.id_specialite
        WHERE e.id_enseignant = ?
    ");
    $stmt->execute([$id_enseignant]);
    $info = $stmt->fetch();

    if (!$info) {
        $_SESSION['error'] = "Profil introuvable";
        header('Location: login_coordinateur.php');
        exit();
    }

} catch (PDOException $e) {
    die("Erreur : " . $e->getMessage());
}

// Récupération des données complémentaires
try {
    // Matières enseignées
    $matieres_stmt = $pdo->prepare("
        SELECT m.code, m.nom 
        FROM enseignants_matieres em
        JOIN matieres m ON em.id_matiere = m.id_matiere
        WHERE em.id_enseignant = ?
    ");
    $matieres_stmt->execute([$id_enseignant]);
    $matieres = $matieres_stmt->fetchAll();

    // Groupes encadrés
    $groupes_stmt = $pdo->prepare("
        SELECT g.nom, g.type, g.filiere, g.niveau, ge.role
        FROM groupes_enseignants ge
        JOIN groupes g ON ge.id_groupe = g.id_groupe
        WHERE ge.id_enseignant = ?
    ");
    $groupes_stmt->execute([$id_enseignant]);
    $groupes = $groupes_stmt->fetchAll();

    // Emploi du temps
    $emploi_stmt = $pdo->prepare("
        SELECT c.jour, 
               TIME_FORMAT(c.heure_debut, '%H:%i') AS debut,
               TIME_FORMAT(c.heure_fin, '%H:%i') AS fin,
               g.nom AS groupe,
               IFNULL(s.nom, 'Non attribuée') AS salle
        FROM enseignants_creneaux ec
        JOIN creneaux c ON ec.id_creneau = c.id_creneau
        JOIN groupes g ON c.id_groupe = g.id_groupe
        LEFT JOIN salles s ON c.id_salle = s.id_salle
        WHERE ec.id_enseignant = ?
        ORDER BY FIELD(c.jour, 'Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi'),
               c.heure_debut
    ");
    $emploi_stmt->execute([$id_enseignant]);
    $emploi = $emploi_stmt->fetchAll();

} catch (PDOException $e) {
    die("Erreur de chargement : " . $e->getMessage());
}

// Déconnexion
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: login_coordinateur.php');
    exit();
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Tableau de bord enseignant</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        :root {
            --couleur-principale: #1E90FF;
            --couleur-secondaire: #0a192f;
            --couleur-texte: #ffffff;
        }

        body {
            background: url('fond.png') no-repeat center center fixed;
            background-size: cover;
            min-height: 100vh;
            font-family: 'Segoe UI', system-ui, sans-serif;
        }

        .sidebar {
            width: 250px;
            background: rgba(10, 25, 47, 0.95);
            height: 100vh;
            position: fixed;
            padding: 20px;
            border-right: 2px solid var(--couleur-principale);
        }

        .main-content {
            margin-left: 250px;
            padding: 30px;
            color: var(--couleur-texte);
        }

        .card {
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(10px);
            border: 1px solid var(--couleur-principale);
        }

        table {
            --bs-table-color: var(--couleur-texte);
            --bs-table-border-color: #2d3a4f;
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <div class="text-center mb-4">
            <img src="logo.png" alt="Logo établissement" class="img-fluid" style="max-width: 150px;">
        </div>
        
        <nav class="nav flex-column">
            <a class="nav-link text-white mb-2" href="souhaits_enseignants.php">Mes souhaits</a>
            <a class="nav-link text-white mb-2" href="emploi_du_temps.php">Emploi du temps</a>
            <a class="nav-link text-white mb-2" href="notes.php">Gestion des notes</a>
            <a class="nav-link text-white mb-2" href="modules.php">Mes modules</a>
            <a class="nav-link text-white mb-2" href="profil.php">Mon profil</a>
            <a class="nav-link text-danger mt-4" href="?logout=true">Déconnexion</a>
        </nav>
    </div>

    <div class="main-content">
        <div class="container-fluid">
            <div class="card mb-4 p-4">
                <h1 class="mb-3">Bienvenue <?= htmlspecialchars($info['prenom'] . ' ' . htmlspecialchars($info['nom']) ?></h1>
                <div class="row">
                    <div class="col-md-6">
                        <p class="mb-1"><strong>Email :</strong> <?= htmlspecialchars($info['email']) ?></p>
                    </div>
                    <div class="col-md-6">
                        <p class="mb-1"><strong>Spécialité :</strong> <?= htmlspecialchars($info['nom_specialite'] ?? 'Non spécifiée') ?></p>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-lg-4 mb-4">
                    <div class="card h-100 p-3">
                        <h4 class="mb-3">Matières enseignées</h4>
                        <ul class="list-group list-group-flush">
                            <?php foreach ($matieres as $matiere): ?>
                            <li class="list-group-item bg-transparent text-white">
                                <?= htmlspecialchars($matiere['code']) ?> - <?= htmlspecialchars($matiere['nom']) ?>
                            </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                </div>

                <div class="col-lg-8 mb-4">
                    <div class="card h-100 p-3">
                        <h4 class="mb-3">Emploi du temps</h4>
                        <div class="table-responsive">
                            <table class="table align-middle">
                                <thead>
                                    <tr>
                                        <th>Jour</th>
                                        <th>Horaire</th>
                                        <th>Groupe</th>
                                        <th>Salle</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($emploi as $cours): ?>
                                    <tr>
                                        <td><?= htmlspecialchars($cours['jour']) ?></td>
                                        <td><?= $cours['debut'] ?> - <?= $cours['fin'] ?></td>
                                        <td><?= htmlspecialchars($cours['groupe']) ?></td>
                                        <td><?= htmlspecialchars($cours['salle']) ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>