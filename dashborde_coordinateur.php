<?php
session_start();

// Vérifier si l'utilisateur est connecté et est un coordinateur
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'coordinateur') {
    header("Location: login_coordinateur.php");
    exit;
}

// Données fictives pour le tableau de bord
$vacataires = 2;
$unites_enseignement = 7;
$affectations = 5;
$creneaux = 1;

// Inclure le script d'enregistrement des visites
require_once 'record_page_visit.php';

// Enregistrer la visite de la page
recordPageVisit('dashborde_coordinateur.php', 'coordinateur');

// Récupérer les statistiques de visite mensuelles pour l'année en cours
$visites = getMonthlyVisitStats('dashborde_coordinateur.php', date('Y'), 'coordinateur');

// Noms des mois en français
$mois = ['Jan', 'Fév', 'Mar', 'Avr', '<PERSON>', 'Juin', 'Juil', 'Août', 'Sep', 'Oct', 'Nov', 'Déc'];

// Informations personnelles
$email = $_SESSION['email'] ?? '<EMAIL>';
$filiere = $_SESSION['filiere'] ?? 'Informatique';
$annee_scolaire = $_SESSION['annee_scolaire'] ?? '2024-2025';
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Coordinateur</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary-color: #6a0dad;
            --secondary-color: #8a2be2;
            --light-purple: #e6e6fa;
            --dark-purple: #4b0082;
            --accent-color: #00bfff;
        }

        body {
            background-color: #f0f0f5;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
        }

        .header {
            background-color: var(--primary-color);
            color: white;
            padding: 20px;
            display: flex;
            align-items: center;
        }

        .header img {
            width: 50px;
            height: 50px;
            margin-right: 15px;
        }

        .header h1 {
            margin: 0;
            font-size: 24px;
        }

        .content {
            display: flex;
            padding: 20px;
            gap: 20px;
        }

        .sidebar {
            width: 220px;
            background-color: var(--light-purple);
            border-radius: 10px;
            padding: 15px;
        }

        .sidebar-title {
            background-color: var(--primary-color);
            color: white;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            font-weight: bold;
        }

        .sidebar-menu {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .menu-item {
            display: flex;
            align-items: center;
            background-color: #fff;
            padding: 10px;
            border-radius: 5px;
            color: var(--dark-purple);
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .menu-item:hover {
            background-color: var(--secondary-color);
            color: white;
            transform: translateX(5px);
        }

        .menu-item i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }

        .logout-btn {
            margin-top: auto;
            background-color: #ff4757;
            color: white;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .stats-cards {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .stat-card {
            background-color: #fff;
            border-radius: 10px;
            padding: 15px;
            display: flex;
            align-items: center;
            width: 200px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: white;
        }

        .vacataires-icon {
            background-color: var(--primary-color);
        }

        .ue-icon {
            background-color: var(--secondary-color);
        }

        .affectations-icon {
            background-color: var(--accent-color);
        }

        .creneaux-icon {
            background-color: #ff6b6b;
        }

        .stat-info {
            display: flex;
            flex-direction: column;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }

        .stat-label {
            font-size: 12px;
            color: #777;
        }

        .chart-container {
            background-color: #fff;
            border-radius: 10px;
            padding: 20px 20px 30px 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            max-height: 320px;
            overflow: hidden;
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .chart-title {
            color: var(--dark-purple);
            font-weight: bold;
            display: flex;
            align-items: center;
        }

        .chart-title i {
            margin-right: 10px;
            color: var(--primary-color);
        }

        .chart-tabs {
            display: flex;
            gap: 10px;
        }

        .chart-tab {
            padding: 5px 10px;
            border-radius: 5px;
            cursor: pointer;
            background-color: #f0f0f5;
        }

        .chart-tab.active {
            background-color: var(--primary-color);
            color: white;
        }

        .info-card {
            background-color: #fff;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .info-title {
            color: var(--dark-purple);
            font-weight: bold;
            margin-bottom: 15px;
        }

        .info-item {
            margin-bottom: 10px;
        }

        .info-label {
            font-weight: bold;
            color: #555;
        }

        .calendar {
            background-color: var(--primary-color);
            color: white;
            border-radius: 10px;
            overflow: hidden;
        }

        .calendar-header {
            background-color: var(--secondary-color);
            padding: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .calendar-title {
            font-weight: bold;
        }

        .calendar-body {
            padding: 15px;
        }

        .calendar-month {
            text-align: center;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 5px;
        }

        .calendar-day {
            text-align: center;
            padding: 5px;
            border-radius: 5px;
        }

        .calendar-day.header {
            font-weight: bold;
            background-color: transparent;
        }

        .calendar-day.active {
            background-color: white;
            color: var(--primary-color);
            font-weight: bold;
        }

        .calendar-day.today {
            background-color: var(--accent-color);
            color: white;
        }

        .right-sidebar {
            width: 300px;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
    </style>
</head>
<body>
    <div class="header">
        <img src="https://img.icons8.com/color/48/000000/dashboard.png" alt="Dashboard Icon">
        <h1>Bienvenue Coordinateur</h1>
    </div>

    <div class="content">
        <div class="sidebar">
            <div class="sidebar-title">Liens rapides</div>
            <div class="sidebar-menu">
                <a href="gerer_groupes.php" class="menu-item">
                    <i class="fas fa-users"></i> Gérer les groupes
                </a>
                <a href="#" class="menu-item">
                    <i class="fas fa-calendar-alt"></i> Gérer les emplois du temps
                </a>
                <a href="#" class="menu-item">
                    <i class="fas fa-user-tie"></i> Affectation des vacataires
                </a>
                <a href="gestion_unites_enseignements.php" class="menu-item">
                    <i class="fas fa-book"></i> Unités d'enseignement
                </a>
                <a href="#" class="menu-item">
                    <i class="fas fa-file-excel"></i> Extraire en Excel
                </a>
                <a href="logout.php" class="menu-item logout-btn">
                    <i class="fas fa-sign-out-alt"></i> Déconnexion
                </a>
            </div>
        </div>

        <div class="main-content">
            <div class="stats-cards">
                <div class="stat-card">
                    <div class="stat-icon vacataires-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-number"><?= $vacataires ?></div>
                        <div class="stat-label">Vacataires</div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon ue-icon">
                        <i class="fas fa-book"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-number"><?= $unites_enseignement ?></div>
                        <div class="stat-label">Unités d'enseignement</div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon affectations-icon">
                        <i class="fas fa-link"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-number"><?= $affectations ?></div>
                        <div class="stat-label">Affectations</div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon creneaux-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-info">
                        <div class="stat-number"><?= $creneaux ?></div>
                        <div class="stat-label">Créneaux horaires</div>
                    </div>
                </div>
            </div>

            <div class="chart-container">
                <div class="chart-header">
                    <div class="chart-title">
                        <i class="fas fa-chart-line"></i> Statistiques de visites mensuelles (<?= date('Y') ?>)
                    </div>
                    <div class="chart-tabs">
                        <div class="chart-tab active">Lignes</div>
                        <div class="chart-tab">Colonnes</div>
                    </div>
                </div>
                <canvas id="visitsChart" height="180"></canvas>
            </div>
        </div>

        <div class="right-sidebar">
            <div class="info-card">
                <div class="info-title">Informations personnelles</div>
                <div class="info-item">
                    <span class="info-label">Email :</span> <?= htmlspecialchars($email) ?>
                </div>
                <div class="info-item">
                    <span class="info-label">Filière :</span> <?= htmlspecialchars($filiere) ?>
                </div>
                <div class="info-item">
                    <span class="info-label">Année scolaire :</span> <?= htmlspecialchars($annee_scolaire) ?>
                </div>
            </div>

            <div class="calendar">
                <div class="calendar-header">
                    <div class="calendar-title">Calendrier</div>
                    <i class="fas fa-plus-circle"></i>
                </div>
                <div class="calendar-body">
                    <div class="calendar-month">Juin 2024</div>
                    <div class="calendar-grid">
                        <div class="calendar-day header">Di</div>
                        <div class="calendar-day header">Lu</div>
                        <div class="calendar-day header">Ma</div>
                        <div class="calendar-day header">Me</div>
                        <div class="calendar-day header">Je</div>
                        <div class="calendar-day header">Ve</div>
                        <div class="calendar-day header">Sa</div>

                        <!-- Jours du mois -->
                        <div class="calendar-day">27</div>
                        <div class="calendar-day">28</div>
                        <div class="calendar-day">29</div>
                        <div class="calendar-day">30</div>
                        <div class="calendar-day">31</div>
                        <div class="calendar-day">1</div>
                        <div class="calendar-day">2</div>

                        <div class="calendar-day">3</div>
                        <div class="calendar-day">4</div>
                        <div class="calendar-day">5</div>
                        <div class="calendar-day">6</div>
                        <div class="calendar-day">7</div>
                        <div class="calendar-day">8</div>
                        <div class="calendar-day">9</div>

                        <div class="calendar-day">10</div>
                        <div class="calendar-day">11</div>
                        <div class="calendar-day">12</div>
                        <div class="calendar-day">13</div>
                        <div class="calendar-day">14</div>
                        <div class="calendar-day">15</div>
                        <div class="calendar-day">16</div>

                        <div class="calendar-day">17</div>
                        <div class="calendar-day">18</div>
                        <div class="calendar-day">19</div>
                        <div class="calendar-day">20</div>
                        <div class="calendar-day today">21</div>
                        <div class="calendar-day">22</div>
                        <div class="calendar-day">23</div>

                        <div class="calendar-day">24</div>
                        <div class="calendar-day active">25</div>
                        <div class="calendar-day">26</div>
                        <div class="calendar-day">27</div>
                        <div class="calendar-day">28</div>
                        <div class="calendar-day">29</div>
                        <div class="calendar-day">30</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialisation du graphique
        const ctx = document.getElementById('visitsChart').getContext('2d');
        const visitsChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: <?= json_encode($mois) ?>,
                datasets: [{
                    label: 'Nombre de visites',
                    data: <?= json_encode($visites) ?>,
                    backgroundColor: 'rgba(106, 13, 173, 0.2)',
                    borderColor: 'rgba(106, 13, 173, 1)',
                    borderWidth: 2,
                    pointBackgroundColor: 'white',
                    pointBorderColor: 'rgba(106, 13, 173, 1)',
                    pointRadius: 6,
                    pointHoverRadius: 8,
                    tension: 0.3
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                layout: {
                    padding: {
                        left: 10,
                        right: 10,
                        top: 0,
                        bottom: 20
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)'
                        },
                        ticks: {
                            font: {
                                size: 10
                            }
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            font: {
                                size: 12
                            },
                            color: '#333',
                            padding: 10,
                            autoSkip: false,
                            maxRotation: 0
                        },
                        display: true
                    }
                },
                plugins: {
                    legend: {
                        display: true,
                        position: 'top',
                        labels: {
                            font: {
                                size: 11
                            }
                        }
                    }
                }
            }
        });

        // Gestion des onglets du graphique
        document.querySelectorAll('.chart-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelector('.chart-tab.active').classList.remove('active');
                this.classList.add('active');

                // Changer le type de graphique
                if (this.textContent === 'Colonnes') {
                    visitsChart.config.type = 'bar';
                } else {
                    visitsChart.config.type = 'line';
                }
                visitsChart.update();
            });
        });
    </script>
</body>
</html>
