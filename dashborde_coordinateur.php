<?php
session_start();

// Vérifier si l'utilisateur est connecté et est un coordinateur
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'coordinateur') {
    header("Location: login_coordinateur.php");
    exit;
}

// Données fictives pour le tableau de bord
$vacataires = 2;
$unites_enseignement = 7;
$affectations = 5;
$creneaux = 1;

// Inclure le script d'enregistrement des visites
require_once 'record_page_visit.php';

// Enregistrer la visite de la page
recordPageVisit('dashborde_coordinateur.php', 'coordinateur');

// Récupérer les statistiques de visite mensuelles pour l'année en cours
$visites = getMonthlyVisitStats('dashborde_coordinateur.php', date('Y'), 'coordinateur');

// Noms des mois en français
$mois = ['Jan', 'Fév', 'Mar', 'Avr', '<PERSON>', 'Juin', 'Juil', 'Août', 'Sep', 'Oct', 'Nov', 'Déc'];

// Informations personnelles
$email = $_SESSION['email'] ?? '<EMAIL>';
$filiere = $_SESSION['filiere'] ?? 'Informatique';
$annee_scolaire = $_SESSION['annee_scolaire'] ?? '2024-2025';
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Coordinateur</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/dashboard_style.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-logo">
            <img src="images/logo.png" alt="Logo" onerror="this.src='https://img.icons8.com/color/48/000000/dashboard.png'">
        </div>
        <div class="header-title">
            Bienvenue Coordinateur
        </div>
    </header>

    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h3>Menu Principal</h3>
        </div>
        <ul class="sidebar-menu">
            <li>
                <a href="dashborde_coordinateur.php" class="active">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Tableau de bord</span>
                </a>
            </li>
            <li>
                <a href="gerer_groupes.php">
                    <i class="fas fa-users"></i>
                    <span>Gérer les groupes</span>
                </a>
            </li>
            <li>
                <a href="emplois_du_temps.php">
                    <i class="fas fa-calendar-alt"></i>
                    <span>Emplois du temps</span>
                </a>
            </li>
            <li>
                <a href="affectation_vacataires.php">
                    <i class="fas fa-user-tie"></i>
                    <span>Affectation des vacataires</span>
                </a>
            </li>
            <li>
                <a href="gestion_unites_enseignements.php">
                    <i class="fas fa-book"></i>
                    <span>Unités d'enseignement</span>
                </a>
            </li>
            <li>
                <a href="export_excel.php">
                    <i class="fas fa-file-excel"></i>
                    <span>Extraire en Excel</span>
                </a>
            </li>
            <li>
                <a href="logout.php" class="btn-logout">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Déconnexion</span>
                </a>
            </li>
        </ul>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Dashboard Cards -->
        <div class="dashboard-cards">
            <div class="card">
                <div class="card-icon purple">
                    <i class="fas fa-users"></i>
                </div>
                <div class="card-number"><?= $vacataires ?></div>
                <div class="card-title">Vacataires</div>
            </div>

            <div class="card">
                <div class="card-icon blue">
                    <i class="fas fa-book"></i>
                </div>
                <div class="card-number"><?= $unites_enseignement ?></div>
                <div class="card-title">Unités d'enseignement</div>
            </div>

            <div class="card">
                <div class="card-icon blue">
                    <i class="fas fa-link"></i>
                </div>
                <div class="card-number"><?= $affectations ?></div>
                <div class="card-title">Affectations</div>
            </div>

            <div class="card">
                <div class="card-icon pink">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="card-number"><?= $creneaux ?></div>
                <div class="card-title">Créneaux horaires</div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-8">
                <!-- Statistics Chart -->
                <div class="card">
                    <h3>Statistiques de visites mensuelles (<?= date('Y') ?>)</h3>
                    <div>
                        <canvas id="visitsChart" height="250"></canvas>
                    </div>
                    <div class="chart-legend">
                        <button class="btn btn-primary" id="viewLines">Lignes</button>
                        <button class="btn btn-secondary" id="viewColumns">Colonnes</button>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <!-- Personal Information -->
                <div class="info-card">
                    <h3>Informations personnelles</h3>
                    <div class="info-item">
                        <span class="info-label">Email :</span>
                        <span><?= htmlspecialchars($email) ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Filière :</span>
                        <span><?= htmlspecialchars($filiere) ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Année scolaire :</span>
                        <span><?= htmlspecialchars($annee_scolaire) ?></span>
                    </div>
                </div>

                <!-- Calendar -->
                <div class="calendar-card">
                    <div class="calendar-header">
                        <h3>Calendrier</h3>
                        <i class="fas fa-info-circle"></i>
                    </div>
                    <div id="calendar">
                        <h4 class="text-center">Juin 2024</h4>
                        <div class="calendar-grid">
                            <div class="calendar-day header">Di</div>
                            <div class="calendar-day header">Lu</div>
                            <div class="calendar-day header">Ma</div>
                            <div class="calendar-day header">Me</div>
                            <div class="calendar-day header">Je</div>
                            <div class="calendar-day header">Ve</div>
                            <div class="calendar-day header">Sa</div>

                            <!-- Calendar days -->
                            <div class="calendar-day">27</div>
                            <div class="calendar-day">28</div>
                            <div class="calendar-day">29</div>
                            <div class="calendar-day">30</div>
                            <div class="calendar-day">31</div>
                            <div class="calendar-day">1</div>
                            <div class="calendar-day">2</div>

                            <div class="calendar-day">3</div>
                            <div class="calendar-day">4</div>
                            <div class="calendar-day">5</div>
                            <div class="calendar-day">6</div>
                            <div class="calendar-day">7</div>
                            <div class="calendar-day">8</div>
                            <div class="calendar-day">9</div>

                            <div class="calendar-day">10</div>
                            <div class="calendar-day">11</div>
                            <div class="calendar-day">12</div>
                            <div class="calendar-day">13</div>
                            <div class="calendar-day">14</div>
                            <div class="calendar-day">15</div>
                            <div class="calendar-day">16</div>

                            <div class="calendar-day">17</div>
                            <div class="calendar-day">18</div>
                            <div class="calendar-day">19</div>
                            <div class="calendar-day">20</div>
                            <div class="calendar-day today">21</div>
                            <div class="calendar-day">22</div>
                            <div class="calendar-day">23</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Links -->
        <div class="quick-links">
            <h3>Liens rapides</h3>
            <ul class="quick-links-list">
                <li>
                    <a href="gerer_groupes.php">
                        <i class="fas fa-users"></i>
                        Gérer les groupes
                    </a>
                </li>
                <li>
                    <a href="emplois_du_temps.php">
                        <i class="fas fa-calendar-alt"></i>
                        Gérer les emplois du temps
                    </a>
                </li>
                <li>
                    <a href="affectation_vacataires.php">
                        <i class="fas fa-user-tie"></i>
                        Affectation des vacataires
                    </a>
                </li>
                <li>
                    <a href="gestion_unites_enseignements.php">
                        <i class="fas fa-book"></i>
                        Unités d'enseignement
                    </a>
                </li>
            </ul>
        </div>
    </div>

    <script>
        // Chart initialization
        document.addEventListener('DOMContentLoaded', function() {
            var ctx = document.getElementById('visitsChart').getContext('2d');
            var chart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: <?= json_encode($mois) ?>,
                    datasets: [{
                        label: 'Nombre de visites',
                        data: <?= json_encode($visites) ?>,
                        backgroundColor: 'rgba(142, 36, 170, 0.2)',
                        borderColor: '#8e24aa',
                        borderWidth: 2,
                        pointBackgroundColor: '#8e24aa',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // Toggle chart type
            document.getElementById('viewLines').addEventListener('click', function() {
                chart.config.type = 'line';
                chart.update();
            });

            document.getElementById('viewColumns').addEventListener('click', function() {
                chart.config.type = 'bar';
                chart.update();
            });
        });
    </script>
</body>
</html>
