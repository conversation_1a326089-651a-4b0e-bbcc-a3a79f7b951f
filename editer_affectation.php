<?php
// 1. Configuration et initialisation
require __DIR__ . '/config.php';

// 2. Gestion de session
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// 3. Protection CSRF
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}
$csrf_token = $_SESSION['csrf_token'];

// 4. Variables initiales
$error = null;
$success = null;
$affectation = null;
$professeurs = [];
$unites_enseignement = [];

// 5. Vérification de l'ID dans l'URL
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: affectation_ue.php');
    exit;
}

$affectation_id = (int)$_GET['id'];

try {
    $pdo = new PDO(
        'mysql:host='.DB_HOST.';dbname='.DB_NAME.';charset=utf8mb4',
        DB_USER,
        DB_PASSWORD,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );

    // Récupération de l'affectation à éditer
    $stmt = $pdo->prepare("
        SELECT a.id, 
               a.professeur_id,
               a.id as ue_id,
               a.annee, 
               a.semestre,
               a.date_debut,
               a.date_fin,
               a.heures
        FROM affectations a
        WHERE a.id = ?
    ");
    $stmt->execute([$affectation_id]);
    $affectation = $stmt->fetch();

    if (!$affectation) {
        header('Location: affectation_ue.php');
        exit;
    }

    // Récupération des professeurs
    $professeurs = $pdo->query("
        SELECT id, CONCAT(nom, ' ', prenom) as nom_complet 
        FROM professeurs
        ORDER BY nom, prenom
    ")->fetchAll();

    // Récupération des UE
    $unites_enseignement = $pdo->query("
        SELECT id, CONCAT(code_ue, ' - ', intitule) as nom_complet, code_ue
        FROM unites_enseignement
        ORDER BY code_ue
    ")->fetchAll();

    // Traitement du formulaire de mise à jour
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['modifier_affectation'])) {
        try {
            // Validation CSRF
            if (empty($_POST['csrf_token']) || !hash_equals($csrf_token, $_POST['csrf_token'])) {
                throw new Exception("Erreur de sécurité: Token invalide");
            }

            // Validation des données
            $required = [
                'professeur_id' => 'Professeur',
                'ue_id' => 'Unité d\'enseignement',
                'annee' => 'Année',
                'semestre' => 'Semestre',
                'date_debut' => 'Date de début',
                'heures' => 'Nombre d\'heures'
            ];
            
            $errors = [];
            foreach ($required as $field => $name) {
                if (empty($_POST[$field])) {
                    $errors[] = "Le champ '$name' est requis";
                }
            }

            if (!empty($errors)) {
                throw new Exception(implode("<br>", $errors));
            }

            // Mise à jour de l'affectation
            $stmt = $pdo->prepare("
                UPDATE affectations 
                SET professeur_id = :professeur_id,
                    id = :ue_id,
                    annee = :annee,
                    semestre = :semestre,
                    date_debut = :date_debut,
                    date_fin = :date_fin,
                    heures = :heures,
                    utilisateur_id = :utilisateur_id
                WHERE id = :id
            ");
            
            $data = [
                'id' => $affectation_id,
                'professeur_id' => (int)$_POST['professeur_id'],
                'ue_id' => (int)$_POST['ue_id'],
                'annee' => (int)$_POST['annee'],
                'semestre' => (int)$_POST['semestre'],
                'date_debut' => $_POST['date_debut'],
                'date_fin' => $_POST['date_fin'] ?? null,
                'heures' => (int)$_POST['heures'],
                'utilisateur_id' => $_SESSION['user_id']
            ];
            
            if ($stmt->execute($data)) {
                $_SESSION['success'] = "Affectation modifiée avec succès!";
                header("Location: affectation_ue.php");
                exit;
            }

        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    }

} catch (PDOException $e) {
    $error = "Erreur de base de données: " . $e->getMessage();
    error_log("DB Error: " . $e->getMessage());
}

function sanitize($data) {
    return htmlspecialchars($data ?? '', ENT_QUOTES, 'UTF-8');
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Éditer Affectation UE</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #1E90FF;
            --primary-magenta: #FF00FF;
            --blue-transparent: rgba(30, 144, 255, 0.3);
            --dark-bg: #0a192f;
        }

        body {
            background: linear-gradient(rgba(10, 25, 47, 0.85), rgba(108, 27, 145, 0.85));
            color: white;
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .card {
            background: rgba(10, 25, 47, 0.9);
            border: 2px solid var(--primary-blue);
            border-radius: 10px;
            box-shadow: 0 5px 15px var(--blue-transparent);
        }

        .card-header {
            background: linear-gradient(90deg, var(--primary-blue) 0%, var(--dark-bg) 100%);
            border-bottom: 2px solid var(--primary-magenta);
        }

        .form-control, .form-select {
            background-color: rgba(10, 25, 47, 0.7);
            color: white;
            border: 1px solid var(--primary-blue);
        }

        .form-control:focus, .form-select:focus {
            background-color: rgba(10, 25, 47, 0.9);
            color: white;
            border-color: var(--primary-magenta);
            box-shadow: 0 0 0 0.25rem rgba(255, 0, 255, 0.25);
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h3 class="m-0"><i class="fas fa-edit me-2"></i> Éditer Affectation</h3>
                    </div>
                    <div class="card-body">
                        <?php if ($error): ?>
                            <div class="alert alert-danger"><?= sanitize($error) ?></div>
                        <?php endif; ?>
                        
                        <form method="POST">
                            <input type="hidden" name="csrf_token" value="<?= sanitize($csrf_token) ?>">
                            
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label class="form-label">Professeur *</label>
                                    <select class="form-select" name="professeur_id" required>
                                        <option value="">Sélectionner...</option>
                                        <?php foreach ($professeurs as $prof): ?>
                                            <option value="<?= $prof['id'] ?>" <?= $prof['id'] == $affectation['professeur_id'] ? 'selected' : '' ?>>
                                                <?= sanitize($prof['nom_complet']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="col-md-6">
                                    <label class="form-label">Unité d'Enseignement *</label>
                                    <select class="form-select" name="ue_id" required>
                                        <option value="">Sélectionner...</option>
                                        <?php foreach ($unites_enseignement as $ue): ?>
                                            <option value="<?= $ue['id'] ?>" <?= $ue['id'] == $affectation['ue_id'] ? 'selected' : '' ?>>
                                                <?= sanitize($ue['nom_complet']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="col-md-3">
                                    <label class="form-label">Année *</label>
                                    <select class="form-select" name="annee" required>
                                        <?php $currentYear = date('Y'); ?>
                                        <?php for ($i = $currentYear - 1; $i <= $currentYear + 2; $i++): ?>
                                            <option value="<?= $i ?>" <?= $i == $affectation['annee'] ? 'selected' : '' ?>><?= $i ?></option>
                                        <?php endfor; ?>
                                    </select>
                                </div>
                                
                                <div class="col-md-3">
                                    <label class="form-label">Semestre *</label>
                                    <select class="form-select" name="semestre" required>
                                        <option value="1" <?= $affectation['semestre'] == 1 ? 'selected' : '' ?>>Semestre 1</option>
                                        <option value="2" <?= $affectation['semestre'] == 2 ? 'selected' : '' ?>>Semestre 2</option>
                                    </select>
                                </div>
                                
                                <div class="col-md-3">
                                    <label class="form-label">Date début *</label>
                                    <input type="date" class="form-control" name="date_debut" required 
                                           value="<?= htmlspecialchars($affectation['date_debut']) ?>">
                                </div>
                                
                                <div class="col-md-3">
                                    <label class="form-label">Date fin</label>
                                    <input type="date" class="form-control" name="date_fin" 
                                           value="<?= htmlspecialchars($affectation['date_fin'] ?? '') ?>">
                                </div>
                                
                                <div class="col-md-2">
                                    <label class="form-label">Heures *</label>
                                    <input type="number" class="form-control" name="heures" required min="1" 
                                           value="<?= htmlspecialchars($affectation['heures']) ?>">
                                </div>
                            </div>
                            
                            <div class="mt-4 d-flex justify-content-between">
                                <a href="affectation_ue.php" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left me-2"></i> Retour
                                </a>
                                <button type="submit" name="modifier_affectation" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i> Enregistrer
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>