<?php
// Script pour ajouter les colonnes manquantes à la table affectations
require_once 'config.php';

// Afficher les erreurs pour le débogage
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Correction de la table affectations</h1>";

try {
    // Connexion à la base de données
    $pdo = new PDO(
        "mysql:host=".DB_HOST.";dbname=".DB_NAME.";charset=utf8",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    // Vérifier si la table affectations existe
    $stmt = $pdo->query("SHOW TABLES LIKE 'affectations'");
    $tableExists = $stmt->rowCount() > 0;
    
    if ($tableExists) {
        echo "<p style='color:green'>La table 'affectations' existe dans la base de données.</p>";
        
        // Vérifier les colonnes existantes
        $stmt = $pdo->query("DESCRIBE affectations");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        echo "<p>Colonnes existantes : " . implode(", ", $columns) . "</p>";
        
        // Colonnes à vérifier
        $requiredColumns = [
            'id' => 'INT AUTO_INCREMENT PRIMARY KEY',
            'professeur_id' => 'INT NOT NULL',
            'ue_id' => 'INT NOT NULL',
            'annee' => 'INT DEFAULT ' . date('Y'),
            'semestre' => 'INT DEFAULT 1',
            'date_debut' => 'DATE NOT NULL',
            'date_fin' => 'DATE',
            'heures' => 'INT NOT NULL DEFAULT 30',
            'utilisateur_id' => 'INT',
            'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
            'updated_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
        ];
        
        // Vérifier chaque colonne requise
        $missingColumns = [];
        foreach ($requiredColumns as $column => $definition) {
            if (!in_array($column, $columns)) {
                $missingColumns[$column] = $definition;
            }
        }
        
        if (empty($missingColumns)) {
            echo "<p style='color:green'>Toutes les colonnes requises existent dans la table 'affectations'.</p>";
        } else {
            echo "<p style='color:orange'>Colonnes manquantes dans la table 'affectations' : " . implode(", ", array_keys($missingColumns)) . "</p>";
            
            // Proposer d'ajouter les colonnes manquantes
            echo "<form method='post'>";
            echo "<input type='hidden' name='action' value='add_columns'>";
            echo "<button type='submit' style='padding: 10px 20px; background-color: #28a745; color: white; border: none; border-radius: 5px; cursor: pointer;'>Ajouter les colonnes manquantes</button>";
            echo "</form>";
            
            if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'add_columns') {
                try {
                    // Ajouter chaque colonne manquante
                    foreach ($missingColumns as $column => $definition) {
                        $pdo->exec("ALTER TABLE affectations ADD COLUMN $column $definition");
                        echo "<p style='color:green'>La colonne '$column' a été ajoutée à la table 'affectations' avec succès.</p>";
                    }
                    
                    echo "<p>Veuillez rafraîchir la page pour voir les changements.</p>";
                } catch (PDOException $e) {
                    echo "<p style='color:red'>Erreur lors de l'ajout des colonnes: " . $e->getMessage() . "</p>";
                }
            }
        }
        
    } else {
        echo "<p style='color:red'>La table 'affectations' n'existe pas dans la base de données.</p>";
        
        // Proposer de créer la table
        echo "<form method='post'>";
        echo "<input type='hidden' name='action' value='create_table'>";
        echo "<button type='submit' style='padding: 10px 20px; background-color: #28a745; color: white; border: none; border-radius: 5px; cursor: pointer;'>Créer la table 'affectations'</button>";
        echo "</form>";
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'create_table') {
            try {
                // Créer la table affectations
                $pdo->exec("
                    CREATE TABLE affectations (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        professeur_id INT NOT NULL,
                        ue_id INT NOT NULL,
                        annee INT DEFAULT " . date('Y') . ",
                        semestre INT DEFAULT 1,
                        date_debut DATE NOT NULL,
                        date_fin DATE,
                        heures INT NOT NULL DEFAULT 30,
                        utilisateur_id INT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                    )
                ");
                echo "<p style='color:green'>La table 'affectations' a été créée avec succès.</p>";
                echo "<p>Veuillez rafraîchir la page pour voir les changements.</p>";
            } catch (PDOException $e) {
                echo "<p style='color:red'>Erreur lors de la création de la table: " . $e->getMessage() . "</p>";
            }
        }
    }
    
    // Ajouter un lien vers affectation_ue.php
    echo "<p><a href='affectation_ue.php' style='padding: 10px 20px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px;'>Retour à la page des affectations</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color:red'>Erreur de connexion à la base de données: " . $e->getMessage() . "</p>";
}
?>
