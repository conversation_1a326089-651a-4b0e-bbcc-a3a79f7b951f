<?php
$conn = new mysqli("localhost", "root", "", "gestion_coordinteur");

if ($conn->connect_error) {
    die("Erreur de connexion: " . $conn->connect_error);
}

$groupes = $conn->query("SELECT * FROM groupes WHERE actif = 1");
$salles = $conn->query("SELECT * FROM salles");
$creneaux = $conn->query("SELECT * FROM creneaux");

// Fonction pour valider et nettoyer les entrées
function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

// Fonction pour vérifier les conflits de créneaux
function check_creneau_conflict($conn, $id_salle, $jour, $heure_debut, $heure_fin, $id_creneau = null) {
    $condition = $id_creneau ? "AND id_creneau != $id_creneau" : "";

    $query = "SELECT * FROM creneaux
              WHERE id_salle = ?
              AND jour = ?
              AND ((heure_debut <= ? AND heure_fin > ?)
                  OR (heure_debut < ? AND heure_fin >= ?)
                  OR (heure_debut >= ? AND heure_fin <= ?))
              $condition";

    $stmt = $conn->prepare($query);
    $stmt->bind_param("isssssss", $id_salle, $jour, $heure_fin, $heure_debut, $heure_fin, $heure_debut, $heure_debut, $heure_fin);
    $stmt->execute();
    $result = $stmt->get_result();

    return $result->num_rows > 0;
}

// Initialisation des variables pour les messages
$success_message = "";
$error_message = "";

// Traitement de l'ajout d'un créneau
if (isset($_POST['ajouter'])) {
    // Récupération et nettoyage des données
    $id_groupe = sanitize_input($_POST['id_groupe']);
    $id_salle = sanitize_input($_POST['id_salle']);
    $jour = sanitize_input($_POST['jour']);
    $heure_debut = sanitize_input($_POST['heure_debut']);
    $heure_fin = sanitize_input($_POST['heure_fin']);
    $frequence = sanitize_input($_POST['frequence']);

    // Validation des données
    if (empty($id_groupe) || empty($id_salle) || empty($jour) || empty($heure_debut) || empty($heure_fin) || empty($frequence)) {
        $error_message = "Tous les champs sont obligatoires.";
    } elseif ($heure_debut >= $heure_fin) {
        $error_message = "L'heure de début doit être antérieure à l'heure de fin.";
    } elseif (check_creneau_conflict($conn, $id_salle, $jour, $heure_debut, $heure_fin)) {
        $error_message = "Ce créneau est en conflit avec un autre créneau existant pour cette salle.";
    } else {
        // Préparation et exécution de la requête
        $stmt = $conn->prepare("INSERT INTO creneaux (id_groupe, id_salle, jour, heure_debut, heure_fin, frequence)
                               VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->bind_param("iissss", $id_groupe, $id_salle, $jour, $heure_debut, $heure_fin, $frequence);

        if ($stmt->execute()) {
            $success_message = "Créneau ajouté avec succès.";
            header("Location: gerer_emplois_temps.php?page=liste&success=1");
            exit();
        } else {
            $error_message = "Erreur lors de l'ajout du créneau: " . $stmt->error;
        }
    }
}

// Traitement de la modification d'un créneau
if (isset($_POST['modifier'])) {
    // Récupération et nettoyage des données
    $id = sanitize_input($_POST['id_creneau']);
    $id_groupe = sanitize_input($_POST['id_groupe']);
    $id_salle = sanitize_input($_POST['id_salle']);
    $jour = sanitize_input($_POST['jour']);
    $heure_debut = sanitize_input($_POST['heure_debut']);
    $heure_fin = sanitize_input($_POST['heure_fin']);
    $frequence = sanitize_input($_POST['frequence']);

    // Validation des données
    if (empty($id) || empty($id_groupe) || empty($id_salle) || empty($jour) || empty($heure_debut) || empty($heure_fin) || empty($frequence)) {
        $error_message = "Tous les champs sont obligatoires.";
    } elseif ($heure_debut >= $heure_fin) {
        $error_message = "L'heure de début doit être antérieure à l'heure de fin.";
    } elseif (check_creneau_conflict($conn, $id_salle, $jour, $heure_debut, $heure_fin, $id)) {
        $error_message = "Ce créneau est en conflit avec un autre créneau existant pour cette salle.";
    } else {
        // Préparation et exécution de la requête
        $stmt = $conn->prepare("UPDATE creneaux SET id_groupe=?, id_salle=?, jour=?,
                              heure_debut=?, heure_fin=?, frequence=?
                              WHERE id_creneau=?");
        $stmt->bind_param("iissssi", $id_groupe, $id_salle, $jour, $heure_debut, $heure_fin, $frequence, $id);

        if ($stmt->execute()) {
            $success_message = "Créneau modifié avec succès.";
            header("Location: gerer_emplois_temps.php?page=liste&success=2");
            exit();
        } else {
            $error_message = "Erreur lors de la modification du créneau: " . $stmt->error;
        }
    }
}

// Traitement de la suppression d'un créneau
if (isset($_POST['supprimer'])) {
    $id = sanitize_input($_POST['id_creneau']);

    // Préparation et exécution de la requête
    $stmt = $conn->prepare("DELETE FROM creneaux WHERE id_creneau=?");
    $stmt->bind_param("i", $id);

    if ($stmt->execute()) {
        $success_message = "Créneau supprimé avec succès.";
        header("Location: gerer_emplois_temps.php?page=liste&success=3");
        exit();
    } else {
        $error_message = "Erreur lors de la suppression du créneau: " . $stmt->error;
    }
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion des emplois du temps</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #6a11cb;
            --secondary-color: #5a0cb2;
            --light-bg: #f5f7ff;
            --input-bg: #f5f7ff;
            --border-color: #e0e0ff;
            --text-color: #000000;
            --white: #ffffff;
            --error-color: #ff4757;
            --accent-color: #d9d2ff;
            --blue-transparent: rgba(106, 17, 203, 0.3);
        }

        body {
            background: linear-gradient(135deg, #e8f0fe 0%, #d8d0f9 100%);
            color: var(--text-color);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            position: relative;
        }

        body::before {
            content: "";
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 40%;
            background: linear-gradient(0deg, #8a4fff 0%, transparent 100%);
            border-radius: 50% 50% 0 0 / 100% 100% 0 0;
            z-index: -1;
        }

        body::after {
            content: "";
            position: absolute;
            top: -10%;
            right: -10%;
            width: 40%;
            height: 40%;
            background-color: rgba(138, 79, 255, 0.2);
            border-radius: 50%;
            z-index: -1;
        }

        .container {
            background-color: rgba(255, 255, 255, 0.9);
            padding: 30px;
            border-radius: 10px;
            border: 1px solid #8a4fff;
            box-shadow: 0 0 20px rgba(138, 79, 255, 0.3);
            animation: cardBorderPulse 10s infinite;
            margin-bottom: 20px;
            max-width: 800px;
        }

        @keyframes cardBorderPulse {
            0% { border-color: #8a4fff; box-shadow: 0 5px 15px rgba(138, 79, 255, 0.3); }
            50% { border-color: #6a11cb; box-shadow: 0 5px 20px rgba(106, 17, 203, 0.3); }
            100% { border-color: #8a4fff; box-shadow: 0 5px 15px rgba(138, 79, 255, 0.3); }
        }

        h1.text-center, h3 {
            color: var(--primary-color);
            text-shadow: 0 0 10px var(--blue-transparent);
            animation: textPulse 5s infinite;
        }

        @keyframes textPulse {
            0% { text-shadow: 0 0 10px var(--primary-color); }
            50% { text-shadow: 0 0 15px var(--secondary-color); }
            100% { text-shadow: 0 0 10px var(--primary-color); }
        }

        nav {
            background-color: rgba(10, 25, 47, 0.9);
            color: var(--white);
            border-right: 1px solid var(--primary-color);
            box-shadow: 0 0 20px var(--blue-transparent);
            animation: sidebarBorderPulse 8s infinite alternate;
        }

        @keyframes sidebarBorderPulse {
            0% { border-right-color: var(--primary-color); }
            50% { border-right-color: var(--secondary-color); }
            100% { border-right-color: var(--primary-color); }
        }

        nav h4 {
            font-weight: bold;
            margin-bottom: 20px;
            color: var(--primary-color);
        }

        .nav-link {
            color: var(--white);
            text-decoration: none;
            font-size: 1.1rem;
            display: block;
            padding: 10px;
            transition: all 0.3s ease;
            border-radius: 5px;
            border: 1px solid transparent;
        }

        .nav-link:hover {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            transform: translateX(5px);
            box-shadow: 0 5px 15px var(--blue-transparent);
            color: var(--white);
        }

        .form-label {
            font-weight: bold;
            color: var(--primary-color);
        }

        .form-control, .form-select {
            background-color: #f5f7ff;
            border: 1px solid #e0e0ff;
            color: #333;
            border-radius: 8px;
            transition: all 0.3s ease;
            padding: 0.6rem 1rem;
        }

        .form-control:focus, .form-select:focus {
            background-color: #ffffff;
            border-color: #8a4fff;
            box-shadow: 0 0 0 0.25rem rgba(138, 79, 255, 0.25);
            color: #333;
        }

        .btn-success, .btn-primary {
            background: #8a4fff;
            border: none;
            border-radius: 8px;
            font-weight: bold;
            transition: all 0.3s ease;
            padding: 0.6rem 1.5rem;
            color: white;
        }

        .btn-success:hover, .btn-primary:hover {
            background: #6a11cb;
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(138, 79, 255, 0.3);
        }

        .table {
            background-color: rgba(255, 255, 255, 0.95);
            color: #333;
            border-radius: 10px;
            overflow: hidden;
            border: 1px solid #e0e0ff;
            box-shadow: 0 5px 15px rgba(138, 79, 255, 0.2);
        }

        .table thead {
            background: #8a4fff !important;
            color: white !important;
            border-bottom: 1px solid #6a11cb !important;
        }

        .table td, .table th {
            vertical-align: middle !important;
            border-bottom: 1px solid rgba(106, 17, 203, 0.3);
        }

        .table tr:hover td {
            background-color: rgba(142, 129, 155, 0.2);
        }

        .btn:hover {
            transform: scale(1.05);
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffc107 0%, var(--secondary-color) 100%);
            border: none;
        }

        .btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, var(--secondary-color) 100%);
            border: none;
        }

        table input, table select {
            min-width: 120px;
            background-color: rgba(106, 17, 203, 0.1);
            border: 1px solid var(--primary-color);
            color: var(--white);
        }

        @media (max-width: 768px) {
            .d-flex {
                flex-direction: column;
            }

            nav {
                width: 100%;
                min-height: auto;
                position: relative;
                border-right: none;
                border-bottom: 1px solid var(--primary-color);
            }

            .container {
                padding: 1rem;
            }
        }

        .image-size {
            width: 200px;
            height: auto;
            border-radius: 10px;
            border: 1px solid var(--primary-color);
        }

        .image-size:hover {
            transform: scale(1.1);
            filter: brightness(1.2);
            box-shadow: 0 0 15px var(--blue-transparent);
        }

        /* Animations */
        @keyframes headerGlow {
            0% { box-shadow: 0 2px 15px rgba(106, 17, 203, 0.1); }
            50% { box-shadow: 0 2px 15px rgba(106, 17, 203, 0.3); }
            100% { box-shadow: 0 2px 15px rgba(106, 17, 203, 0.1); }
        }

        /* Améliorations UI */
        .header-container {
            background: transparent;
            padding: 1.5rem 0;
            margin-bottom: 2rem;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
            border: none;
            box-shadow: none;
        }

        .page-title {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: #8a4fff;
            text-align: center;
        }

        .breadcrumb {
            background-color: rgba(106, 17, 203, 0.1);
            padding: 0.5rem 1rem;
            border-radius: 5px;
            margin-bottom: 1.5rem;
        }

        .breadcrumb-item a {
            color: var(--primary-color);
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .breadcrumb-item a:hover {
            color: var(--secondary-color);
            text-decoration: underline;
        }

        .breadcrumb-item.active {
            color: var(--white);
        }

        .card {
            background-color: rgba(255, 255, 255, 0.95);
            border: 1px solid #e0e0ff;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(138, 79, 255, 0.2);
            margin-bottom: 1.5rem;
            transition: all 0.3s ease;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(138, 79, 255, 0.3);
        }

        .card-header {
            background: #8a4fff;
            color: white;
            border-bottom: 1px solid #6a11cb;
            border-radius: 10px 10px 0 0 !important;
            padding: 1rem;
        }

        .card-body {
            padding: 1.5rem;
        }

        .form-group {
            margin-bottom: 1.2rem;
        }

        .btn {
            padding: 0.5rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border: none;
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px var(--blue-transparent);
        }

        .btn-outline-primary {
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
            background: transparent;
        }

        .btn-outline-primary:hover {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: var(--white);
            border-color: transparent;
        }

        /* Tooltip personnalisé */
        .custom-tooltip {
            position: relative;
            display: inline-block;
        }

        .custom-tooltip .tooltip-text {
            visibility: hidden;
            width: 200px;
            background-color: rgba(10, 25, 47, 0.95);
            color: var(--white);
            text-align: center;
            border-radius: 6px;
            padding: 10px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            transform: translateX(-50%);
            opacity: 0;
            transition: opacity 0.3s;
            border: 1px solid var(--primary-color);
            box-shadow: 0 5px 15px var(--blue-transparent);
        }

        .custom-tooltip .tooltip-text::after {
            content: "";
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: var(--primary-color) transparent transparent transparent;
        }

        .custom-tooltip:hover .tooltip-text {
            visibility: visible;
            opacity: 1;
        }

        /* Badge personnalisé */
        .custom-badge {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: var(--white);
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            display: inline-block;
            margin-left: 0.5rem;
        }

        /* Pagination améliorée */
        .pagination {
            display: flex;
            justify-content: center;
            margin-top: 1.5rem;
        }

        .pagination .page-item .page-link {
            background-color: rgba(10, 25, 47, 0.9);
            color: var(--white);
            border: 1px solid var(--primary-color);
            margin: 0 3px;
            border-radius: 5px;
            transition: all 0.3s ease;
        }

        .pagination .page-item .page-link:hover {
            background-color: var(--primary-color);
            color: var(--white);
            transform: translateY(-2px);
            box-shadow: 0 5px 10px var(--blue-transparent);
        }

        .pagination .page-item.active .page-link {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border-color: var(--secondary-color);
        }

        /* Loader */
        .loader-container {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(10, 25, 47, 0.8);
            z-index: 9999;
            justify-content: center;
            align-items: center;
        }

        .loader {
            width: 50px;
            height: 50px;
            border: 5px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Amélioration des alertes */
        .alert {
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1.5rem;
            border: none;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .alert::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 5px;
            height: 100%;
        }

        .alert-success {
            background-color: rgba(40, 167, 69, 0.1);
            color: #28a745;
        }

        .alert-success::before {
            background-color: #28a745;
        }

        .alert-danger {
            background-color: rgba(220, 53, 69, 0.1);
            color: #dc3545;
        }

        .alert-danger::before {
            background-color: #dc3545;
        }

        .alert-info {
            background-color: rgba(106, 17, 203, 0.1);
            color: var(--primary-color);
        }

        .alert-info::before {
            background-color: var(--primary-color);
        }

        /* Amélioration des tableaux */
        .table-container {
            overflow-x: auto;
            border-radius: 10px;
            box-shadow: 0 5px 15px var(--blue-transparent);
        }

        .table-hover tbody tr:hover {
            background-color: rgba(106, 17, 203, 0.1);
            transform: scale(1.01);
            transition: all 0.3s ease;
        }

        .table-actions {
            display: flex;
            gap: 5px;
            justify-content: center;
        }

        .action-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
        }

        /* Statistiques */
        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: linear-gradient(135deg, #ffffff 0%, #f5f7ff 100%);
            border: 1px solid #e0e0ff;
            border-radius: 10px;
            padding: 1.2rem;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(138, 79, 255, 0.2);
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(138, 79, 255, 0.3);
        }

        .stat-card:first-child {
            background: linear-gradient(135deg, #8a4fff 0%, #6a11cb 100%);
        }

        .stat-card:first-child .stat-info h3,
        .stat-card:first-child .stat-info p {
            color: white;
        }

        .stat-card:first-child .stat-icon {
            background: white;
            color: #8a4fff;
        }

        .stat-card:nth-child(2) {
            background: linear-gradient(135deg, #6a11cb 0%, #5a0cb2 100%);
        }

        .stat-card:nth-child(2) .stat-info h3,
        .stat-card:nth-child(2) .stat-info p {
            color: white;
        }

        .stat-card:nth-child(2) .stat-icon {
            background: white;
            color: #6a11cb;
        }

        .stat-card:nth-child(3) {
            background: linear-gradient(135deg, #5a0cb2 0%, #4a0a92 100%);
        }

        .stat-card:nth-child(3) .stat-info h3,
        .stat-card:nth-child(3) .stat-info p {
            color: white;
        }

        .stat-card:nth-child(3) .stat-icon {
            background: white;
            color: #5a0cb2;
        }

        .stat-card:nth-child(4) {
            background: linear-gradient(135deg, #3a0872 0%, #2a0652 100%);
            border: 1px solid #d9d2ff;
            position: relative;
            overflow: hidden;
        }

        .stat-card:nth-child(4)::before {
            content: "";
            position: absolute;
            top: -10px;
            right: -10px;
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            z-index: 0;
        }

        .stat-card:nth-child(4) .stat-info h3,
        .stat-card:nth-child(4) .stat-info p {
            color: white;
            position: relative;
            z-index: 1;
        }

        .stat-card:nth-child(4) .stat-icon {
            background: white;
            color: #3a0872;
            position: relative;
            z-index: 1;
            box-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
        }

        .stat-card:nth-child(2) .stat-info h3,
        .stat-card:nth-child(2) .stat-info p {
            color: white;
        }

        .stat-card:nth-child(2) .stat-icon {
            background: white;
            color: #6a11cb;
        }

        .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #8a4fff;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            font-size: 1.2rem;
            color: white;
        }

        .stat-info h3 {
            font-size: 1.5rem;
            margin: 0 0 5px;
            color: #333;
            font-weight: bold;
        }

        .stat-info p {
            margin: 0;
            color: #666;
            font-size: 0.9rem;
        }

        /* Scrollbar personnalisée */
        ::-webkit-scrollbar {
            width: 10px;
            height: 10px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(10, 25, 47, 0.9);
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 5px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--secondary-color);
        }
    </style>
</head>
<body>

<div class="d-flex">
    <nav class="text-white p-3" style="width: 250px; min-height: 100vh;">
        <div class="text-center mb-4">
            <img src="image.jpg" class="image-size">
            <h4 class="mt-3">Menu</h4>
        </div>

        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link text-white <?= (!isset($_GET['page']) || $_GET['page'] == 'ajouter') ? 'active' : '' ?>"
                   href="gerer_emplois_temps.php?page=ajouter">
                   <i class="fas fa-plus-circle me-2"></i> Ajouter un créneau
                </a>
            </li>
            <li class="nav-item mt-2">
                <a class="nav-link text-white <?= (isset($_GET['page']) && $_GET['page'] == 'liste') ? 'active' : '' ?>"
                   href="gerer_emplois_temps.php?page=liste">
                   <i class="fas fa-list-alt me-2"></i> Liste des créneaux
                </a>
            </li>
            <li class="nav-item mt-2">
                <a class="nav-link text-white" href="logout.php">
                   <i class="fas fa-sign-out-alt me-2"></i> Déconnexion
                </a>
            </li>
        </ul>

        <div class="mt-5">
            <div class="nav-divider"></div>
            <div class="text-center mt-4 text-white-50">
                <p><i class="fas fa-calendar-check"></i> Gestion des emplois du temps</p>
                <small>Version 2.0</small>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Loader pour les actions asynchrones -->
        <div class="loader-container" id="loader">
            <div class="loader"></div>
        </div>

        <div class="header-container">
            <h1 class="page-title">📅 Gestion des Emplois du Temps</h1>
        </div>

        <!-- Fil d'Ariane -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="index.php">Accueil</a></li>
                <?php if (!isset($_GET['page']) || $_GET['page'] == 'ajouter'): ?>
                    <li class="breadcrumb-item active" aria-current="page">Ajouter un créneau</li>
                <?php elseif ($_GET['page'] == 'liste'): ?>
                    <li class="breadcrumb-item active" aria-current="page">Liste des créneaux</li>
                <?php endif; ?>
            </ol>
        </nav>

        <!-- Statistiques -->
        <div class="stats-container">
            <?php
            // Compter le nombre total de créneaux
            $total_creneaux = $conn->query("SELECT COUNT(*) as total FROM creneaux")->fetch_assoc()['total'];

            // Compter le nombre de salles
            $total_salles = $conn->query("SELECT COUNT(*) as total FROM salles")->fetch_assoc()['total'];

            // Compter le nombre de groupes actifs
            $total_groupes = $conn->query("SELECT COUNT(*) as total FROM groupes WHERE actif = 1")->fetch_assoc()['total'];

            // Compter le nombre de créneaux par jour (pour le jour le plus chargé)
            $jour_plus_charge = $conn->query("SELECT jour, COUNT(*) as total FROM creneaux GROUP BY jour ORDER BY total DESC LIMIT 1")->fetch_assoc();
            ?>

            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-calendar-check"></i>
                </div>
                <div class="stat-info">
                    <h3><?php echo $total_creneaux; ?></h3>
                    <p>Créneaux programmés</p>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-door-open"></i>
                </div>
                <div class="stat-info">
                    <h3><?php echo $total_salles; ?></h3>
                    <p>Salles disponibles</p>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-info">
                    <h3><?php echo $total_groupes; ?></h3>
                    <p>Groupes actifs</p>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stat-info">
                    <h3><?php echo isset($jour_plus_charge['total']) ? $jour_plus_charge['total'] : '0'; ?></h3>
                    <p>Créneaux le <?php echo isset($jour_plus_charge['jour']) ? $jour_plus_charge['jour'] : 'N/A'; ?></p>
                </div>
            </div>
        </div>

        <!-- Affichage des messages de succès -->
        <?php if (isset($_GET['success'])): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php
                    switch($_GET['success']) {
                        case 1:
                            echo "Le créneau a été ajouté avec succès.";
                            break;
                        case 2:
                            echo "Le créneau a été modifié avec succès.";
                            break;
                        case 3:
                            echo "Le créneau a été supprimé avec succès.";
                            break;
                        default:
                            echo "Opération réussie.";
                    }
                ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <!-- Affichage des messages d'erreur -->
        <?php if (!empty($error_message)): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo $error_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <?php if (!isset($_GET['page']) || $_GET['page'] == 'ajouter'): ?>
            <div class="card" style="max-width: 800px; margin: 0 auto;">
                <div class="card-header">
                    <h3 class="mb-0" style="font-size: 1.5rem;"><i class="fas fa-plus-circle me-2"></i>Ajouter un créneau</h3>
                </div>
                <div class="card-body">
                    <form method="POST" class="row g-3" id="addCreneauForm">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-users me-1"></i> Groupe
                                    <span class="custom-tooltip">
                                        <i class="fas fa-info-circle text-white"></i>
                                        <span class="tooltip-text">Sélectionnez le groupe d'étudiants pour ce créneau</span>
                                    </span>
                                </label>
                                <select name="id_groupe" class="form-select" required>
                                    <option value="" disabled selected>Choisir un groupe...</option>
                                    <?php
                                    // Réinitialiser le pointeur de résultat
                                    $groupes->data_seek(0);
                                    while($g = $groupes->fetch_assoc()):
                                    ?>
                                        <option value="<?= $g['id_groupe'] ?>"><?= $g['nom'] ?> - <?= $g['filiere'] ?> (<?= $g['niveau'] ?>)</option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-door-open me-1"></i> Salle
                                    <span class="custom-tooltip">
                                        <i class="fas fa-info-circle text-white"></i>
                                        <span class="tooltip-text">Sélectionnez la salle pour ce créneau</span>
                                    </span>
                                </label>
                                <select name="id_salle" class="form-select" required id="salleSelect">
                                    <option value="" disabled selected>Choisir une salle...</option>
                                    <?php
                                    // Réinitialiser le pointeur de résultat
                                    $salles->data_seek(0);
                                    while($s = $salles->fetch_assoc()):
                                    ?>
                                        <option value="<?= $s['id_salle'] ?>"><?= $s['nom'] ?> (<?= $s['type'] ?>)</option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-calendar-day me-1"></i> Jour
                                </label>
                                <select name="jour" class="form-select" required>
                                    <option value="" disabled selected>Choisir un jour...</option>
                                    <option value="Lundi">Lundi</option>
                                    <option value="Mardi">Mardi</option>
                                    <option value="Mercredi">Mercredi</option>
                                    <option value="Jeudi">Jeudi</option>
                                    <option value="Vendredi">Vendredi</option>
                                    <option value="Samedi">Samedi</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-clock me-1"></i> Heure de début
                                </label>
                                <input type="time" name="heure_debut" class="form-control" required id="heureDebut">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-hourglass-end me-1"></i> Heure de fin
                                </label>
                                <input type="time" name="heure_fin" class="form-control" required id="heureFin">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-sync-alt me-1"></i> Fréquence
                                    <span class="custom-tooltip">
                                        <i class="fas fa-info-circle text-white"></i>
                                        <span class="tooltip-text">Hebdo: chaque semaine, Bimensuel: toutes les deux semaines, Ponctuel: une seule fois</span>
                                    </span>
                                </label>
                                <select name="frequence" class="form-select" required>
                                    <option value="" disabled selected>Choisir une fréquence...</option>
                                    <option value="Hebdo">Hebdomadaire</option>
                                    <option value="Bimensuel">Bimensuel</option>
                                    <option value="Ponctuel">Ponctuel</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" name="ajouter" class="btn btn-primary w-100" id="btnAjouter">
                                <i class="fas fa-plus-circle me-2"></i>Ajouter
                            </button>
                        </div>

                        <div class="col-12 mt-4">
                            <div class="alert alert-info" role="alert">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Vérification automatique :</strong> Le système vérifiera automatiquement les conflits de salles avant d'ajouter le créneau.
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Aperçu des disponibilités -->
            <div class="card mt-4" style="max-width: 800px; margin: 0 auto;">
                <div class="card-header">
                    <h3 class="mb-0" style="font-size: 1.5rem;"><i class="fas fa-calendar-alt me-2"></i>Aperçu des disponibilités</h3>
                </div>
                <div class="card-body">
                    <div class="table-container">
                        <table class="table table-bordered table-sm">
                            <thead class="table-dark">
                                <tr>
                                    <th>Horaire</th>
                                    <th>Lundi</th>
                                    <th>Mardi</th>
                                    <th>Mercredi</th>
                                    <th>Jeudi</th>
                                    <th>Vendredi</th>
                                    <th>Samedi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $heures = ['08:00', '09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00', '17:00'];
                                foreach ($heures as $heure) {
                                    echo "<tr>";
                                    echo "<td><strong>$heure</strong></td>";

                                    $jours = ['Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi'];
                                    foreach ($jours as $jour) {
                                        // Vérifier si des créneaux existent à cette heure et ce jour
                                        $heure_fin = date('H:i', strtotime($heure . ' +1 hour'));
                                        $query = "SELECT COUNT(*) as count FROM creneaux
                                                WHERE jour = '$jour'
                                                AND ((heure_debut <= '$heure' AND heure_fin > '$heure')
                                                OR (heure_debut < '$heure_fin' AND heure_fin >= '$heure_fin')
                                                OR (heure_debut >= '$heure' AND heure_fin <= '$heure_fin'))";
                                        $result = $conn->query($query);
                                        $row = $result->fetch_assoc();

                                        if ($row['count'] > 0) {
                                            echo "<td class='bg-danger text-white text-center'><i class='fas fa-times'></i> Occupé</td>";
                                        } else {
                                            echo "<td class='bg-success text-white text-center'><i class='fas fa-check'></i> Libre</td>";
                                        }
                                    }

                                    echo "</tr>";
                                }
                                ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        <?php elseif ($_GET['page'] == 'liste'): ?>
            <div class="card" style="max-width: 800px; margin: 0 auto;">
                <div class="card-header">
                    <h3 class="mb-0" style="font-size: 1.5rem;"><i class="fas fa-list-alt me-2"></i>Liste des créneaux</h3>
                </div>
                <div class="card-body">
                    <!-- Barre de recherche et filtres -->
                    <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="input-group">
                            <input type="text" id="searchInput" class="form-control" placeholder="Rechercher...">
                            <button class="btn btn-outline-primary" type="button">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-8">
                        <div class="d-flex justify-content-end gap-2">
                            <select id="filterJour" class="form-select form-select-sm">
                                <option value="">Tous les jours</option>
                                <option>Lundi</option>
                                <option>Mardi</option>
                                <option>Mercredi</option>
                                <option>Jeudi</option>
                                <option>Vendredi</option>
                                <option>Samedi</option>
                            </select>
                            <select id="filterFrequence" class="form-select form-select-sm">
                                <option value="">Toutes les fréquences</option>
                                <option>Hebdo</option>
                                <option>Bimensuel</option>
                                <option>Ponctuel</option>
                            </select>
                            <button id="resetFilters" class="btn btn-sm btn-outline-secondary">
                                <i class="fas fa-undo"></i> Réinitialiser
                            </button>
                        </div>
                    </div>
                </div>

                <div class="table-responsive">
                    <table id="creneauxTable" class="table table-bordered table-sm">
                    <thead class="table-dark">
                        <tr>
                            <th>ID</th>
                            <th>Groupe</th>
                            <th>Salle</th>
                            <th>Jour</th>
                            <th>Début</th>
                            <th>Fin</th>
                            <th>Fréquence</th>
                            <th>Modifier</th>
                            <th>Supprimer</th>
                        </tr>
                    </thead>
                    <tbody>
                    <?php while($c = $creneaux->fetch_assoc()): ?>
                        <tr>
                            <form method="POST" class="align-middle">
                                <td><input type="text" name="id_creneau" value="<?= $c['id_creneau'] ?>" readonly class="form-control form-control-sm"></td>

                                <td>
                                    <select name="id_groupe" class="form-select form-select-sm">
                                        <?php
                                        $groupes_reload = $conn->query("SELECT * FROM groupes WHERE actif = 1");
                                        while($g = $groupes_reload->fetch_assoc()):
                                            $selected = ($g['id_groupe'] == $c['id_groupe']) ? "selected" : "";
                                        ?>
                                            <option value="<?= $g['id_groupe'] ?>" <?= $selected ?>><?= $g['nom'] ?> - <?= $g['filiere'] ?> (<?= $g['niveau'] ?>)</option>
                                        <?php endwhile; ?>
                                    </select>
                                </td>

                                <td>
                                    <select name="id_salle" class="form-select form-select-sm">
                                        <?php
                                        $salles_reload = $conn->query("SELECT * FROM salles");
                                        while($s = $salles_reload->fetch_assoc()):
                                            $selected = ($s['id_salle'] == $c['id_salle']) ? "selected" : "";
                                        ?>
                                            <option value="<?= $s['id_salle'] ?>" <?= $selected ?>><?= $s['nom'] ?> (<?= $s['type'] ?>)</option>
                                        <?php endwhile; ?>
                                    </select>
                                </td>

                                <td>
                                    <select name="jour" class="form-select form-select-sm">
                                        <option <?= $c['jour'] == 'Lundi' ? 'selected' : '' ?>>Lundi</option>
                                        <option <?= $c['jour'] == 'Mardi' ? 'selected' : '' ?>>Mardi</option>
                                        <option <?= $c['jour'] == 'Mercredi' ? 'selected' : '' ?>>Mercredi</option>
                                        <option <?= $c['jour'] == 'Jeudi' ? 'selected' : '' ?>>Jeudi</option>
                                        <option <?= $c['jour'] == 'Vendredi' ? 'selected' : '' ?>>Vendredi</option>
                                        <option <?= $c['jour'] == 'Samedi' ? 'selected' : '' ?>>Samedi</option>
                                    </select>
                                </td>
                                <td><input type="time" name="heure_debut" value="<?= $c['heure_debut'] ?>" class="form-control form-control-sm"></td>
                                <td><input type="time" name="heure_fin" value="<?= $c['heure_fin'] ?>" class="form-control form-control-sm"></td>
                                <td>
                                    <select name="frequence" class="form-select form-select-sm">
                                        <option <?= $c['frequence'] == 'Hebdo' ? 'selected' : '' ?>>Hebdo</option>
                                        <option <?= $c['frequence'] == 'Bimensuel' ? 'selected' : '' ?>>Bimensuel</option>
                                        <option <?= $c['frequence'] == 'Ponctuel' ? 'selected' : '' ?>>Ponctuel</option>
                                    </select>
                                </td>
                                <td><button name="modifier" class="btn btn-warning btn-sm"><i class="fas fa-edit"></i> Modifier</button></td>
                                <td><button name="supprimer" class="btn btn-danger btn-sm" onclick="return confirm('Êtes-vous sûr de vouloir supprimer ce créneau ?');"><i class="fas fa-trash-alt"></i> Supprimer</button></td>
                            </form>
                        </tr>
                    <?php endwhile; ?>
                    </tbody>
                </table>
                </div>

                    <!-- Message si aucun résultat -->
                    <div id="noResults" class="alert alert-info text-center mt-3" style="display: none;">
                        <i class="fas fa-info-circle"></i> Aucun créneau ne correspond à votre recherche.
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Scripts Bootstrap et FontAwesome -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>

<!-- Scripts améliorés pour l'interface utilisateur -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Éléments du DOM
    const searchInput = document.getElementById('searchInput');
    const filterJour = document.getElementById('filterJour');
    const filterFrequence = document.getElementById('filterFrequence');
    const resetFilters = document.getElementById('resetFilters');
    const table = document.getElementById('creneauxTable');
    const noResults = document.getElementById('noResults');
    const loader = document.getElementById('loader');
    const heureDebut = document.getElementById('heureDebut');
    const heureFin = document.getElementById('heureFin');
    const salleSelect = document.getElementById('salleSelect');
    const addCreneauForm = document.getElementById('addCreneauForm');

    // Fonction pour afficher le loader
    function showLoader() {
        if (loader) {
            loader.style.display = 'flex';
        }
    }

    // Fonction pour masquer le loader
    function hideLoader() {
        if (loader) {
            setTimeout(() => {
                loader.style.display = 'none';
            }, 500); // Délai pour une meilleure expérience utilisateur
        }
    }

    // Fonction pour filtrer le tableau avec animation
    function filterTable() {
        showLoader();

        const searchValue = searchInput.value.toLowerCase();
        const jourValue = filterJour.value;
        const frequenceValue = filterFrequence.value;

        let visibleCount = 0;
        const rows = table.querySelectorAll('tbody tr');

        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            const jour = row.querySelector('td:nth-child(4)').textContent.trim();
            const frequence = row.querySelector('td:nth-child(7)').textContent.trim();

            const matchesSearch = text.includes(searchValue);
            const matchesJour = jourValue === '' || jour === jourValue;
            const matchesFrequence = frequenceValue === '' || frequence === frequenceValue;

            const isVisible = matchesSearch && matchesJour && matchesFrequence;

            // Animation de transition
            if (isVisible) {
                row.style.display = '';
                setTimeout(() => {
                    row.style.opacity = '1';
                    row.style.transform = 'translateY(0)';
                }, 50);
                visibleCount++;
            } else {
                row.style.opacity = '0';
                row.style.transform = 'translateY(-10px)';
                setTimeout(() => {
                    row.style.display = 'none';
                }, 300);
            }
        });

        // Afficher ou masquer le message "Aucun résultat"
        if (noResults) {
            noResults.style.display = visibleCount === 0 ? 'block' : 'none';
        }

        hideLoader();
    }

    // Événements pour les filtres
    if (searchInput) searchInput.addEventListener('input', filterTable);
    if (filterJour) filterJour.addEventListener('change', filterTable);
    if (filterFrequence) filterFrequence.addEventListener('change', filterTable);

    // Réinitialiser les filtres
    if (resetFilters) {
        resetFilters.addEventListener('click', function() {
            if (searchInput) searchInput.value = '';
            if (filterJour) filterJour.value = '';
            if (filterFrequence) filterFrequence.value = '';
            filterTable();
        });
    }

    // Effet de survol pour les lignes du tableau
    const tableRows = document.querySelectorAll('tbody tr');
    tableRows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 4px 8px rgba(106, 17, 203, 0.3)';
            this.style.transition = 'all 0.3s ease';
        });

        row.addEventListener('mouseleave', function() {
            this.style.transform = '';
            this.style.boxShadow = '';
        });
    });

    // Animation pour les alertes
    const alerts = document.querySelectorAll('.alert:not(#noResults)');
    alerts.forEach(alert => {
        setTimeout(() => {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 5000); // Fermer après 5 secondes
    });

    // Validation du formulaire d'ajout de créneau
    if (addCreneauForm) {
        addCreneauForm.addEventListener('submit', function(e) {
            if (heureDebut && heureFin) {
                const debut = heureDebut.value;
                const fin = heureFin.value;

                if (debut >= fin) {
                    e.preventDefault();
                    alert("L'heure de début doit être antérieure à l'heure de fin.");
                    return false;
                }
            }

            showLoader();
            return true;
        });
    }

    // Effet de focus sur les champs de formulaire
    const formInputs = document.querySelectorAll('.form-control, .form-select');
    formInputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.style.boxShadow = '0 0 0 0.25rem rgba(106, 17, 203, 0.25)';
            this.style.borderColor = 'var(--primary-color)';
        });

        input.addEventListener('blur', function() {
            this.style.boxShadow = '';
            this.style.borderColor = '';
        });
    });

    // Initialisation des tooltips Bootstrap
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Ajouter des animations d'entrée aux cartes
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';

        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, 100 * (index + 1));
    });

    // Masquer le loader au chargement complet
    window.addEventListener('load', hideLoader);
});
</script>
</body>
</html>