<?php
require_once 'config.php';
session_start();

// Vérifier si l'utilisateur est connecté et est un coordinateur
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'coordinateur') {
    header("Location: login_coordinateur.php");
    exit;
}

// Connexion à la base de données
try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
} catch (PDOException $e) {
    die("Erreur de connexion à la base de données: " . $e->getMessage());
}

// Vérifier si la table groupes existe, sinon la créer
try {
    $pdo->query("SELECT 1 FROM groupes LIMIT 1");
} catch (PDOException $e) {
    // La table n'existe pas, on la crée
    $pdo->exec("
        CREATE TABLE groupes (
            id_groupe INT AUTO_INCREMENT PRIMARY KEY,
            nom VARCHAR(100) NOT NULL,
            type ENUM('TP', 'TD') NOT NULL,
            filiere VARCHAR(100) NOT NULL,
            niveau VARCHAR(50) NOT NULL,
            capacite INT NOT NULL DEFAULT 30,
            annee_scolaire VARCHAR(20) NOT NULL,
            departement_id INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (departement_id) REFERENCES departements(departement_id) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
    ");
}

// Récupérer les informations du coordinateur
$departement_id = $_SESSION['id_departement'] ?? null;
$filiere_coordinateur = $_SESSION['filiere'] ?? 'Informatique'; // Filière par défaut si non définie

// Traitement des actions
$error = null;
$success = null;

// Ajout d'un groupe
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['ajouter'])) {
    try {
        $nom = $_POST['nom'];
        $type = $_POST['type'];
        $niveau = $_POST['niveau'];
        $capacite = (int)$_POST['capacite'];
        $annee = $_POST['annee_scolaire'];

        // Utiliser la filière du coordinateur
        $filiere = $filiere_coordinateur;

        $sql = "INSERT INTO groupes (nom, type, filiere, niveau, capacite, annee_scolaire, departement_id)
                VALUES (?, ?, ?, ?, ?, ?, ?)";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$nom, $type, $filiere, $niveau, $capacite, $annee, $departement_id]);

        $success = "Groupe ajouté avec succès";
        header("Location: gerer_groupes.php?page=liste");
        exit();
    } catch (PDOException $e) {
        $error = "Erreur lors de l'ajout du groupe: " . $e->getMessage();
    }
}

// Suppression d'un groupe
if (isset($_GET['delete'])) {
    try {
        $id = (int)$_GET['delete'];
        $stmt = $pdo->prepare("DELETE FROM groupes WHERE id_groupe = ?");
        $stmt->execute([$id]);

        $success = "Groupe supprimé avec succès";
        header("Location: gerer_groupes.php?page=liste");
        exit();
    } catch (PDOException $e) {
        $error = "Erreur lors de la suppression du groupe: " . $e->getMessage();
    }
}

// Modification d'un groupe
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['modifier'])) {
    try {
        $id = (int)$_POST['id'];
        $nom = $_POST['nom'];
        $type = $_POST['type'];
        $niveau = $_POST['niveau'];
        $capacite = (int)$_POST['capacite'];
        $annee = $_POST['annee_scolaire'];

        // Conserver la filière existante ou utiliser celle du coordinateur
        $stmt = $pdo->prepare("SELECT filiere FROM groupes WHERE id_groupe = ?");
        $stmt->execute([$id]);
        $filiere = $stmt->fetchColumn() ?: $filiere_coordinateur;

        $sql = "UPDATE groupes
                SET nom = ?, type = ?, niveau = ?, capacite = ?, annee_scolaire = ?, departement_id = ?
                WHERE id_groupe = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$nom, $type, $niveau, $capacite, $annee, $departement_id, $id]);

        $success = "Groupe modifié avec succès";
        header("Location: gerer_groupes.php?page=liste");
        exit();
    } catch (PDOException $e) {
        $error = "Erreur lors de la modification du groupe: " . $e->getMessage();
    }
}

// Vérifier si la colonne departement_id existe dans la table groupes
try {
    $pdo->query("SELECT departement_id FROM groupes LIMIT 1");
} catch (PDOException $e) {
    // La colonne n'existe pas, on l'ajoute
    $pdo->exec("ALTER TABLE groupes ADD COLUMN departement_id INT");
    $pdo->exec("ALTER TABLE groupes ADD CONSTRAINT fk_departement FOREIGN KEY (departement_id) REFERENCES departements(departement_id) ON DELETE SET NULL");
}

// Récupération des statistiques
$total_groupes = $pdo->query("SELECT COUNT(*) FROM groupes")->fetchColumn() ?: 0;
$total_tp = $pdo->query("SELECT COUNT(*) FROM groupes WHERE type = 'TP'")->fetchColumn() ?: 0;
$total_td = $pdo->query("SELECT COUNT(*) FROM groupes WHERE type = 'TD'")->fetchColumn() ?: 0;
$capacite_totale = $pdo->query("SELECT SUM(capacite) FROM groupes")->fetchColumn() ?: 0;

// Récupération des filières pour le formulaire
$filieres = $pdo->query("SELECT DISTINCT filiere FROM groupes ORDER BY filiere")->fetchAll() ?: [];

// Récupération des niveaux pour le formulaire
$niveaux = $pdo->query("SELECT DISTINCT niveau FROM groupes ORDER BY niveau")->fetchAll() ?: [];

// Récupération des années scolaires pour le formulaire
$annees = $pdo->query("SELECT DISTINCT annee_scolaire FROM groupes ORDER BY annee_scolaire DESC")->fetchAll() ?: [];

// Inclure le script d'enregistrement des visites
require_once 'record_page_visit.php';
recordPageVisit('gerer_groupes.php', 'coordinateur');
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion des Groupes TP/TD</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.28/jspdf.plugin.autotable.min.js"></script>
    <style>
        :root {
            --primary-color: #6a11cb;
            --secondary-color: #5a0cb2;
            --light-bg: #f5f7ff;
            --card-bg: rgba(255, 255, 255, 0.95);
            --text-color: #333333;
            --white: #ffffff;
            --error-color: #ff4757;
            --success-color: #28a745;
            --border-radius: 10px;
            --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        body {
            background: linear-gradient(135deg, var(--light-bg) 0%, #c3c7f7 100%);
            color: var(--text-color);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
            margin: 0;
        }

        .header-container {
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 1.5rem;
            box-shadow: 0 2px 15px rgba(106, 17, 203, 0.1);
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: center;
            max-width: 1200px;
            margin: 0 auto;
        }

        .header-title {
            font-weight: 600;
            font-size: 2rem;
            margin-left: 20px;
            color: white;
        }

        .header-logo {
            height: 60px;
            width: auto;
        }

        .main-container {
            display: flex;
            max-width: 1400px;
            margin: 2rem auto;
            gap: 1.5rem;
            padding: 0 1rem;
        }

        .left-menu {
            flex: 0 0 250px;
        }

        .content-area {
            flex: 1;
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            padding: 2rem;
        }

        .card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            margin-bottom: 1.5rem;
            overflow: hidden;
        }

        .card-header {
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
            color: white;
            padding: 1rem 1.5rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border: none;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-color) 100%);
        }

        .table-responsive {
            border-radius: var(--border-radius);
            overflow: hidden;
        }

        table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
        }

        table th {
            background-color: var(--primary-color);
            color: white;
            padding: 0.75rem 1rem;
        }

        table td {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #eee;
        }

        .alert {
            border-radius: var(--border-radius);
        }

        .alert-danger {
            background-color: rgba(220, 53, 69, 0.1);
            border-color: var(--error-color);
            color: var(--error-color);
        }

        .alert-success {
            background-color: rgba(40, 167, 69, 0.1);
            border-color: var(--success-color);
            color: var(--success-color);
        }

        .stat-card {
            background-color: var(--card-bg);
            border-radius: var(--border-radius);
            padding: 1.5rem;
            box-shadow: var(--box-shadow);
            transition: transform 0.3s ease;
            height: 100%;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            color: white;
            font-size: 1.5rem;
        }

        .search-box {
            position: relative;
            width: 100%;
        }

        .search-box input {
            padding-right: 2.5rem;
            border-radius: var(--border-radius);
        }

        .search-icon {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--primary-color);
        }

        .action-buttons .btn {
            margin-left: 0.5rem;
        }

        .progress {
            height: 20px;
            background-color: #e9ecef;
            border-radius: var(--border-radius);
        }

        .progress-bar {
            border-radius: var(--border-radius);
        }

        .back-to-top {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 1000;
        }

        .back-to-top.visible {
            opacity: 1;
        }

        /* Responsive adjustments */
        @media (max-width: 992px) {
            .main-container {
                flex-direction: column;
            }

            .left-menu {
                width: 100%;
                margin-bottom: 1.5rem;
            }

            .header-title {
                font-size: 1.8rem;
            }
        }

        @media (max-width: 768px) {
            .header-title {
                font-size: 1.5rem;
            }

            .header-logo {
                height: 50px;
            }

            .content-area {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>

<!-- En-tête -->
<div class="header-container">
    <div class="header-content">
        <img src="image copy.png" alt="Logo" class="header-logo">
        <h1 class="header-title">Gestion des Groupes TP/TD</h1>
    </div>
</div>

<!-- Contenu principal -->
<div class="main-container">
    <!-- Menu latéral -->
    <div class="left-menu">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-bars me-2"></i>Menu</h5>
            </div>
            <div class="card-body">
                <a href="dashboard_coordinateur.php" class="btn btn-primary btn-block mb-2 text-start">
                    <i class="fas fa-home me-2"></i> Tableau de bord
                </a>
                <a href="gerer_groupes.php" class="btn btn-primary btn-block mb-2 text-start">
                    <i class="fas fa-users me-2"></i> Gérer les groupes
                </a>
                <a href="gerer_emplois_temps.php" class="btn btn-primary btn-block mb-2 text-start">
                    <i class="fas fa-calendar-alt me-2"></i> Emplois du temps
                </a>
                <a href="affectation_vactaire.php" class="btn btn-primary btn-block mb-2 text-start">
                    <i class="fas fa-user-tie me-2"></i> Affectation Vacataires
                </a>
                <a href="Definir_UE.php" class="btn btn-primary btn-block mb-2 text-start">
                    <i class="fas fa-book me-2"></i> Définir les UE
                </a>
                <a href="Extraire_D_Excel.php" class="btn btn-primary btn-block mb-2 text-start">
                    <i class="fas fa-file-excel me-2"></i> Extraire en Excel
                </a>
                <button class="btn btn-danger btn-block text-start" onclick="logout()">
                    <i class="fas fa-sign-out-alt me-2"></i> Déconnexion
                </button>
            </div>
        </div>
    </div>

    <!-- Zone de contenu principale -->
    <div class="content-area">
        <?php if ($error): ?>
            <div class="alert alert-danger mb-4"><?= htmlspecialchars($error) ?></div>
        <?php endif; ?>

        <?php if ($success): ?>
            <div class="alert alert-success mb-4"><?= htmlspecialchars($success) ?></div>
        <?php endif; ?>

        <!-- Barre d'outils -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="mb-0">
                <i class="fas fa-users me-2"></i>
                <?php
                if (!isset($_GET['page']) || $_GET['page'] == 'ajouter') {
                    echo isset($_GET['edit']) ? 'Modifier un Groupe' : 'Ajouter un Groupe';
                } else {
                    echo 'Liste des Groupes';
                }
                ?>
            </h2>

            <div class="d-flex">
                <?php if (!isset($_GET['page']) || $_GET['page'] == 'ajouter'): ?>
                    <a href="?page=liste" class="btn btn-secondary me-2">
                        <i class="fas fa-list me-1"></i> Voir la liste
                    </a>
                <?php else: ?>
                    <a href="?page=ajouter" class="btn btn-success me-2">
                        <i class="fas fa-plus me-1"></i> Nouveau groupe
                    </a>
                <?php endif; ?>
                <button class="btn btn-primary me-2" onclick="window.print()">
                    <i class="fas fa-print me-1"></i> Imprimer
                </button>
                <button class="btn btn-success me-2" id="exportExcel">
                    <i class="fas fa-file-excel me-1"></i> Excel
                </button>
                <button class="btn btn-danger" id="exportPDF">
                    <i class="fas fa-file-pdf me-1"></i> PDF
                </button>
            </div>
        </div>

        <!-- Tableau de bord statistique -->
        <?php if (!isset($_GET['page']) || $_GET['page'] == 'ajouter' || $_GET['page'] == 'liste'): ?>
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="stat-card">
                    <div class="d-flex align-items-center">
                        <div class="stat-icon" style="background: linear-gradient(135deg, #6a11cb 0%, #5a0cb2 100%);">
                            <i class="fas fa-users"></i>
                        </div>
                        <div>
                            <h3 class="mb-0"><?= $total_groupes ?></h3>
                            <p class="mb-0 text-muted">Groupes au total</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="stat-card">
                    <div class="d-flex align-items-center">
                        <div class="stat-icon" style="background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);">
                            <i class="fas fa-flask"></i>
                        </div>
                        <div>
                            <h3 class="mb-0"><?= $total_tp ?></h3>
                            <p class="mb-0 text-muted">Groupes TP</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="stat-card">
                    <div class="d-flex align-items-center">
                        <div class="stat-icon" style="background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);">
                            <i class="fas fa-chalkboard-teacher"></i>
                        </div>
                        <div>
                            <h3 class="mb-0"><?= $total_td ?></h3>
                            <p class="mb-0 text-muted">Groupes TD</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="stat-card">
                    <div class="d-flex align-items-center">
                        <div class="stat-icon" style="background: linear-gradient(135deg, #28a745 0%, #218838 100%);">
                            <i class="fas fa-user-graduate"></i>
                        </div>
                        <div>
                            <h3 class="mb-0"><?= $capacite_totale ?: 0 ?></h3>
                            <p class="mb-0 text-muted">Capacité totale</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Contenu dynamique -->
        <?php
        // Récupérer les données du groupe si on est en mode édition
        $groupe = [];
        if (isset($_GET['edit'])) {
            $id = (int)$_GET['edit'];
            $stmt = $pdo->prepare("SELECT * FROM groupes WHERE id_groupe = ?");
            $stmt->execute([$id]);
            $groupe = $stmt->fetch();
            if (!$groupe) {
                header("Location: gerer_groupes.php?page=liste");
                exit();
            }
        } else {
            // Valeurs par défaut pour un nouveau groupe
            $groupe = [
                'id_groupe' => '',
                'nom' => '',
                'type' => 'TP',
                'filiere' => $filiere_coordinateur,
                'niveau' => '',
                'capacite' => 30,
                'annee_scolaire' => date('Y') . '-' . (date('Y') + 1)
            ];
        }

        // Déterminer les niveaux disponibles en fonction de la filière
        $niveaux_disponibles = [];
        if ($filiere_coordinateur == 'Informatique') {
            $niveaux_disponibles = [
                'gi1' => 'GI1',
                'gi2' => 'GI2',
                'gi3' => 'GI3'
            ];
        } elseif ($filiere_coordinateur == 'Ingénierie de données') {
            $niveaux_disponibles = [
                'id1' => 'ID1',
                'id2' => 'ID2',
                'id3' => 'ID3'
            ];
        } elseif ($filiere_coordinateur == 'Réseaux et Systèmes') {
            $niveaux_disponibles = [
                'rs1' => 'RS1',
                'rs2' => 'RS2',
                'rs3' => 'RS3'
            ];
        } else {
            // Filière par défaut ou autre
            $niveaux_disponibles = [
                'n1' => 'Niveau 1',
                'n2' => 'Niveau 2',
                'n3' => 'Niveau 3'
            ];
        }

        // Si le niveau n'est pas défini, prendre le premier niveau disponible
        if (empty($groupe['niveau'])) {
            $groupe['niveau'] = array_key_first($niveaux_disponibles);
        }
        ?>

        <?php if (!isset($_GET['page']) || $_GET['page'] == 'ajouter'): ?>
            <!-- Formulaire d'ajout/modification -->
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0"><?= isset($_GET['edit']) ? 'Modifier un Groupe' : 'Ajouter un Groupe' ?></h4>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <input type="hidden" name="id" value="<?= $groupe['id_groupe'] ?>">

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">Nom du groupe</label>
                                <input type="text" class="form-control" name="nom" value="<?= $groupe['nom'] ?>" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Type de groupe</label>
                                <select class="form-select" name="type">
                                    <option value="TP" <?= $groupe['type'] == 'TP' ? 'selected' : '' ?>>TP</option>
                                    <option value="TD" <?= $groupe['type'] == 'TD' ? 'selected' : '' ?>>TD</option>
                                </select>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">Filière</label>
                                <input type="text" class="form-control" value="<?= htmlspecialchars($filiere_coordinateur) ?>" readonly>
                                <input type="hidden" name="filiere" value="<?= htmlspecialchars($filiere_coordinateur) ?>">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Niveau</label>
                                <select class="form-select" name="niveau">
                                    <?php foreach ($niveaux_disponibles as $value => $label): ?>
                                        <option value="<?= $value ?>" <?= $groupe['niveau'] == $value ? 'selected' : '' ?>><?= $label ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">Capacité</label>
                                <input type="number" class="form-control" name="capacite" value="<?= $groupe['capacite'] ?>" min="1" max="100" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Année scolaire</label>
                                <select class="form-select" name="annee_scolaire">
                                    <?php
                                    $current_year = date('Y');
                                    for ($i = $current_year - 1; $i <= $current_year + 3; $i++) {
                                        $academic_year = $i . '-' . ($i + 1);
                                        echo '<option value="' . $academic_year . '"';
                                        if ($groupe['annee_scolaire'] == $academic_year) echo ' selected';
                                        echo '>' . $academic_year . '</option>';
                                    }
                                    ?>
                                </select>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between mt-4">
                            <a href="?page=liste" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i> Retour
                            </a>
                            <button type="submit" name="<?= isset($_GET['edit']) ? 'modifier' : 'ajouter' ?>" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> <?= isset($_GET['edit']) ? 'Enregistrer' : 'Ajouter' ?>
                            </button>
                        </div>
                    </form>
                </div>
            </div>

        <?php elseif ($_GET['page'] == 'liste'): ?>
            <!-- Liste des groupes -->
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">Liste des Groupes</h4>
                        <div class="search-box">
                            <input type="text" id="searchInput" class="form-control" placeholder="Rechercher...">
                            <i class="fas fa-search search-icon"></i>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <select id="filterType" class="form-select">
                                <option value="">Tous les types</option>
                                <option value="TP">TP</option>
                                <option value="TD">TD</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select id="filterFiliere" class="form-select">
                                <option value="">Toutes les filières</option>
                                <?php foreach ($filieres as $filiere): ?>
                                    <option value="<?= $filiere['filiere'] ?>"><?= $filiere['filiere'] ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select id="filterNiveau" class="form-select">
                                <option value="">Tous les niveaux</option>
                                <?php foreach ($niveaux as $niveau): ?>
                                    <option value="<?= $niveau['niveau'] ?>"><?= $niveau['niveau'] ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select id="filterAnnee" class="form-select">
                                <option value="">Toutes les années</option>
                                <?php foreach ($annees as $annee): ?>
                                    <option value="<?= $annee['annee_scolaire'] ?>"><?= $annee['annee_scolaire'] ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Nom</th>
                                    <th>Type</th>
                                    <th>Filière</th>
                                    <th>Niveau</th>
                                    <th>Capacité</th>
                                    <th>Année</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="groupesTableBody">
                                <?php
                                $stmt = $pdo->prepare("SELECT * FROM groupes ORDER BY id_groupe DESC");
                                $stmt->execute();
                                while ($row = $stmt->fetch()):
                                ?>
                                <tr data-type="<?= $row['type'] ?>" data-filiere="<?= $row['filiere'] ?>" data-niveau="<?= $row['niveau'] ?>" data-annee="<?= $row['annee_scolaire'] ?>">
                                    <td><?= $row['id_groupe'] ?></td>
                                    <td><?= $row['nom'] ?></td>
                                    <td>
                                        <span class="badge bg-<?= $row['type'] == 'TP' ? 'info' : 'warning' ?>">
                                            <?= $row['type'] ?>
                                        </span>
                                    </td>
                                    <td><?= $row['filiere'] ?></td>
                                    <td><?= $row['niveau'] ?></td>
                                    <td>
                                        <div class="progress">
                                            <div class="progress-bar" role="progressbar"
                                                 style="width: <?= min(100, ($row['capacite'] / 50) * 100) ?>%;"
                                                 aria-valuenow="<?= $row['capacite'] ?>"
                                                 aria-valuemin="0"
                                                 aria-valuemax="50">
                                                <?= $row['capacite'] ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td><?= $row['annee_scolaire'] ?></td>
                                    <td>
                                        <a href="?edit=<?= $row['id_groupe'] ?>" class="btn btn-sm btn-primary" title="Modifier">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="?delete=<?= $row['id_groupe'] ?>" class="btn btn-sm btn-danger" title="Supprimer"
                                           onclick="return confirm('Êtes-vous sûr de vouloir supprimer ce groupe ?');">
                                            <i class="fas fa-trash-alt"></i>
                                        </a>
                                    </td>
                                </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>

                    <div id="noResults" class="alert alert-info text-center mt-3" style="display: none;">
                        <i class="fas fa-info-circle me-2"></i> Aucun groupe trouvé
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Bouton de retour en haut -->
<button id="backToTop" class="back-to-top">
    <i class="fas fa-arrow-up"></i>
</button>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
$(document).ready(function() {
    // Filtrage des groupes
    function filterGroups() {
        const type = $('#filterType').val();
        const filiere = $('#filterFiliere').val();
        const niveau = $('#filterNiveau').val();
        const annee = $('#filterAnnee').val();
        const search = $('#searchInput').val().toLowerCase();

        let visibleCount = 0;

        $('#groupesTableBody tr').each(function() {
            const rowType = $(this).data('type');
            const rowFiliere = $(this).data('filiere');
            const rowNiveau = $(this).data('niveau');
            const rowAnnee = $(this).data('annee');
            const rowText = $(this).text().toLowerCase();

            const typeMatch = !type || rowType === type;
            const filiereMatch = !filiere || rowFiliere === filiere;
            const niveauMatch = !niveau || rowNiveau === niveau;
            const anneeMatch = !annee || rowAnnee === annee;
            const searchMatch = !search || rowText.includes(search);

            if (typeMatch && filiereMatch && niveauMatch && anneeMatch && searchMatch) {
                $(this).show();
                visibleCount++;
            } else {
                $(this).hide();
            }
        });

        if (visibleCount === 0) {
            $('#noResults').show();
        } else {
            $('#noResults').hide();
        }
    }

    // Appliquer les filtres
    $('#filterType, #filterFiliere, #filterNiveau, #filterAnnee, #searchInput').on('change keyup', filterGroups);

    // Bouton de retour en haut
    $(window).scroll(function() {
        if ($(this).scrollTop() > 300) {
            $('#backToTop').addClass('visible');
        } else {
            $('#backToTop').removeClass('visible');
        }
    });

    $('#backToTop').click(function() {
        $('html, body').animate({scrollTop: 0}, 'smooth');
    });

    // Animation des cartes statistiques
    $('.stat-card').hover(
        function() {
            $(this).css('transform', 'translateY(-5px)');
        },
        function() {
            $(this).css('transform', '');
        }
    );

    // Ajouter les gestionnaires d'événements pour les boutons d'exportation
    $('#exportExcel').click(function() {
        exportToExcel();
    });

    $('#exportPDF').click(function() {
        exportToPDF();
    });
});

// Fonction pour exporter en Excel
function exportToExcel() {
    // Créer un tableau temporaire avec les données filtrées
    let table = document.createElement('table');
    let thead = document.createElement('thead');
    let tbody = document.createElement('tbody');

    // Ajouter l'en-tête
    let headerRow = document.createElement('tr');
    ['ID', 'Nom', 'Type', 'Filière', 'Niveau', 'Capacité', 'Année'].forEach(text => {
        let th = document.createElement('th');
        th.textContent = text;
        headerRow.appendChild(th);
    });
    thead.appendChild(headerRow);
    table.appendChild(thead);

    // Ajouter les données filtrées
    const rows = document.querySelectorAll('#groupesTableBody tr:not([style*="display: none"])');
    rows.forEach(row => {
        let newRow = document.createElement('tr');
        // Ignorer la dernière colonne (actions)
        Array.from(row.cells).slice(0, -1).forEach(cell => {
            let td = document.createElement('td');
            // Si c'est une cellule avec un badge ou une barre de progression
            if (cell.querySelector('.badge')) {
                td.textContent = cell.querySelector('.badge').textContent.trim();
            } else if (cell.querySelector('.progress')) {
                td.textContent = cell.querySelector('.progress-bar').textContent.trim();
            } else {
                td.textContent = cell.textContent.trim();
            }
            newRow.appendChild(td);
        });
        tbody.appendChild(newRow);
    });
    table.appendChild(tbody);

    // Convertir en format Excel
    let html = table.outerHTML.replace(/ /g, '%20');
    let date = new Date().toISOString().slice(0, 10);
    let filename = 'groupes_' + date + '.xls';

    // Créer un lien de téléchargement
    let downloadLink = document.createElement('a');
    document.body.appendChild(downloadLink);
    downloadLink.href = 'data:application/vnd.ms-excel;charset=utf-8,' + html;
    downloadLink.download = filename;
    downloadLink.click();
    document.body.removeChild(downloadLink);
}

// Fonction pour exporter en PDF
function exportToPDF() {
    // Utiliser jsPDF
    const { jsPDF } = window.jspdf;
    const doc = new jsPDF();

    // Ajouter un titre
    doc.setFontSize(18);
    doc.text('Liste des Groupes', 14, 22);

    // Préparer les données pour le tableau
    const rows = document.querySelectorAll('#groupesTableBody tr:not([style*="display: none"])');
    let data = [];
    rows.forEach(row => {
        let rowData = [];
        // Ignorer la dernière colonne (actions)
        Array.from(row.cells).slice(0, -1).forEach(cell => {
            // Si c'est une cellule avec un badge ou une barre de progression
            if (cell.querySelector('.badge')) {
                rowData.push(cell.querySelector('.badge').textContent.trim());
            } else if (cell.querySelector('.progress')) {
                rowData.push(cell.querySelector('.progress-bar').textContent.trim());
            } else {
                rowData.push(cell.textContent.trim());
            }
        });
        data.push(rowData);
    });

    // Définir les en-têtes
    const headers = ['ID', 'Nom', 'Type', 'Filière', 'Niveau', 'Capacité', 'Année'];

    // Créer le tableau dans le PDF
    doc.autoTable({
        head: [headers],
        body: data,
        startY: 30,
        theme: 'striped',
        headStyles: {
            fillColor: [106, 17, 203],
            textColor: [255, 255, 255]
        },
        margin: { top: 30 }
    });

    // Ajouter la date en bas de page
    const date = new Date().toLocaleDateString();
    doc.setFontSize(10);
    doc.text('Date d\'exportation: ' + date, 14, doc.internal.pageSize.height - 10);

    // Enregistrer le PDF
    doc.save('groupes_' + new Date().toISOString().slice(0, 10) + '.pdf');
}

function logout() {
    if (confirm('Êtes-vous sûr de vouloir vous déconnecter ?')) {
        window.location.href = 'logout.php';
    }
}
</script>
</body>
</html>