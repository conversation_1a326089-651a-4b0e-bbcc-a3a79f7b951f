<?php
require_once 'config.php';
session_start();

if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'admin') {
    header("Location: login.php");
    exit;
}

$titre = 'Chefs de département';
$description = 'Gestion des chefs de département';

try {
    $pdo = new PDO(
        "mysql:host=".DB_HOST.";dbname=".DB_NAME.";charset=utf8",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );

    $departements = $pdo->query("SELECT id_departement as id, nom_departement as nom FROM departement ORDER BY nom_departement")->fetchAll();

    // Vérifier si la colonne id_specialite existe dans la table utilisateurs
    $columns = $pdo->query("SHOW COLUMNS FROM utilisateurs LIKE 'id_specialite'")->fetchAll();
    $hasSpecialiteColumn = count($columns) > 0;

    if ($hasSpecialiteColumn) {
        $chefs = $pdo->query("
            SELECT u.id as id, u.nom, u.prenom, u.email,
                   d.id_departement, d.nom_departement,
                   s.id_specialite, s.nom_specialite
            FROM utilisateurs u
            JOIN departement d ON u.id_departement = d.id_departement
            LEFT JOIN specialite s ON u.id_specialite = s.id_specialite
            WHERE u.type_utilisateur = 'chef_departement'
            ORDER BY u.id ASC
        ")->fetchAll();
    } else {
        // Si la colonne n'existe pas, exécuter une requête sans la jointure sur specialite
        $chefs = $pdo->query("
            SELECT u.id as id, u.nom, u.prenom, u.email,
                   d.id_departement, d.nom_departement,
                   NULL as id_specialite, NULL as nom_specialite
            FROM utilisateurs u
            JOIN departement d ON u.id_departement = d.id_departement
            WHERE u.type_utilisateur = 'chef_departement'
            ORDER BY u.id ASC
        ")->fetchAll();

        // Afficher un message d'avertissement
        $message = "La colonne 'id_specialite' n'existe pas dans la table utilisateurs. <a href='add_specialite_column.php'>Cliquez ici</a> pour l'ajouter.";
        $messageType = "warning";
    }

} catch(PDOException $e) {
    die("Erreur de base de données : " . htmlspecialchars($e->getMessage()));
}

$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'ajouter':
                try {
                    $nom = trim($_POST['nom']);
                    $prenom = trim($_POST['prenom']);
                    $email = trim($_POST['email']);
                    $departement_id = $_POST['departement_id'];
                    $specialite_id = isset($_POST['specialite_id']) ? $_POST['specialite_id'] : null;
                    $password = $_POST['password'];

                    if (empty($nom) || empty($prenom) || empty($email) || empty($departement_id) || empty($password)) {
                        throw new Exception('Tous les champs sont obligatoires');
                    }

                    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                        throw new Exception('Email invalide');
                    }

                    if (strlen($password) < 8) {
                        throw new Exception('Le mot de passe doit contenir au moins 8 caractères');
                    }

                    $stmt = $pdo->prepare("SELECT id FROM utilisateurs WHERE email = ?");
                    $stmt->execute([$email]);

                    if ($stmt->rowCount() > 0) {
                        throw new Exception('Cet email est déjà utilisé');
                    }

                    $password_hash = password_hash($password, PASSWORD_DEFAULT);

                    $pdo->beginTransaction();

                    // Vérifier si la colonne id_specialite existe
                    $columns = $pdo->query("SHOW COLUMNS FROM utilisateurs LIKE 'id_specialite'")->fetchAll();
                    $hasSpecialiteColumn = count($columns) > 0;

                    if ($hasSpecialiteColumn) {
                        $stmt = $pdo->prepare("
                            INSERT INTO utilisateurs (nom, prenom, email, mot_de_passe, type_utilisateur, id_departement, id_specialite)
                            VALUES (?, ?, ?, ?, 'chef_departement', ?, ?)
                        ");
                        $stmt->execute([$nom, $prenom, $email, $password_hash, $departement_id, $specialite_id]);
                    } else {
                        $stmt = $pdo->prepare("
                            INSERT INTO utilisateurs (nom, prenom, email, mot_de_passe, type_utilisateur, id_departement)
                            VALUES (?, ?, ?, ?, 'chef_departement', ?)
                        ");
                        $stmt->execute([$nom, $prenom, $email, $password_hash, $departement_id]);
                    }

                    $pdo->commit();
                    $message = 'Chef de département ajouté avec succès';
                    $messageType = 'success';
                } catch (Exception $e) {
                    if ($pdo->inTransaction()) {
                        $pdo->rollBack();
                    }
                    $message = 'Erreur lors de l\'ajout : ' . $e->getMessage();
                    $messageType = 'danger';
                }
                break;

            case 'modifier':
                try {
                    $id = $_POST['id'];
                    $nom = trim($_POST['nom']);
                    $prenom = trim($_POST['prenom']);
                    $email = trim($_POST['email']);
                    $departement_id = $_POST['departement_id'];
                    $specialite_id = isset($_POST['specialite_id']) ? $_POST['specialite_id'] : null;
                    $password = $_POST['password'];

                    if (empty($nom) || empty($prenom) || empty($email) || empty($departement_id)) {
                        throw new Exception('Tous les champs sont obligatoires sauf le mot de passe');
                    }

                    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                        throw new Exception('Email invalide');
                    }

                    $pdo->beginTransaction();

                    // Vérifier si la colonne id_specialite existe
                    $columns = $pdo->query("SHOW COLUMNS FROM utilisateurs LIKE 'id_specialite'")->fetchAll();
                    $hasSpecialiteColumn = count($columns) > 0;

                    if ($hasSpecialiteColumn) {
                        $sql = "UPDATE utilisateurs SET
                                nom = ?,
                                prenom = ?,
                                email = ?,
                                id_departement = ?,
                                id_specialite = ?";

                        $params = [$nom, $prenom, $email, $departement_id, $specialite_id];
                    } else {
                        $sql = "UPDATE utilisateurs SET
                                nom = ?,
                                prenom = ?,
                                email = ?,
                                id_departement = ?";

                        $params = [$nom, $prenom, $email, $departement_id];
                    }

                    if (!empty($password)) {
                        if (strlen($password) < 8) {
                            throw new Exception('Le mot de passe doit contenir au moins 8 caractères');
                        }
                        $password_hash = password_hash($password, PASSWORD_DEFAULT);
                        $sql .= ", mot_de_passe = ?";
                        $params[] = $password_hash;
                    }

                    $sql .= " WHERE id = ? AND type_utilisateur = 'chef_departement'";
                    $params[] = $id;

                    $stmt = $pdo->prepare($sql);
                    $stmt->execute($params);

                    $pdo->commit();
                    $message = 'Chef de département modifié avec succès';
                    $messageType = 'success';
                } catch (Exception $e) {
                    if ($pdo->inTransaction()) {
                        $pdo->rollBack();
                    }
                    $message = 'Erreur lors de la modification : ' . $e->getMessage();
                    $messageType = 'danger';
                }
                break;

            case 'supprimer':
                try {
                    $id = $_POST['id'];

                    $pdo->beginTransaction();

                    $stmt = $pdo->prepare("
                        DELETE FROM utilisateurs
                        WHERE id = ? AND type_utilisateur = 'chef_departement'
                    ");
                    $stmt->execute([$id]);

                    $pdo->commit();
                    $message = 'Chef de département supprimé avec succès';
                    $messageType = 'success';
                } catch (Exception $e) {
                    if ($pdo->inTransaction()) {
                        $pdo->rollBack();
                    }
                    $message = 'Erreur lors de la suppression : ' . $e->getMessage();
                    $messageType = 'danger';
                }
                break;
        }

        header("Location: gestion_chef_departement.php?message=" . urlencode($message) . "&messageType=" . urlencode($messageType));
        exit;
    }
}

if (isset($_GET['message']) && isset($_GET['messageType'])) {
    $message = $_GET['message'];
    $messageType = $_GET['messageType'];
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($titre) ?> - ENSAH</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/responsive/2.4.1/css/responsive.bootstrap5.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/buttons/2.4.1/css/buttons.bootstrap5.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #1E90FF;
            --primary-magenta: magenta;
            --blue-transparent: rgba(30, 144, 255, 0.3);
            --dark-bg: #0a192f;
        }

        body {
            background: linear-gradient(rgba(10, 25, 47, 0.85), rgba(108, 27, 145, 0.85)),
            url('images/background.jpg') center center/cover fixed;
            color: white;
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .sidebar {
            background: rgba(44, 62, 80, 0.95);
            backdrop-filter: blur(5px);
            border-right: 2px solid var(--primary-blue);
            box-shadow: 4px 0 15px var(--blue-transparent);
            animation: borderGlow 8s infinite alternate;
            height: 100vh;
            position: fixed;
            width: 250px;
            z-index: 1000;
        }

        .sidebar .nav-link {
            color: white;
            padding: 12px 15px;
            margin: 8px 0;
            border-radius: 8px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .sidebar .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(18, 84, 151, 0.2), transparent);
            transition: 0.5s;
        }

        .sidebar .nav-link:hover {
            background: rgba(30, 144, 255, 0.1);
            transform: translateX(10px);
        }

        .sidebar .nav-link:hover::before {
            left: 100%;
        }

        /* Styles pour le menu déroulant */
        .sidebar .nav-link.dropdown-toggle::after {
            display: inline-block;
            margin-left: 0.5em;
            vertical-align: 0.15em;
            content: "";
            border-top: 0.3em solid;
            border-right: 0.3em solid transparent;
            border-bottom: 0;
            border-left: 0.3em solid transparent;
            transition: transform 0.3s ease;
        }

        .sidebar .nav-link.dropdown-toggle[aria-expanded="true"]::after {
            transform: rotate(180deg);
        }

        .sidebar #userSubmenu {
            padding-left: 0;
            list-style: none;
            transition: all 0.3s ease;
        }

        .sidebar #userSubmenu .nav-link {
            padding-left: 2.5rem;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            margin: 4px 0;
        }

        .sidebar #userSubmenu .nav-link:hover {
            background-color: rgba(30, 144, 255, 0.2);
        }

        .sidebar #userSubmenu .nav-link.active {
            background-color: rgba(30, 144, 255, 0.3);
            border-left: 3px solid var(--primary-blue);
        }

        /* Styles responsives pour le sidebar */
        @media (max-width: 991.98px) {
            .sidebar {
                width: 220px;
            }

            main[style*="margin-left: 250px"] {
                margin-left: 220px !important;
            }

            .sidebar .nav-link {
                padding: 10px 12px;
                font-size: 0.9rem;
            }

            .sidebar #userSubmenu .nav-link {
                padding-left: 2rem;
                font-size: 0.85rem;
            }
        }

        @media (max-width: 767.98px) {
            .sidebar {
                width: 200px;
            }

            main[style*="margin-left: 250px"] {
                margin-left: 200px !important;
            }

            .sidebar .nav-link {
                padding: 8px 10px;
                font-size: 0.85rem;
            }

            .sidebar #userSubmenu .nav-link {
                padding-left: 1.8rem;
                font-size: 0.8rem;
            }

            .sidebar .text-center h5 {
                font-size: 1rem;
            }

            .sidebar .img-fluid {
                max-width: 80px;
            }
        }

        .card {
            background: rgba(10, 25, 47, 0.9);
            border: 2px solid var(--primary-blue);
            border-radius: 10px;
            animation: cardBorderPulse 10s infinite;
        }

        .table {
            color: white;
            width: 100%;
            margin-bottom: 1rem;
            border-collapse: collapse;
            table-layout: fixed;
        }

        .table thead {
            background: linear-gradient(90deg, var(--primary-blue) 0%, var(--dark-bg) 100%);
        }

        .table thead th {
            border-bottom: 2px solid var(--primary-blue);
            color: var(--primary-blue);
            padding: 0.5rem;
            vertical-align: middle;
            text-align: left;
            font-size: 0.9rem;
        }

        .table tbody td {
            padding: 0.5rem;
            vertical-align: middle;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            font-size: 0.9rem;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        /* Ajustement des largeurs de colonnes */
        .table th:nth-child(1), .table td:nth-child(1) { /* ID */
            width: 5%;
        }
        .table th:nth-child(2), .table td:nth-child(2), /* Nom */
        .table th:nth-child(3), .table td:nth-child(3) { /* Prénom */
            width: 15%;
        }
        .table th:nth-child(4), .table td:nth-child(4) { /* Email */
            width: 20%;
        }
        .table th:nth-child(5), .table td:nth-child(5), /* Département */
        .table th:nth-child(6), .table td:nth-child(6) { /* Spécialité */
            width: 15%;
        }
        .table th:nth-child(7), .table td:nth-child(7) { /* Actions */
            width: 15%;
            text-align: center;
        }

        /* Ajustement de la largeur du conteneur du tableau */
        .table-responsive {
            max-width: 100%;
            overflow-x: auto;
        }

        /* Ajustement pour DataTables */
        .dataTables_wrapper {
            font-size: 0.9rem;
        }

        .dataTables_wrapper .dt-buttons {
            margin-bottom: 1rem;
        }

        /* Réduire la taille des boutons */
        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-magenta) 100%);
            border: none;
        }

        .btn-outline-primary {
            border-color: var(--primary-blue);
            color: var(--primary-blue);
        }

        .btn-outline-primary:hover {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-magenta) 100%);
            border-color: transparent;
        }

        .modal-content {
            background: rgba(10, 25, 47, 0.95);
            border: 2px solid var(--primary-blue);
        }

        .form-control, .form-select {
            background-color: rgba(255, 255, 255, 0.1);
            border: 1px solid var(--primary-blue);
            color: white;
        }

        .form-control:focus, .form-select:focus {
            background-color: rgba(255, 255, 255, 0.15);
            border-color: var(--primary-magenta);
            color: white;
            box-shadow: 0 0 0 0.25rem rgba(255, 0, 255, 0.25);
        }

        .form-control::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .form-select option {
            background-color: var(--dark-bg);
        }

        @keyframes cardBorderPulse {
            0% { border-color: var(--primary-blue); box-shadow: 0 5px 15px var(--blue-transparent); }
            50% { border-color: var(--primary-magenta); box-shadow: 0 5px 20px rgba(255, 0, 255, 0.3); }
            100% { border-color: var(--primary-blue); box-shadow: 0 5px 15px var(--blue-transparent); }
        }

        @keyframes borderGlow {
            0% { border-color: var(--primary-blue); }
            50% { border-color: var(--primary-magenta); }
            100% { border-color: var(--primary-blue); }
        }
    </style>
</head>
<body>
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <nav class="col-md-3 col-lg-2 sidebar">
            <div class="text-center mb-4">
                <img src="images/logo.png" alt="ENSAH" class="img-fluid mb-3" style="filter: drop-shadow(0 0 5px var(--primary-blue));">
                <h5 class="text-white" style="text-shadow: 0 0 10px var(--primary-blue);">Administration ENSAH</h5>
            </div>
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" href="admin_dashboard.php">
                        <i class="fas fa-chart-line me-2"></i>Tableau de bord
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link dropdown-toggle active" href="#userSubmenu" data-bs-toggle="collapse" aria-expanded="true">
                        <i class="fas fa-users-cog me-2"></i>
                        Gestion Utilisateurs
                    </a>
                    <ul class="collapse show" id="userSubmenu">
                        <li class="nav-item">
                            <a class="nav-link ms-3 active" href="gestion_chef_departement.php" style="
                                background: rgba(30, 144, 255, 0.2);
                                border-left: 4px solid var(--primary-blue);
                                transform: translateX(8px);
                            ">
                                <i class="fas fa-user-tie me-2"></i> Chefs de département
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link ms-3" href="gestion_coordinateur.php">
                                <i class="fas fa-user-cog me-2"></i> Coordinateurs
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link ms-3" href="gestion_professeurs.php">
                                <i class="fas fa-chalkboard-teacher me-2"></i> Professeurs
                            </a>
                        </li>
                    </ul>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="gestion_modules.php">
                        <i class="fas fa-book-open me-2"></i> Modules
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="affectation_ue.php">
                        <i class="fas fa-tasks me-2"></i> Affectations
                    </a>
                </li>
                <li class="nav-item mt-4">
                    <a class="nav-link text-danger" href="logout.php">
                        <i class="fas fa-sign-out-alt me-2"></i>Déconnexion
                    </a>
                </li>
            </ul>
        </nav>

        <!-- Main Content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 p-4" style="margin-left: 250px;">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="ms-4"><?= htmlspecialchars($titre) ?></h1>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#ajouterModal">
                    <i class="fas fa-plus-circle me-2"></i>Ajouter un chef de département
                </button>
            </div>

            <?php if ($message): ?>
            <div class="alert alert-<?= $messageType ?> alert-dismissible fade show" role="alert">
                <?= htmlspecialchars($message) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            <?php endif; ?>

            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h5 class="m-0 text-white ms-3"><i class="fas fa-users me-2"></i><?= htmlspecialchars($description) ?></h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover table-sm" id="chefsTable">
                            <thead>
                                <tr>
                                    <th style="width: 5%;">ID</th>
                                    <th style="width: 15%;">Nom</th>
                                    <th style="width: 15%;">Prénom</th>
                                    <th style="width: 20%;">Email</th>
                                    <th style="width: 15%;">Département</th>
                                    <th style="width: 15%;">Spécialité</th>
                                    <th style="width: 15%; text-align: center;">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($chefs as $index => $chef): ?>
                                <tr>
                                    <td><?= $index + 1 ?></td>
                                    <td><?= htmlspecialchars($chef['nom']) ?></td>
                                    <td><?= htmlspecialchars($chef['prenom']) ?></td>
                                    <td><?= htmlspecialchars($chef['email']) ?></td>
                                    <td><?= htmlspecialchars($chef['nom_departement']) ?></td>
                                    <td><?= htmlspecialchars($chef['nom_specialite'] ?? 'Non définie') ?></td>
                                    <td class="text-center">
                                        <button class="btn btn-sm btn-outline-primary me-1 btn-modifier"
                                                data-id="<?= $chef['id'] ?>"
                                                data-nom="<?= htmlspecialchars($chef['nom']) ?>"
                                                data-prenom="<?= htmlspecialchars($chef['prenom']) ?>"
                                                data-email="<?= htmlspecialchars($chef['email']) ?>"
                                                data-departement="<?= $chef['id_departement'] ?>"
                                                data-specialite="<?= $chef['id_specialite'] ?? '' ?>">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger btn-supprimer"
                                                data-id="<?= $chef['id'] ?>"
                                                data-nom="<?= htmlspecialchars($chef['prenom'] . ' ' . $chef['nom']) ?>">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <div class="modal fade" id="ajouterModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Ajouter un chef de département</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="post">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="ajouter">

                        <div class="mb-3">
                            <label for="nom" class="form-label">Nom</label>
                            <input type="text" class="form-control" id="nom" name="nom" required>
                        </div>

                        <div class="mb-3">
                            <label for="prenom" class="form-label">Prénom</label>
                            <input type="text" class="form-control" id="prenom" name="prenom" required>
                        </div>

                        <div class="mb-3">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>

                        <div class="mb-3">
                            <label for="departement_id" class="form-label">Département</label>
                            <select class="form-select" id="departement_id" name="departement_id" required onchange="chargerSpecialites(this.value)">
                                <option value="" selected disabled>Sélectionnez un département</option>
                                <?php foreach ($departements as $departement): ?>
                                    <option value="<?= $departement['id'] ?>"><?= htmlspecialchars($departement['nom']) ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="specialite_id" class="form-label">Spécialité</label>
                            <select class="form-select" id="specialite_id" name="specialite_id">
                                <option value="" selected disabled>Sélectionnez d'abord un département</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label">Mot de passe</label>
                            <input type="password" class="form-control" id="password" name="password" required minlength="8">
                            <div class="form-text text-light">Le mot de passe doit contenir au moins 8 caractères</div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                        <button type="submit" class="btn btn-primary">Ajouter</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="modal fade" id="modifierModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Modifier le chef de département</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="post">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="modifier">
                        <input type="hidden" name="id" id="modifier_id">

                        <div class="mb-3">
                            <label for="modifier_nom" class="form-label">Nom</label>
                            <input type="text" class="form-control" id="modifier_nom" name="nom" required>
                        </div>

                        <div class="mb-3">
                            <label for="modifier_prenom" class="form-label">Prénom</label>
                            <input type="text" class="form-control" id="modifier_prenom" name="prenom" required>
                        </div>

                        <div class="mb-3">
                            <label for="modifier_email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="modifier_email" name="email" required>
                        </div>

                        <div class="mb-3">
                            <label for="modifier_departement_id" class="form-label">Département</label>
                            <select class="form-select" id="modifier_departement_id" name="departement_id" required onchange="chargerSpecialitesModifier(this.value)">
                                <?php foreach ($departements as $departement): ?>
                                    <option value="<?= $departement['id'] ?>"><?= htmlspecialchars($departement['nom']) ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="modifier_specialite_id" class="form-label">Spécialité</label>
                            <select class="form-select" id="modifier_specialite_id" name="specialite_id">
                                <option value="" selected disabled>Sélectionnez d'abord un département</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="modifier_password" class="form-label">Mot de passe (laisser vide pour ne pas changer)</label>
                            <input type="password" class="form-control" id="modifier_password" name="password" minlength="8">
                            <div class="form-text text-light">Le mot de passe doit contenir au moins 8 caractères</div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                        <button type="submit" class="btn btn-primary">Enregistrer</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="modal fade" id="supprimerModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirmer la suppression</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Êtes-vous sûr de vouloir supprimer le chef de département <span id="supprimer_nom"></span> ?</p>
                    <p class="text-danger">Cette action est irréversible.</p>
                </div>
                <form method="post">
                    <input type="hidden" name="action" value="supprimer">
                    <input type="hidden" name="id" id="supprimer_id">
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                        <button type="submit" class="btn btn-danger">Supprimer</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.4.1/js/dataTables.responsive.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.4.1/js/responsive.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.bootstrap5.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.html5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.print.min.js"></script>
    <script>
        // Fonction pour charger les spécialités en fonction du département sélectionné
        function chargerSpecialites(departementId) {
            if (!departementId) {
                $('#specialite_id').html('<option value="" selected disabled>Sélectionnez d\'abord un département</option>');
                return;
            }

            $.ajax({
                url: 'get_specialites.php',
                type: 'GET',
                data: { departement_id: departementId },
                dataType: 'json',
                success: function(response) {
                    if (response.success && response.specialites.length > 0) {
                        let options = '<option value="" selected disabled>Sélectionnez une spécialité</option>';
                        response.specialites.forEach(function(specialite) {
                            options += `<option value="${specialite.id}">${specialite.nom}</option>`;
                        });
                        $('#specialite_id').html(options);
                    } else {
                        $('#specialite_id').html('<option value="" selected disabled>Aucune spécialité disponible</option>');
                    }
                },
                error: function() {
                    $('#specialite_id').html('<option value="" selected disabled>Erreur lors du chargement des spécialités</option>');
                }
            });
        }

        // Fonction pour charger les spécialités dans le formulaire de modification
        function chargerSpecialitesModifier(departementId) {
            if (!departementId) {
                $('#modifier_specialite_id').html('<option value="" selected disabled>Sélectionnez d\'abord un département</option>');
                return;
            }

            $.ajax({
                url: 'get_specialites.php',
                type: 'GET',
                data: { departement_id: departementId },
                dataType: 'json',
                success: function(response) {
                    if (response.success && response.specialites.length > 0) {
                        let options = '<option value="" selected disabled>Sélectionnez une spécialité</option>';
                        response.specialites.forEach(function(specialite) {
                            options += `<option value="${specialite.id}">${specialite.nom}</option>`;
                        });
                        $('#modifier_specialite_id').html(options);
                    } else {
                        $('#modifier_specialite_id').html('<option value="" selected disabled>Aucune spécialité disponible</option>');
                    }
                },
                error: function() {
                    $('#modifier_specialite_id').html('<option value="" selected disabled>Erreur lors du chargement des spécialités</option>');
                }
            });
        }

        // Script pour le menu déroulant
        document.addEventListener('DOMContentLoaded', function() {
            // Gestion du clic sur les liens du sous-menu
            const subMenuLinks = document.querySelectorAll('#userSubmenu .nav-link');
            subMenuLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    // Empêcher la fermeture du menu déroulant lors du clic sur un sous-élément
                    e.stopPropagation();
                });
            });

            // Gestion de l'animation de la flèche du menu déroulant
            const dropdownToggle = document.querySelector('.nav-link.dropdown-toggle');
            if (dropdownToggle) {
                dropdownToggle.addEventListener('click', function() {
                    const isExpanded = this.getAttribute('aria-expanded') === 'true';
                    this.setAttribute('aria-expanded', !isExpanded);
                });
            }
        });

        $(document).ready(function() {
            $('#chefsTable').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/fr-FR.json'
                },
                dom: '<"row"<"col-sm-12 col-md-6"B><"col-sm-12 col-md-6"f>>rt<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
                responsive: true,
                autoWidth: false,
                pageLength: 10,
                lengthMenu: [[10, 25, 50, -1], [10, 25, 50, "Tous"]],
                columnDefs: [
                    { orderable: false, targets: [6] },
                    { className: "text-center", targets: [6] },
                    { width: "5%", targets: 0 },
                    { width: "15%", targets: [1, 2] },
                    { width: "20%", targets: 3 },
                    { width: "15%", targets: [4, 5] },
                    { width: "15%", targets: 6 }
                ],
                buttons: [
                    {
                        extend: 'excel',
                        text: '<i class="fas fa-file-excel"></i> Excel',
                        className: 'btn btn-sm btn-outline-primary me-1',
                        exportOptions: {
                            columns: [0, 1, 2, 3, 4, 5]
                        }
                    },
                    {
                        extend: 'pdf',
                        text: '<i class="fas fa-file-pdf"></i> PDF',
                        className: 'btn btn-sm btn-outline-primary me-1',
                        exportOptions: {
                            columns: [0, 1, 2, 3, 4, 5]
                        }
                    },
                    {
                        extend: 'print',
                        text: '<i class="fas fa-print"></i> Imprimer',
                        className: 'btn btn-sm btn-outline-primary',
                        exportOptions: {
                            columns: [0, 1, 2, 3, 4, 5]
                        }
                    }
                ],
                drawCallback: function() {
                    // Ajuster les largeurs des colonnes après le rendu
                    $('.dataTables_scrollHeadInner, .dataTable').css('width', '100%');
                }
            });

            $('#ajouterModal').on('show.bs.modal', function() {
                $('#ajouterModal form')[0].reset();
                $('#specialite_id').html('<option value="" selected disabled>Sélectionnez d\'abord un département</option>');
            });

            $('.btn-modifier').click(function() {
                const id = $(this).data('id');
                const nom = $(this).data('nom');
                const prenom = $(this).data('prenom');
                const email = $(this).data('email');
                const departement = $(this).data('departement');
                const specialite = $(this).data('specialite');

                $('#modifier_id').val(id);
                $('#modifier_nom').val(nom);
                $('#modifier_prenom').val(prenom);
                $('#modifier_email').val(email);
                $('#modifier_departement_id').val(departement);

                // Charger les spécialités pour ce département
                chargerSpecialitesModifier(departement);

                // Sélectionner la spécialité après le chargement des options
                setTimeout(function() {
                    $('#modifier_specialite_id').val(specialite);
                }, 500);

                $('#modifierModal').modal('show');
            });

            $('.btn-supprimer').click(function() {
                const id = $(this).data('id');
                const nom = $(this).data('nom');

                $('#supprimer_id').val(id);
                $('#supprimer_nom').text(nom);

                $('#supprimerModal').modal('show');
            });
        });
    </script>
</body>
</html>