<?php
require_once 'config.php';

// Gestion de session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Vérification de l'authentification et du rôle
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php?error=session_invalide");
    exit();
}

// Pour le débogage
error_log("Session user_id: " . $_SESSION['user_id']);
error_log("Session role: " . ($_SESSION['role'] ?? 'non défini'));
error_log("Session user_type: " . ($_SESSION['user_type'] ?? 'non défini'));

// Forcer le type d'utilisateur à chef_departement pour cette page
$_SESSION['user_type'] = 'chef_departement';
$_SESSION['role'] = 'chef_departement';

// Protection CSRF
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}
$csrf_token = $_SESSION['csrf_token'];

// Variables
$error = null;
$success = $_SESSION['success'] ?? null;
unset($_SESSION['success']);

try {
    // Connexion à la base de données
    $pdo = new PDO(
        "mysql:host=".DB_HOST.";dbname=".DB_NAME.";charset=utf8",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );

    // Récupération des infos du département - avec gestion des erreurs
    try {
        // Essayer d'abord avec la structure 'departements'
        $stmt = $pdo->prepare("
            SELECT d.departement_id, d.nom_departement
            FROM departements d
            JOIN utilisateurs u ON d.departement_id = u.id_departement
            WHERE u.id = ?
        ");
        $stmt->execute([$_SESSION['user_id']]);
        $departement = $stmt->fetch();
    } catch (PDOException $e) {
        error_log("Erreur requête departements: " . $e->getMessage());
        $departement = false;
    }

    // Si la première requête échoue, essayer avec la structure 'departement'
    if (!$departement) {
        try {
            $stmt = $pdo->prepare("
                SELECT d.id_departement as departement_id, d.nom_departement
                FROM departement d
                JOIN utilisateurs u ON d.id_departement = u.id_departement
                WHERE u.id = ?
            ");
            $stmt->execute([$_SESSION['user_id']]);
            $departement = $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Erreur requête departement: " . $e->getMessage());
            $departement = false;
        }
    }

    // Si toujours pas de département, utiliser les valeurs de session ou des valeurs par défaut
    if (!$departement) {
        error_log("Aucun département trouvé pour l'utilisateur " . $_SESSION['user_id']);

        // Utiliser les valeurs de session si elles existent
        if (isset($_SESSION['id_departement']) || isset($_SESSION['departement_id'])) {
            $dept_id = $_SESSION['id_departement'] ?? $_SESSION['departement_id'] ?? 1;
            $dept_nom = $_SESSION['departement_nom'] ?? 'Département par défaut';

            $departement = [
                'departement_id' => $dept_id,
                'nom_departement' => $dept_nom
            ];

            error_log("Utilisation des valeurs de session pour le département: ID=$dept_id, Nom=$dept_nom");
        } else {
            // Valeurs par défaut
            $departement = [
                'departement_id' => 1,
                'nom_departement' => 'Département par défaut'
            ];
            error_log("Utilisation des valeurs par défaut pour le département");
        }
    }

    // Mettre à jour les variables de session avec les deux formats pour assurer la compatibilité
    $_SESSION['id_departement'] = $departement['departement_id'];
    $_SESSION['departement_id'] = $departement['departement_id'];
    $_SESSION['departement_nom'] = $departement['nom_departement'];

    // Récupération des choix en attente avec gestion des erreurs
    try {
        $choixQuery = $pdo->prepare("
            SELECT
                c.id,
                c.professeur_id,
                CONCAT(p.nom, ' ', p.prenom) as professeur,
                c.ue_id,
                CONCAT(ue.code_ue, ' - ', ue.intitule) as ue,
                c.statut,
                DATE_FORMAT(c.date_choix, '%d/%m/%Y %H:%i') as date_choix,
                c.departement_id
            FROM choix_professeurs c
            JOIN professeurs p ON c.professeur_id = p.id
            JOIN unites_enseignements ue ON c.ue_id = ue.id
            WHERE c.statut = 'en_attente' AND c.departement_id = ?
            ORDER BY c.date_choix ASC
        ");
        $choixQuery->execute([$departement['departement_id']]);
        $choixEnAttente = $choixQuery->fetchAll();
    } catch (PDOException $e) {
        error_log("Erreur récupération choix en attente: " . $e->getMessage());
        $choixEnAttente = [];
    }

    // Récupération des choix validés avec gestion des erreurs
    try {
        $choixValidesQuery = $pdo->prepare("
            SELECT
                c.id,
                CONCAT(p.nom, ' ', p.prenom) as professeur,
                CONCAT(ue.code_ue, ' - ', ue.intitule) as ue,
                DATE_FORMAT(c.date_choix, '%d/%m/%Y %H:%i') as date_choix,
                DATE_FORMAT(c.date_validation, '%d/%m/%Y %H:%i') as date_validation
            FROM choix_professeurs c
            JOIN professeurs p ON c.professeur_id = p.id
            JOIN unites_enseignements ue ON c.ue_id = ue.id
            WHERE c.statut = 'valide' AND c.departement_id = ?
            ORDER BY c.date_validation DESC
        ");
        $choixValidesQuery->execute([$departement['departement_id']]);
        $choixValides = $choixValidesQuery->fetchAll();
    } catch (PDOException $e) {
        error_log("Erreur récupération choix validés: " . $e->getMessage());
        $choixValides = [];
    }

    // Récupération des choix déclinés avec gestion des erreurs
    try {
        $choixDeclinesQuery = $pdo->prepare("
            SELECT
                c.id,
                CONCAT(p.nom, ' ', p.prenom) as professeur,
                CONCAT(ue.code_ue, ' - ', ue.intitule) as ue,
                DATE_FORMAT(c.date_choix, '%d/%m/%Y %H:%i') as date_choix,
                DATE_FORMAT(c.date_validation, '%d/%m/%Y %H:%i') as date_validation,
                c.commentaire
            FROM choix_professeurs c
            JOIN professeurs p ON c.professeur_id = p.id
            JOIN unites_enseignements ue ON c.ue_id = ue.id
            WHERE c.statut = 'rejete' AND c.departement_id = ?
            ORDER BY c.date_validation DESC
        ");
        $choixDeclinesQuery->execute([$departement['departement_id']]);
        $choixDeclines = $choixDeclinesQuery->fetchAll();
    } catch (PDOException $e) {
        error_log("Erreur récupération choix déclinés: " . $e->getMessage());
        $choixDeclines = [];
    }

    // Traitement des actions
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (!isset($_POST['csrf_token']) || !hash_equals($csrf_token, $_POST['csrf_token'])) {
            throw new Exception("Erreur de sécurité: Token invalide");
        }

        if (isset($_POST['valider'])) {
            $choixId = (int)$_POST['choix_id'];

            // Validation du choix
            $stmt = $pdo->prepare("
                UPDATE choix_professeurs
                SET statut = 'valide',
                    date_validation = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$choixId]);

            $_SESSION['success'] = "Le choix a été validé avec succès!";
            header("Location: gestion_choix.php");
            exit;

        } elseif (isset($_POST['decliner'])) {
            $choixId = (int)$_POST['choix_id'];
            $commentaire = trim($_POST['commentaire'] ?? '');

            if (empty($commentaire)) {
                throw new Exception("Un commentaire est requis pour décliner un choix");
            }

            // Vérification de l'existence de la colonne commentaire
            $columnCheck = $pdo->query("SHOW COLUMNS FROM choix_professeurs LIKE 'commentaire'")->fetch();

            if ($columnCheck) {
                $stmt = $pdo->prepare("
                    UPDATE choix_professeurs
                    SET statut = 'rejete',
                        date_validation = NOW(),
                        commentaire = ?
                    WHERE id = ?
                ");
                $stmt->execute([$commentaire, $choixId]);
            } else {
                $stmt = $pdo->prepare("
                    UPDATE choix_professeurs
                    SET statut = 'rejete',
                        date_validation = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([$choixId]);
            }

            $_SESSION['success'] = "Le choix a été décliné avec succès!";
            header("Location: gestion_choix.php");
            exit;
        }
    }

} catch(PDOException $e) {
    $error = "Erreur de base de données: " . $e->getMessage();
    error_log("DB Error: " . $e->getMessage());
} catch(Exception $e) {
    $error = $e->getMessage();
}

function sanitize($data) {
    return htmlspecialchars($data ?? '', ENT_QUOTES, 'UTF-8');
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Validation des Choix - <?= sanitize($_SESSION['departement_nom']) ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #1E90FF;
            --primary-magenta: #FF00FF;
            --blue-transparent: rgba(30, 144, 255, 0.3);
            --dark-bg: #0a192f;
        }

        body {
            background: linear-gradient(rgba(10, 25, 47, 0.85), rgba(108, 27, 145, 0.85));
            color: white;
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .sidebar {
            width: 250px;
            position: fixed;
            height: 100vh;
            background: rgba(44, 62, 80, 0.95);
            color: white;
            padding-top: 20px;
            backdrop-filter: blur(5px);
            border-right: 2px solid var(--primary-blue);
            box-shadow: 4px 0 15px var(--blue-transparent);
            animation: borderGlow 8s infinite alternate;
            z-index: 1000;
        }

        @keyframes borderGlow {
            0% { border-color: var(--primary-blue); }
            50% { border-color: var(--primary-magenta); }
            100% { border-color: var(--primary-blue); }
        }

        .sidebar .nav-link {
            color: white;
            padding: 12px 15px;
            margin: 8px 0;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover {
            background: rgba(30, 144, 255, 0.1);
            transform: translateX(10px);
        }

        .sidebar .nav-link.active {
            background: rgba(30, 144, 255, 0.2);
            border-left: 4px solid var(--primary-blue);
        }

        .content-area {
            margin-left: 270px;
            padding: 20px;
        }

        .card {
            background: rgba(10, 25, 47, 0.9);
            border: 2px solid var(--primary-blue);
            border-radius: 10px;
            box-shadow: 0 5px 15px var(--blue-transparent);
            transition: all 0.3s ease;
        }

        .card-header {
            background: linear-gradient(90deg, var(--primary-blue) 0%, var(--dark-bg) 100%);
            color: white;
            padding: 15px;
            border-bottom: 2px solid var(--primary-magenta);
        }

        #choixTable {
            background: rgba(10, 25, 47, 0.9);
            border: 1px solid var(--primary-blue);
            color: white;
        }

        #choixTable thead {
            background: linear-gradient(90deg, var(--primary-blue) 0%, var(--dark-bg) 100%);
            border-bottom: 2px solid var(--primary-magenta);
        }

        .badge-en-attente {
            background-color: #ffc107;
            color: #212529;
            padding: 5px 10px;
            border-radius: 20px;
        }

        .badge-valide {
            background-color: #28a745;
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
        }

        .badge-rejete {
            background-color: #dc3545;
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
        }

        .comment-text {
            background-color: rgba(10, 25, 47, 0.7);
            border-left: 3px solid var(--primary-magenta);
            padding: 10px;
            margin-top: 5px;
            font-style: italic;
        }

        .modal-content {
            background: rgba(10, 25, 47, 0.95);
            border: 2px solid var(--primary-blue);
        }

        .form-control, .form-select, textarea {
            background-color: rgba(10, 25, 47, 0.7);
            color: white;
            border: 1px solid var(--primary-blue);
        }

        .form-control:focus, .form-select:focus, textarea:focus {
            background-color: rgba(10, 25, 47, 0.9);
            color: white;
            border-color: var(--primary-magenta);
            box-shadow: 0 0 0 0.25rem rgba(255, 0, 255, 0.25);
        }
    </style>
</head>
<body>
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <nav class="col-md-3 col-lg-2 sidebar">
            <div class="text-center mb-4">
                <img src="images/logo.png" alt="Logo" class="img-fluid mb-3" style="filter: drop-shadow(0 0 5px var(--primary-blue));">
                <h5 class="text-white" style="text-shadow: 0 0 10px var(--primary-blue);">
                    <?= sanitize($_SESSION['departement_nom']) ?>
                </h5>
            </div>
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" href="chef_dashboard.php">
                        <i class="fas fa-tachometer-alt me-2"></i> Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="gestion_modules.php">
                        <i class="fas fa-book-open me-2"></i> Unités d'Enseignement
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="gestion_professeurs.php">
                        <i class="fas fa-users-cog me-2"></i> Professeurs
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="affectation_ue.php">
                        <i class="fas fa-tasks me-2"></i> Affectations
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link active" href="gestion_choix.php">
                        <i class="fas fa-check-double me-2"></i> Validation Choix
                    </a>
                </li>
                <li class="nav-item mt-4">
                    <a class="nav-link text-danger" href="logout.php">
                        <i class="fas fa-sign-out-alt me-2"></i> Déconnexion
                    </a>
                </li>
            </ul>
        </nav>

        <!-- Contenu principal -->
        <main class="col-md-9 ms-sm-auto col-lg-10 p-4">
            <h1 class="mb-4 text-white"><i class="fas fa-check-double me-2"></i> Validation des Choix</h1>

            <?php if ($error): ?>
                <div class="alert alert-danger"><?= sanitize($error) ?></div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="alert alert-success"><?= sanitize($success) ?></div>
            <?php endif; ?>

            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="m-0"><i class="fas fa-hourglass-half me-2"></i> Demandes en Attente</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($choixEnAttente)): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i> Aucune demande en attente de validation
                        </div>
                    <?php else: ?>
                        <table class="table table-hover" id="choixTable">
                            <thead>
                                <tr>
                                    <th>Professeur</th>
                                    <th>Unité d'Enseignement</th>
                                    <th>Date de demande</th>
                                    <th>Statut</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($choixEnAttente as $choix): ?>
                                <tr>
                                    <td><?= sanitize($choix['professeur']) ?></td>
                                    <td><?= sanitize($choix['ue']) ?></td>
                                    <td><?= sanitize($choix['date_choix']) ?></td>
                                    <td>
                                        <span class="badge badge-en-attente">
                                            <i class="fas fa-clock me-1"></i> En attente
                                        </span>
                                    </td>
                                    <td>
                                        <form method="POST" class="d-inline">
                                            <input type="hidden" name="csrf_token" value="<?= sanitize($csrf_token) ?>">
                                            <input type="hidden" name="choix_id" value="<?= $choix['id'] ?>">
                                            <button type="submit" name="valider" class="btn btn-sm btn-success me-1">
                                                <i class="fas fa-check me-1"></i> Valider
                                            </button>
                                        </form>

                                        <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal"
                                                data-bs-target="#declinerModal<?= $choix['id'] ?>">
                                            <i class="fas fa-times me-1"></i> Décliner
                                        </button>

                                        <!-- Modal de déclination -->
                                        <div class="modal fade" id="declinerModal<?= $choix['id'] ?>" tabindex="-1" aria-hidden="true">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title">Décliner le choix</h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <form method="POST">
                                                        <div class="modal-body">
                                                            <input type="hidden" name="csrf_token" value="<?= sanitize($csrf_token) ?>">
                                                            <input type="hidden" name="choix_id" value="<?= $choix['id'] ?>">

                                                            <p>Vous êtes sur le point de décliner le choix de :</p>
                                                            <p><strong><?= sanitize($choix['professeur']) ?></strong> pour <strong><?= sanitize($choix['ue']) ?></strong></p>

                                                            <div class="mb-3">
                                                                <label class="form-label">Commentaire (obligatoire)</label>
                                                                <textarea class="form-control" name="commentaire" rows="3" required></textarea>
                                                            </div>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                                                <i class="fas fa-arrow-left me-1"></i> Annuler
                                                            </button>
                                                            <button type="submit" name="decliner" class="btn btn-danger">
                                                                <i class="fas fa-times me-1"></i> Confirmer
                                                            </button>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Choix validés -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="m-0"><i class="fas fa-check-circle me-2"></i> Choix Validés</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($choixValides)): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i> Aucun choix validé pour le moment
                        </div>
                    <?php else: ?>
                        <table class="table table-hover" id="choixValidesTable">
                            <thead>
                                <tr>
                                    <th>Professeur</th>
                                    <th>Unité d'Enseignement</th>
                                    <th>Date de demande</th>
                                    <th>Date de validation</th>
                                    <th>Statut</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($choixValides as $choix): ?>
                                <tr>
                                    <td><?= sanitize($choix['professeur']) ?></td>
                                    <td><?= sanitize($choix['ue']) ?></td>
                                    <td><?= sanitize($choix['date_choix']) ?></td>
                                    <td><?= sanitize($choix['date_validation']) ?></td>
                                    <td>
                                        <span class="badge badge-valide">
                                            <i class="fas fa-check me-1"></i> Validé
                                        </span>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Choix déclinés -->
            <div class="card">
                <div class="card-header">
                    <h5 class="m-0"><i class="fas fa-times-circle me-2"></i> Choix Déclinés</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($choixDeclines)): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i> Aucun choix décliné pour le moment
                        </div>
                    <?php else: ?>
                        <table class="table table-hover" id="choixDeclinesTable">
                            <thead>
                                <tr>
                                    <th>Professeur</th>
                                    <th>Unité d'Enseignement</th>
                                    <th>Date de demande</th>
                                    <th>Date de décision</th>
                                    <th>Statut</th>
                                    <th>Commentaire</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($choixDeclines as $choix): ?>
                                <tr>
                                    <td><?= sanitize($choix['professeur']) ?></td>
                                    <td><?= sanitize($choix['ue']) ?></td>
                                    <td><?= sanitize($choix['date_choix']) ?></td>
                                    <td><?= sanitize($choix['date_validation']) ?></td>
                                    <td>
                                        <span class="badge badge-rejete">
                                            <i class="fas fa-times me-1"></i> Décliné
                                        </span>
                                    </td>
                                    <td>
                                        <?php if (!empty($choix['commentaire'])): ?>
                                            <div class="comment-text">
                                                <?= nl2br(sanitize($choix['commentaire'])) ?>
                                            </div>
                                        <?php else: ?>
                                            <em class="text-muted">Aucun commentaire</em>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
</div>

<script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
<script>
    $(document).ready(function() {
        // Configuration commune pour tous les tableaux
        const dataTableConfig = {
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/fr-FR.json'
            },
            responsive: true,
            pageLength: 10
        };

        // Table des choix en attente
        $('#choixTable').DataTable({
            ...dataTableConfig,
            order: [[2, 'asc']]
        });

        // Table des choix validés
        $('#choixValidesTable').DataTable({
            ...dataTableConfig,
            order: [[3, 'desc']] // Tri par date de validation
        });

        // Table des choix déclinés
        $('#choixDeclinesTable').DataTable({
            ...dataTableConfig,
            order: [[3, 'desc']] // Tri par date de décision
        });
    });
</script>
</body>
</html>