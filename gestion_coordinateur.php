<?php
require_once 'config.php';
session_start();

// Vérification de l'authentification et des droits d'administrateur
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'admin') {
    header("Location: login_coordinateur.php");
    exit;
}

// Connexion à la base de données
try {
    $pdo = new PDO(
        "mysql:host=".DB_HOST.";dbname=".DB_NAME.";charset=utf8",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );

    // Récupérer la liste des départements
    $departements = $pdo->query("SELECT id_departement AS id, nom_departement AS nom FROM departement ORDER BY nom_departement")->fetchAll();

    // Récupérer la liste des filières
    $filieres = $pdo->query("SELECT id_filiere AS id, nom_filiere AS nom, id_departement FROM filiere ORDER BY nom_filiere")->fetchAll();

    // Récupérer la liste des spécialités
    $specialites = $pdo->query("SELECT id_specialite AS id, nom_specialite AS nom, id_departement FROM specialite ORDER BY nom_specialite")->fetchAll();

    // Vérifier si la colonne id_specialite existe dans la table utilisateurs
    $stmt = $pdo->prepare("
        SELECT COLUMN_NAME
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'utilisateurs' AND COLUMN_NAME = 'id_specialite'
    ");
    $stmt->execute([DB_NAME]);
    $specialite_column_exists = ($stmt->rowCount() > 0);

    // Vérifier si la colonne id_filiere existe dans la table utilisateurs
    $stmt = $pdo->prepare("
        SELECT COLUMN_NAME
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'utilisateurs' AND COLUMN_NAME = 'id_filiere'
    ");
    $stmt->execute([DB_NAME]);
    $filiere_column_exists = ($stmt->rowCount() > 0);

    // Récupérer la liste des coordinateurs existants
    if ($specialite_column_exists && $filiere_column_exists) {
        $coordinateurs = $pdo->query("
            SELECT u.id, u.nom, u.prenom, u.email,
                   u.id_filiere AS filiere_id,
                   f.nom_filiere AS filiere,
                   d.id_departement AS departement_id, d.nom_departement AS departement,
                   s.id_specialite AS specialite_id, s.nom_specialite AS specialite
            FROM utilisateurs u
            JOIN departement d ON u.id_departement = d.id_departement
            LEFT JOIN filiere f ON u.id_filiere = f.id_filiere
            LEFT JOIN specialite s ON u.id_specialite = s.id_specialite
            WHERE u.type_utilisateur = 'coordinateur'
            ORDER BY u.nom, u.prenom
        ")->fetchAll();
    } elseif ($specialite_column_exists) {
        $coordinateurs = $pdo->query("
            SELECT u.id, u.nom, u.prenom, u.email,
                   (SELECT f.id_filiere FROM filiere f WHERE f.id_departement = d.id_departement LIMIT 1) AS filiere_id,
                   (SELECT f.nom_filiere FROM filiere f WHERE f.id_departement = d.id_departement LIMIT 1) AS filiere,
                   d.id_departement AS departement_id, d.nom_departement AS departement,
                   s.id_specialite AS specialite_id, s.nom_specialite AS specialite
            FROM utilisateurs u
            JOIN departement d ON u.id_departement = d.id_departement
            LEFT JOIN specialite s ON u.id_specialite = s.id_specialite
            WHERE u.type_utilisateur = 'coordinateur'
            ORDER BY u.nom, u.prenom
        ")->fetchAll();
    } else {
        // Si les colonnes n'existent pas encore, on utilise une requête sans ces colonnes
        $coordinateurs = $pdo->query("
            SELECT u.id, u.nom, u.prenom, u.email,
                   (SELECT f.id_filiere FROM filiere f WHERE f.id_departement = d.id_departement LIMIT 1) AS filiere_id,
                   (SELECT f.nom_filiere FROM filiere f WHERE f.id_departement = d.id_departement LIMIT 1) AS filiere,
                   d.id_departement AS departement_id, d.nom_departement AS departement,
                   NULL AS specialite_id, 'Non définie' AS specialite
            FROM utilisateurs u
            JOIN departement d ON u.id_departement = d.id_departement
            WHERE u.type_utilisateur = 'coordinateur'
            ORDER BY u.nom, u.prenom
        ")->fetchAll();
    }

} catch(PDOException $e) {
    die("Erreur de base de données : " . htmlspecialchars($e->getMessage()));
}

// Traitement des actions (ajout, modification, suppression)
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'ajouter':
                // Code pour ajouter un coordinateur
                try {
                    $nom = trim($_POST['nom']);
                    $prenom = trim($_POST['prenom']);
                    $email = trim($_POST['email']);
                    $filiere_id = $_POST['filiere_id'];
                    $password = $_POST['password'];

                    // Validation
                    if (empty($nom) || empty($prenom) || empty($email) || empty($filiere_id) || empty($password)) {
                        throw new Exception('Tous les champs sont obligatoires');
                    }

                    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                        throw new Exception('Email invalide');
                    }

                    if (strlen($password) < 8) {
                        throw new Exception('Le mot de passe doit contenir au moins 8 caractères');
                    }

                    // Vérifier si l'email existe déjà
                    $stmt = $pdo->prepare("SELECT id FROM utilisateurs WHERE email = ?");
                    $stmt->execute([$email]);

                    if ($stmt->rowCount() > 0) {
                        throw new Exception('Cet email est déjà utilisé');
                    }

                    // Hachage du mot de passe
                    $password_hash = password_hash($password, PASSWORD_DEFAULT);

                    // Transaction pour garantir l'intégrité des données
                    $pdo->beginTransaction();

                    // Récupérer l'id_departement correspondant à la filière sélectionnée
                    $stmt = $pdo->prepare("SELECT id_departement FROM filiere WHERE id_filiere = ?");
                    $stmt->execute([$filiere_id]);
                    $departement_id = $stmt->fetchColumn();

                    // Récupérer l'id_specialite
                    $specialite_id = isset($_POST['specialite_id']) ? intval($_POST['specialite_id']) : null;

                    // Vérifier que la spécialité appartient bien au département
                    if ($specialite_id) {
                        $stmt = $pdo->prepare("SELECT id_specialite FROM specialite WHERE id_specialite = ? AND id_departement = ?");
                        $stmt->execute([$specialite_id, $departement_id]);
                        if (!$stmt->fetch()) {
                            // Si la spécialité n'appartient pas au département, on la met à null
                            $specialite_id = null;
                        }
                    }

                    // Création de l'utilisateur
                    $stmt = $pdo->prepare("
                        INSERT INTO utilisateurs (nom, prenom, email, mot_de_passe, type_utilisateur, id_departement, id_specialite, id_filiere)
                        VALUES (?, ?, ?, ?, 'coordinateur', ?, ?, ?)
                    ");
                    $stmt->execute([$nom, $prenom, $email, $password_hash, $departement_id, $specialite_id, $filiere_id]);

                    $pdo->commit();
                    $message = "Coordinateur ajouté avec succès.";
                    $messageType = "success";
                } catch(Exception $e) {
                    if ($pdo->inTransaction()) {
                        $pdo->rollBack();
                    }
                    $message = "Erreur lors de l'ajout : " . $e->getMessage();
                    $messageType = "danger";
                }
                break;

            case 'modifier':
                // Code pour modifier un coordinateur
                try {
                    $user_id = $_POST['id'];
                    $nom = trim($_POST['nom']);
                    $prenom = trim($_POST['prenom']);
                    $email = trim($_POST['email']);
                    $filiere_id = $_POST['filiere_id'];
                    $password = $_POST['password'];

                    // Validation
                    if (empty($nom) || empty($prenom) || empty($email) || empty($filiere_id)) {
                        throw new Exception('Tous les champs sont obligatoires sauf le mot de passe');
                    }

                    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                        throw new Exception('Email invalide');
                    }

                    if (!empty($password) && strlen($password) < 8) {
                        throw new Exception('Le mot de passe doit contenir au moins 8 caractères');
                    }

                    // Vérifier si l'email existe déjà pour un autre utilisateur
                    $stmt = $pdo->prepare("SELECT id FROM utilisateurs WHERE email = ? AND id != ?");
                    $stmt->execute([$email, $user_id]);

                    if ($stmt->rowCount() > 0) {
                        throw new Exception('Cet email est déjà utilisé par un autre utilisateur');
                    }

                    // Transaction pour garantir l'intégrité des données
                    $pdo->beginTransaction();

                    // Récupérer l'id_departement correspondant à la filière sélectionnée
                    $stmt = $pdo->prepare("SELECT id_departement FROM filiere WHERE id_filiere = ?");
                    $stmt->execute([$filiere_id]);
                    $departement_id = $stmt->fetchColumn();

                    // Récupérer l'id_specialite
                    $specialite_id = isset($_POST['specialite_id']) ? intval($_POST['specialite_id']) : null;

                    // Vérifier que la spécialité appartient bien au département
                    if ($specialite_id) {
                        $stmt = $pdo->prepare("SELECT id_specialite FROM specialite WHERE id_specialite = ? AND id_departement = ?");
                        $stmt->execute([$specialite_id, $departement_id]);
                        if (!$stmt->fetch()) {
                            // Si la spécialité n'appartient pas au département, on la met à null
                            $specialite_id = null;
                        }
                    }

                    // Mise à jour de l'utilisateur
                    $sql = "UPDATE utilisateurs SET nom = ?, prenom = ?, email = ?, id_departement = ?, id_specialite = ?, id_filiere = ?";
                    $params = [$nom, $prenom, $email, $departement_id, $specialite_id, $filiere_id];

                    if (!empty($password)) {
                        $sql .= ", mot_de_passe = ?";
                        $params[] = password_hash($password, PASSWORD_DEFAULT);
                    }

                    $sql .= " WHERE id = ?";
                    $params[] = $user_id;

                    $stmt = $pdo->prepare($sql);
                    $stmt->execute($params);

                    $pdo->commit();
                    $message = "Coordinateur modifié avec succès.";
                    $messageType = "success";
                } catch(Exception $e) {
                    if ($pdo->inTransaction()) {
                        $pdo->rollBack();
                    }
                    $message = "Erreur lors de la modification : " . $e->getMessage();
                    $messageType = "danger";
                }
                break;

            case 'supprimer':
                // Code pour supprimer un coordinateur
                try {
                    $user_id = $_POST['id'];

                    // Transaction pour garantir l'intégrité des données
                    $pdo->beginTransaction();

                    // Suppression de l'utilisateur
                    $stmt = $pdo->prepare("
                        DELETE FROM utilisateurs
                        WHERE id = ? AND type_utilisateur = 'coordinateur'
                    ");
                    $stmt->execute([$user_id]);

                    $pdo->commit();
                    $message = "Coordinateur supprimé avec succès.";
                    $messageType = "success";
                } catch(Exception $e) {
                    if ($pdo->inTransaction()) {
                        $pdo->rollBack();
                    }
                    $message = "Erreur lors de la suppression : " . $e->getMessage();
                    $messageType = "danger";
                }
                break;
        }

        // Rediriger pour éviter la resoumission du formulaire
        header("Location: gestion_coordinateur.php?message=" . urlencode($message) . "&messageType=" . urlencode($messageType));
        exit;
    }
}

// Récupérer le message de la redirection
if (isset($_GET['message']) && isset($_GET['messageType'])) {
    $message = $_GET['message'];
    $messageType = $_GET['messageType'];
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion des Coordinateurs - ENSAH</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/responsive/2.4.1/css/responsive.bootstrap5.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/buttons/2.4.1/css/buttons.bootstrap5.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #1E90FF;
            --primary-magenta: magenta;
            --blue-transparent: rgba(30, 144, 255, 0.3);
            --dark-bg: #0a192f;
        }

        body {
            background: linear-gradient(rgba(10, 25, 47, 0.85), rgba(108, 27, 145, 0.85)),
            url('images/background.jpg') center center/cover fixed;
            color: white;
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow-x: hidden;
        }

        .sidebar {
            background: rgba(44, 62, 80, 0.95);
            backdrop-filter: blur(5px);
            border-right: 2px solid var(--primary-blue);
            box-shadow: 4px 0 15px var(--blue-transparent);
            animation: borderGlow 8s infinite alternate;
        }

        /* Ajustement pour le contenu principal */
        main {
            transition: all 0.3s ease;
            margin-left: 280px; /* Correspond à la largeur de la sidebar */
        }

        @media (max-width: 991.98px) {
            main {
                margin-left: 0;
                padding-left: 15px !important;
                padding-right: 15px !important;
            }
        }

        .card {
            background: rgba(10, 25, 47, 0.9);
            border: 2px solid var(--primary-blue);
            border-radius: 10px;
            animation: cardBorderPulse 10s infinite;
        }

        .table {
            color: white;
            width: 100%;
            margin-bottom: 1rem;
            border-collapse: collapse;
            table-layout: fixed;
        }

        .table thead {
            background: linear-gradient(90deg, var(--primary-blue) 0%, var(--dark-bg) 100%);
        }

        .table thead th {
            border-bottom: 2px solid var(--primary-blue);
            color: var(--primary-blue);
            padding: 0.5rem;
            vertical-align: middle;
            text-align: left;
            font-size: 0.9rem;
        }

        .table tbody td {
            padding: 0.5rem;
            vertical-align: middle;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            font-size: 0.9rem;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        /* Ajustement des largeurs de colonnes */
        .table th:nth-child(1), .table td:nth-child(1) { /* ID */
            width: 5%;
        }
        .table th:nth-child(2), .table td:nth-child(2), /* Nom */
        .table th:nth-child(3), .table td:nth-child(3) { /* Prénom */
            width: 12%;
        }
        .table th:nth-child(4), .table td:nth-child(4) { /* Email */
            width: 18%;
        }
        .table th:nth-child(5), .table td:nth-child(5), /* Filière */
        .table th:nth-child(6), .table td:nth-child(6), /* Département */
        .table th:nth-child(7), .table td:nth-child(7) { /* Spécialité */
            width: 13%;
        }
        .table th:nth-child(8), .table td:nth-child(8) { /* Actions */
            width: 14%;
            text-align: center;
        }

        /* Ajustement de la largeur du conteneur du tableau */
        .table-responsive {
            max-width: 100%;
            overflow-x: auto;
        }

        /* Ajustement pour DataTables */
        .dataTables_wrapper {
            font-size: 0.9rem;
        }

        .dataTables_wrapper .dt-buttons {
            margin-bottom: 1rem;
        }

        /* Réduire la taille des boutons */
        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-magenta) 100%);
            border: none;
        }

        .btn-outline-primary {
            border-color: var(--primary-blue);
            color: var(--primary-blue);
        }

        .btn-outline-primary:hover {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-magenta) 100%);
            border-color: transparent;
        }

        .modal-content {
            background: rgba(10, 25, 47, 0.95);
            border: 2px solid var(--primary-blue);
        }

        .form-control, .form-select {
            background-color: rgba(255, 255, 255, 0.1);
            border: 1px solid var(--primary-blue);
            color: white;
        }

        .form-control:focus, .form-select:focus {
            background-color: rgba(255, 255, 255, 0.15);
            border-color: var(--primary-magenta);
            color: white;
            box-shadow: 0 0 0 0.25rem rgba(255, 0, 255, 0.25);
        }

        .form-control::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .form-select option {
            background-color: var(--dark-bg);
        }

        @keyframes cardBorderPulse {
            0% { border-color: var(--primary-blue); box-shadow: 0 5px 15px var(--blue-transparent); }
            50% { border-color: var(--primary-magenta); box-shadow: 0 5px 20px rgba(255, 0, 255, 0.3); }
            100% { border-color: var(--primary-blue); box-shadow: 0 5px 15px var(--blue-transparent); }
        }

        /* Styles pour les appareils mobiles */
        @media (max-width: 767.98px) {
            .table th, .table td {
                font-size: 0.8rem;
                padding: 0.4rem;
            }

            .btn-sm {
                padding: 0.2rem 0.4rem;
                font-size: 0.7rem;
            }

            h1 {
                font-size: 1.8rem;
            }

            .card-header h5 {
                font-size: 1rem;
            }

            .modal-dialog {
                margin: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Inclure la nouvelle sidebar -->
    <?php include 'sidebar_new.php'; ?>

    <!-- Contenu principal -->
    <main class="py-4">
        <div class="container-fluid">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="ms-2">Gestion des Coordinateurs</h1>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#ajouterModal">
                    <i class="fas fa-plus-circle me-2"></i><span class="d-none d-sm-inline">Ajouter un coordinateur</span><span class="d-inline d-sm-none">Ajouter</span>
                </button>
            </div>

            <?php if ($message): ?>
            <div class="alert alert-<?= $messageType ?> alert-dismissible fade show mx-2" role="alert">
                <?= htmlspecialchars($message) ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            <?php endif; ?>

            <div class="card shadow mb-4 mx-2">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h5 class="m-0 text-white"><i class="fas fa-user-cog me-2"></i>Liste des coordinateurs</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="coordinateursTable">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Nom</th>
                                    <th>Prénom</th>
                                    <th class="d-none d-md-table-cell">Email</th>
                                    <th class="d-none d-md-table-cell">Filière</th>
                                    <th class="d-none d-lg-table-cell">Département</th>
                                    <th class="d-none d-lg-table-cell">Spécialité</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($coordinateurs as $coord): ?>
                                <tr>
                                    <td><?= htmlspecialchars($coord['id']) ?></td>
                                    <td><?= htmlspecialchars($coord['nom']) ?></td>
                                    <td><?= htmlspecialchars($coord['prenom']) ?></td>
                                    <td class="d-none d-md-table-cell"><?= htmlspecialchars($coord['email']) ?></td>
                                    <td class="d-none d-md-table-cell"><?= htmlspecialchars($coord['filiere']) ?></td>
                                    <td class="d-none d-lg-table-cell"><?= htmlspecialchars($coord['departement']) ?></td>
                                    <td class="d-none d-lg-table-cell"><?= htmlspecialchars($coord['specialite']) ?></td>
                                    <td class="text-center">
                                        <button class="btn btn-sm btn-outline-primary me-1 btn-modifier"
                                                data-id="<?= $coord['id'] ?>"
                                                data-nom="<?= htmlspecialchars($coord['nom']) ?>"
                                                data-prenom="<?= htmlspecialchars($coord['prenom']) ?>"
                                                data-email="<?= htmlspecialchars($coord['email']) ?>"
                                                data-filiere="<?= $coord['filiere_id'] ?>"
                                                data-departement="<?= $coord['departement_id'] ?>"
                                                data-specialite="<?= $coord['specialite_id'] ?>">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger btn-supprimer"
                                                data-id="<?= $coord['id'] ?>"
                                                data-nom="<?= htmlspecialchars($coord['prenom'] . ' ' . $coord['nom']) ?>">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Modal Ajouter -->
    <div class="modal fade" id="ajouterModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Ajouter un coordinateur</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="post">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="ajouter">

                        <div class="mb-3">
                            <label for="nom" class="form-label">Nom</label>
                            <input type="text" class="form-control" id="nom" name="nom" required>
                        </div>

                        <div class="mb-3">
                            <label for="prenom" class="form-label">Prénom</label>
                            <input type="text" class="form-control" id="prenom" name="prenom" required>
                        </div>

                        <div class="mb-3">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>

                        <div class="mb-3">
                            <label for="departement_id" class="form-label">Département</label>
                            <select class="form-select" id="departement_id" name="departement_id" required>
                                <option value="">Sélectionner un département</option>
                                <?php foreach ($departements as $dept): ?>
                                <option value="<?= $dept['id'] ?>"><?= htmlspecialchars($dept['nom']) ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="filiere_id" class="form-label">Filière</label>
                            <select class="form-select" id="filiere_id" name="filiere_id" required>
                                <option value="">Sélectionner d'abord un département</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="specialite_id" class="form-label">Spécialité</label>
                            <select class="form-select" id="specialite_id" name="specialite_id">
                                <option value="">Sélectionner d'abord une filière</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label">Mot de passe</label>
                            <input type="password" class="form-control" id="password" name="password" required minlength="8">
                            <div class="form-text text-light">Le mot de passe doit contenir au moins 8 caractères.</div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                        <button type="submit" class="btn btn-primary">Ajouter</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal Modifier -->
    <div class="modal fade" id="modifierModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Modifier le coordinateur</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="post">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="modifier">
                        <input type="hidden" name="id" id="modifier_id">

                        <div class="mb-3">
                            <label for="modifier_nom" class="form-label">Nom</label>
                            <input type="text" class="form-control" id="modifier_nom" name="nom" required>
                        </div>

                        <div class="mb-3">
                            <label for="modifier_prenom" class="form-label">Prénom</label>
                            <input type="text" class="form-control" id="modifier_prenom" name="prenom" required>
                        </div>

                        <div class="mb-3">
                            <label for="modifier_email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="modifier_email" name="email" required>
                        </div>

                        <div class="mb-3">
                            <label for="modifier_departement_id" class="form-label">Département</label>
                            <select class="form-select" id="modifier_departement_id" name="departement_id" required>
                                <option value="">Sélectionner un département</option>
                                <?php foreach ($departements as $dept): ?>
                                <option value="<?= $dept['id'] ?>"><?= htmlspecialchars($dept['nom']) ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="modifier_filiere_id" class="form-label">Filière</label>
                            <select class="form-select" id="modifier_filiere_id" name="filiere_id" required>
                                <option value="">Sélectionner d'abord un département</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="modifier_specialite_id" class="form-label">Spécialité</label>
                            <select class="form-select" id="modifier_specialite_id" name="specialite_id">
                                <option value="">Sélectionner d'abord une filière</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="modifier_password" class="form-label">Mot de passe (laisser vide pour ne pas changer)</label>
                            <input type="password" class="form-control" id="modifier_password" name="password" minlength="8">
                            <div class="form-text text-light">Le mot de passe doit contenir au moins 8 caractères.</div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                        <button type="submit" class="btn btn-primary">Enregistrer</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal Supprimer -->
    <div class="modal fade" id="supprimerModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirmer la suppression</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Êtes-vous sûr de vouloir supprimer le coordinateur <span id="supprimer_nom"></span> ?</p>
                    <p class="text-danger">Cette action est irréversible.</p>
                </div>
                <form method="post">
                    <input type="hidden" name="action" value="supprimer">
                    <input type="hidden" name="id" id="supprimer_id">
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                        <button type="submit" class="btn btn-danger">Supprimer</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.4.1/js/dataTables.responsive.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.4.1/js/responsive.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.bootstrap5.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.html5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.print.min.js"></script>

    <!-- Script pour gérer la sidebar sur mobile -->
    <script>
        // Fonction pour gérer l'affichage de la sidebar sur mobile
        function handleSidebarOnMobile() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.querySelector('main');

            if (window.innerWidth < 992) {
                sidebar.style.transform = 'translateX(-100%)';
                sidebar.style.transition = 'transform 0.3s ease';
                mainContent.style.marginLeft = '0';

                // Ajouter un bouton pour afficher/masquer la sidebar si pas déjà présent
                if (!document.getElementById('sidebarToggle')) {
                    const toggleBtn = document.createElement('button');
                    toggleBtn.id = 'sidebarToggle';
                    toggleBtn.className = 'btn btn-sm btn-primary position-fixed';
                    toggleBtn.style.top = '10px';
                    toggleBtn.style.left = '10px';
                    toggleBtn.style.zIndex = '1050';
                    toggleBtn.innerHTML = '<i class="fas fa-bars"></i>';

                    toggleBtn.addEventListener('click', function() {
                        if (sidebar.style.transform === 'translateX(0px)') {
                            sidebar.style.transform = 'translateX(-100%)';
                        } else {
                            sidebar.style.transform = 'translateX(0)';
                        }
                    });

                    document.body.appendChild(toggleBtn);

                    // Fermer la sidebar quand on clique en dehors
                    document.addEventListener('click', function(e) {
                        if (window.innerWidth < 992 &&
                            !sidebar.contains(e.target) &&
                            e.target !== toggleBtn &&
                            sidebar.style.transform === 'translateX(0px)') {
                            sidebar.style.transform = 'translateX(-100%)';
                        }
                    });
                }
            } else {
                sidebar.style.transform = 'translateX(0)';
                mainContent.style.marginLeft = '280px';

                // Supprimer le bouton toggle si présent
                const toggleBtn = document.getElementById('sidebarToggle');
                if (toggleBtn) {
                    toggleBtn.remove();
                }
            }
        }

        // Exécuter au chargement et au redimensionnement
        window.addEventListener('load', handleSidebarOnMobile);
        window.addEventListener('resize', handleSidebarOnMobile);
    </script>
    <script>
        // Données globales pour les filières et spécialités
        const filieresData = <?= json_encode($filieres) ?>;
        const specialitesData = <?= json_encode($specialites) ?>;

        // Fonction pour charger les filières en fonction du département sélectionné
        function chargerFilieres(departementId, selecteurFiliere, filiereIdSelectionne = null) {
            const $selectFiliere = $(selecteurFiliere);
            $selectFiliere.empty().append('<option value="">Chargement...</option>');

            if (!departementId) {
                $selectFiliere.empty().append('<option value="">Sélectionner d\'abord un département</option>');
                return;
            }

            // Filtrer les filières par département
            const filieres = filieresData.filter(filiere => filiere.id_departement == departementId);

            $selectFiliere.empty().append('<option value="">Sélectionner une filière</option>');

            if (filieres.length > 0) {
                filieres.forEach(filiere => {
                    $selectFiliere.append(new Option(filiere.nom, filiere.id));
                });

                if (filiereIdSelectionne) {
                    $selectFiliere.val(filiereIdSelectionne);
                }
            } else {
                $selectFiliere.append('<option value="">Aucune filière disponible</option>');
            }
        }

        // Fonction pour charger les spécialités en fonction du département sélectionné
        function chargerSpecialites(departementId, selecteurSpecialite, specialiteIdSelectionne = null) {
            const $selectSpecialite = $(selecteurSpecialite);
            $selectSpecialite.empty().append('<option value="">Chargement...</option>');

            if (!departementId) {
                $selectSpecialite.empty().append('<option value="">Sélectionner d\'abord un département</option>');
                return;
            }

            // Filtrer les spécialités par département
            const specialites = specialitesData.filter(specialite => specialite.id_departement == departementId);

            $selectSpecialite.empty().append('<option value="">Sélectionner une spécialité</option>');

            if (specialites.length > 0) {
                specialites.forEach(specialite => {
                    $selectSpecialite.append(new Option(specialite.nom, specialite.id));
                });

                if (specialiteIdSelectionne) {
                    $selectSpecialite.val(specialiteIdSelectionne);
                }
            } else {
                $selectSpecialite.append('<option value="">Aucune spécialité disponible</option>');
            }
        }

        $(document).ready(function() {
            // Initialisation de DataTables
            $('#coordinateursTable').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/fr-FR.json'
                },
                dom: '<"row"<"col-sm-12 col-md-6"B><"col-sm-12 col-md-6"f>>rt<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
                responsive: true,
                autoWidth: false,
                pageLength: 10,
                lengthMenu: [[10, 25, 50, -1], [10, 25, 50, "Tous"]],
                columnDefs: [
                    { responsivePriority: 1, targets: [0, 1, 2, 7] }, // Colonnes prioritaires
                    { responsivePriority: 2, targets: [3, 4] },
                    { responsivePriority: 3, targets: [5, 6] },
                    { orderable: false, targets: [7] },
                    { className: "text-center", targets: [7] }
                ],
                buttons: [
                    {
                        extend: 'excel',
                        text: '<i class="fas fa-file-excel"></i> <span class="d-none d-sm-inline">Excel</span>',
                        className: 'btn btn-sm btn-outline-primary me-1',
                        exportOptions: {
                            columns: [0, 1, 2, 3, 4, 5, 6]
                        }
                    },
                    {
                        extend: 'pdf',
                        text: '<i class="fas fa-file-pdf"></i> <span class="d-none d-sm-inline">PDF</span>',
                        className: 'btn btn-sm btn-outline-primary me-1',
                        exportOptions: {
                            columns: [0, 1, 2, 3, 4, 5, 6]
                        }
                    },
                    {
                        extend: 'print',
                        text: '<i class="fas fa-print"></i> <span class="d-none d-sm-inline">Imprimer</span>',
                        className: 'btn btn-sm btn-outline-primary',
                        exportOptions: {
                            columns: [0, 1, 2, 3, 4, 5, 6]
                        }
                    }
                ],
                drawCallback: function() {
                    // Ajuster les largeurs des colonnes après le rendu
                    $('.dataTables_scrollHeadInner, .dataTable').css('width', '100%');
                }
            });

            // Gestion des sélecteurs de département et filière dans le formulaire d'ajout
            $('#departement_id').change(function() {
                const departementId = $(this).val();
                chargerFilieres(departementId, '#filiere_id');
                chargerSpecialites(departementId, '#specialite_id');
            });

            // Gestion des sélecteurs de département et filière dans le formulaire de modification
            $('#modifier_departement_id').change(function() {
                const departementId = $(this).val();
                chargerFilieres(departementId, '#modifier_filiere_id');
                chargerSpecialites(departementId, '#modifier_specialite_id');
            });

            // Gestion du modal de modification
            $('.btn-modifier').click(function() {
                const id = $(this).data('id');
                const nom = $(this).data('nom');
                const prenom = $(this).data('prenom');
                const email = $(this).data('email');
                const filiereId = $(this).data('filiere');
                const departementId = $(this).data('departement');
                const specialiteId = $(this).data('specialite');

                $('#modifier_id').val(id);
                $('#modifier_nom').val(nom);
                $('#modifier_prenom').val(prenom);
                $('#modifier_email').val(email);

                // Sélectionner le département
                $('#modifier_departement_id').val(departementId);

                // Charger les filières et sélectionner celle du coordinateur
                chargerFilieres(departementId, '#modifier_filiere_id', filiereId);

                // Charger les spécialités et sélectionner celle du coordinateur
                chargerSpecialites(departementId, '#modifier_specialite_id', specialiteId);

                $('#modifierModal').modal('show');
            });

            // Gestion du modal de suppression
            $('.btn-supprimer').click(function() {
                const id = $(this).data('id');
                const nom = $(this).data('nom');

                $('#supprimer_id').val(id);
                $('#supprimer_nom').text(nom);

                $('#supprimerModal').modal('show');
            });
        });
    </script>
</body>
</html>