<?php
require_once 'config.php';
session_start();

// Vérification des droits admin
if (!isset($_SESSION['user_id']) || $_SESSION['user_type'] !== 'admin') {
    header("Location: login.php");
    exit;
}

$error = '';
$success = '';
$enseignants = [];

try {
    $pdo = new PDO(
        "mysql:host=".DB_HOST.";dbname=".DB_NAME.";charset=utf8",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );

    // Récupérer la liste des départements et spécialités
    $departements = $pdo->query("SELECT id_departement, nom_departement FROM departement")->fetchAll();
    $specialites = $pdo->query("SELECT id_specialite, nom_specialite, id_departement FROM specialite")->fetchAll();

    // Récupérer la liste des enseignants
    $enseignants = $pdo->query("
        SELECT u.*, d.nom_departement, s.nom_specialite
        FROM utilisateurs u
        LEFT JOIN departement d ON u.id_departement = d.id_departement
        LEFT JOIN specialite s ON u.id_specialite = s.id_specialite
        WHERE u.type_utilisateur = 'enseignant'
        ORDER BY u.nom, u.prenom
    ")->fetchAll();

    // Traitement du formulaire
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (isset($_POST['action']) && $_POST['action'] === 'ajouter') {
            $nom = trim($_POST['nom']);
            $prenom = trim($_POST['prenom']);
            $email = trim($_POST['email']);
            $type = $_POST['type'];
            $departement_id = $_POST['departement_id'];
            $specialite_id = $_POST['specialite_id'];
            $password = $_POST['password'];

            // Validation
            if (empty($nom) || empty($prenom) || empty($email) || empty($type) || empty($password)) {
                $error = 'Tous les champs obligatoires sont requis';
            } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $error = 'Email invalide';
            } elseif (strlen($password) < 8) {
                $error = 'Le mot de passe doit contenir au moins 8 caractères';
            } else {
                // Vérifier si l'email existe déjà
                $stmt = $pdo->prepare("SELECT id FROM utilisateurs WHERE email = ?");
                $stmt->execute([$email]);

                if ($stmt->rowCount() > 0) {
                    $error = 'Cet email est déjà utilisé';
                } else {
                    // Hachage du mot de passe
                    $password_hash = password_hash($password, PASSWORD_DEFAULT);

                    // Définir le type d'utilisateur comme "enseignant"
                    $type_utilisateur = 'enseignant';

                    // Transaction
                    $pdo->beginTransaction();

                    try {
                        // Insérer dans la table utilisateurs
                        $stmt = $pdo->prepare("
                            INSERT INTO utilisateurs
                            (nom, prenom, email, mot_de_passe, type_utilisateur, id_departement, id_specialite)
                            VALUES (?, ?, ?, ?, ?, ?, ?)
                        ");
                        $stmt->execute([
                            $nom,
                            $prenom,
                            $email,
                            $password_hash,
                            $type_utilisateur,
                            $departement_id ?: null,
                            $specialite_id ?: null
                        ]);

                        // Récupérer l'ID de l'utilisateur inséré
                        $user_id = $pdo->lastInsertId();

                        // Insérer également dans la table professeurs
                        $stmt = $pdo->prepare("
                            INSERT INTO professeurs
                            (id, nom, prenom, email, type, id_departement, id_specialite)
                            VALUES (?, ?, ?, ?, ?, ?, ?)
                        ");
                        $stmt->execute([
                            $user_id,
                            $nom,
                            $prenom,
                            $email,
                            $type, // 'permanent' ou 'vacataire'
                            $departement_id ?: null,
                            $specialite_id ?: null
                        ]);

                        $pdo->commit();
                        $success = 'Enseignant ajouté avec succès';
                        header("Location: gestion_enseignant.php");
                        exit;
                    } catch (Exception $e) {
                        $pdo->rollBack();
                        $error = 'Erreur lors de l\'ajout : ' . $e->getMessage();
                    }
                }
            }
        } elseif (isset($_POST['action']) && $_POST['action'] === 'modifier') {
            try {
                $user_id = $_POST['id'];
                $nom = trim($_POST['nom']);
                $prenom = trim($_POST['prenom']);
                $email = trim($_POST['email']);
                $type = $_POST['type'];
                $departement_id = $_POST['departement_id'];
                $specialite_id = isset($_POST['specialite_id']) && !empty($_POST['specialite_id']) ? $_POST['specialite_id'] : null;
                $password = $_POST['password'];

                // Validation
                if (empty($nom) || empty($prenom) || empty($email) || empty($type) || empty($departement_id)) {
                    throw new Exception('Tous les champs obligatoires sont requis');
                }

                if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                    throw new Exception('Email invalide');
                }

                if (!empty($password) && strlen($password) < 8) {
                    throw new Exception('Le mot de passe doit contenir au moins 8 caractères');
                }

                // Vérifier si l'email existe déjà pour un autre utilisateur
                $stmt = $pdo->prepare("SELECT id FROM utilisateurs WHERE email = ? AND id != ?");
                $stmt->execute([$email, $user_id]);

                if ($stmt->rowCount() > 0) {
                    throw new Exception('Cet email est déjà utilisé par un autre utilisateur');
                }

                // Définir le type d'utilisateur comme "enseignant"
                $type_utilisateur = 'enseignant';

                // Transaction
                $pdo->beginTransaction();

                // Mise à jour de l'utilisateur
                $sql = "UPDATE utilisateurs SET nom = ?, prenom = ?, email = ?, type_utilisateur = ?, id_departement = ?, id_specialite = ?";
                $params = [$nom, $prenom, $email, $type_utilisateur, $departement_id, $specialite_id];

                if (!empty($password)) {
                    $sql .= ", mot_de_passe = ?";
                    $params[] = password_hash($password, PASSWORD_DEFAULT);
                }

                $sql .= " WHERE id = ?";
                $params[] = $user_id;

                $stmt = $pdo->prepare($sql);
                $stmt->execute($params);

                // Vérifier si l'enseignant existe déjà dans la table professeurs
                $stmt = $pdo->prepare("SELECT id FROM professeurs WHERE id = ?");
                $stmt->execute([$user_id]);

                if ($stmt->rowCount() > 0) {
                    // Mise à jour dans la table professeurs
                    $sql_prof = "UPDATE professeurs SET nom = ?, prenom = ?, email = ?, type = ?, id_departement = ?, id_specialite = ? WHERE id = ?";
                    $params_prof = [$nom, $prenom, $email, $type, $departement_id, $specialite_id, $user_id];

                    $stmt = $pdo->prepare($sql_prof);
                    $stmt->execute($params_prof);
                } else {
                    // Insertion dans la table professeurs
                    $stmt = $pdo->prepare("
                        INSERT INTO professeurs
                        (id, nom, prenom, email, type, id_departement, id_specialite)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ");
                    $stmt->execute([
                        $user_id,
                        $nom,
                        $prenom,
                        $email,
                        $type,
                        $departement_id,
                        $specialite_id
                    ]);
                }

                $pdo->commit();
                $success = 'Enseignant modifié avec succès';
                header("Location: gestion_enseignant.php");
                exit;
            } catch (Exception $e) {
                if ($pdo->inTransaction()) {
                    $pdo->rollBack();
                }
                $error = 'Erreur lors de la modification : ' . $e->getMessage();
            }
        } elseif (isset($_POST['action']) && $_POST['action'] === 'supprimer') {
            $user_id = $_POST['user_id'];

            $pdo->beginTransaction();
            try {
                // Supprimer d'abord de la table professeurs
                $stmt = $pdo->prepare("DELETE FROM professeurs WHERE id = ?");
                $stmt->execute([$user_id]);

                // Puis supprimer de la table utilisateurs
                $stmt = $pdo->prepare("DELETE FROM utilisateurs WHERE id = ? AND type_utilisateur = 'enseignant'");
                $stmt->execute([$user_id]);

                $pdo->commit();
                $success = 'Enseignant supprimé avec succès';
                header("Location: gestion_enseignant.php");
                exit;
            } catch (Exception $e) {
                $pdo->rollBack();
                $error = 'Erreur lors de la suppression : ' . $e->getMessage();
            }
        }
    }
} catch(PDOException $e) {
    $error = 'Erreur de base de données : ' . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion des Enseignants - ENSAH</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #1E90FF;
            --primary-green: #28a745;
            --primary-orange: #fd7e14;
            --primary-purple: #6c1b91;
            --primary-teal: #20c997;
            --dark-bg: #0a192f;
            --modal-bg: #1a2a43;
            --input-bg: rgba(255, 255, 255, 0.15);
        }

        body {
            background: linear-gradient(rgba(10, 25, 47, 0.85), rgba(108, 27, 145, 0.85)),
            url('images/background.jpg') center center/cover fixed;
            color: white;
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .card {
            background: rgba(10, 25, 47, 0.9);
            border: 2px solid var(--primary-blue);
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(30, 144, 255, 0.3);
        }



        .badge-permanent {
            background-color: var(--primary-green);
        }

        .badge-vacataire {
            background-color: var(--primary-orange);
        }

        #enseignantsTable {
            background: rgba(10, 25, 47, 0.9);
            border: 1px solid var(--primary-blue);
            color: white;
        }

        #enseignantsTable thead {
            background: linear-gradient(90deg, var(--primary-blue) 0%, var(--dark-bg) 100%);
            border-bottom: 2px solid #6c1b91;
        }

        .table-hover tbody tr:hover {
            background-color: rgba(30, 144, 255, 0.2) !important;
        }

        /* Styles pour les modals */
        .modal-content {
            background: var(--modal-bg);
            color: white;
            border: 2px solid var(--primary-teal);
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(32, 201, 151, 0.4);
        }

        .modal-header {
            border-bottom: 1px solid var(--primary-teal);
            background: linear-gradient(90deg, var(--dark-bg), var(--primary-purple));
            border-radius: 13px 13px 0 0;
        }

        .modal-footer {
            border-top: 1px solid var(--primary-teal);
            background: rgba(10, 25, 47, 0.7);
            border-radius: 0 0 13px 13px;
        }

        .modal-title {
            color: var(--primary-teal);
            font-weight: bold;
        }

        .form-label {
            color: var(--primary-teal);
            font-weight: 500;
        }

        .form-control, .form-select {
            background-color: var(--input-bg);
            border: 1px solid var(--primary-teal);
            color: white;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            background-color: rgba(255, 255, 255, 0.25);
            border-color: var(--primary-teal);
            color: white;
            box-shadow: 0 0 0 0.25rem rgba(32, 201, 151, 0.25);
        }

        .btn-close {
            filter: invert(1) brightness(200%);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-teal) 100%);
            border: none;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--primary-teal) 0%, var(--primary-blue) 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(32, 201, 151, 0.3);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            border: none;
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background: linear-gradient(135deg, #495057 0%, #6c757d 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(108, 117, 125, 0.3);
        }

        .btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            border: none;
            transition: all 0.3s ease;
        }

        .btn-danger:hover {
            background: linear-gradient(135deg, #c82333 0%, #dc3545 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
        }

        /* Animation pour les modals */
        .modal.fade .modal-dialog {
            transition: transform 0.3s ease-out;
            transform: scale(0.95);
        }

        .modal.show .modal-dialog {
            transform: scale(1);
        }

        /* Styles pour la barre latérale */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            bottom: 0;
            width: 250px;
            z-index: 100;
            padding: 20px 0;
            background: linear-gradient(180deg, rgba(10, 25, 47, 0.95) 0%, rgba(108, 27, 145, 0.95) 100%);
            box-shadow: 5px 0 15px rgba(0, 0, 0, 0.3);
            overflow-y: auto;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 12px 20px;
            margin: 4px 0;
            border-radius: 0 30px 30px 0;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .sidebar .nav-link:hover {
            color: white;
            background-color: rgba(30, 144, 255, 0.2);
            transform: translateX(5px);
        }

        .sidebar .nav-link i {
            width: 24px;
            text-align: center;
            margin-right: 8px;
            color: var(--primary-blue);
        }

        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(30, 144, 255, 0.3);
            border-left: 3px solid var(--primary-blue);
        }

        /* Styles responsives pour le sidebar */
        @media (max-width: 991.98px) {
            .sidebar {
                width: 220px;
            }

            main[style*="margin-left: 250px"] {
                margin-left: 220px !important;
            }

            .sidebar .nav-link {
                padding: 10px 12px;
                font-size: 0.9rem;
            }

            .sidebar #userSubmenu .nav-link {
                padding-left: 2rem;
                font-size: 0.85rem;
            }
        }

        @media (max-width: 767.98px) {
            .sidebar {
                width: 200px;
            }

            main[style*="margin-left: 250px"] {
                margin-left: 200px !important;
            }

            .sidebar .nav-link {
                padding: 8px 10px;
                font-size: 0.85rem;
            }

            .sidebar #userSubmenu .nav-link {
                padding-left: 1.8rem;
                font-size: 0.8rem;
            }

            .sidebar .text-center h5 {
                font-size: 1rem;
            }

            .sidebar .img-fluid {
                max-width: 80px;
            }
        }
    </style>
</head>
<body>
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <nav class="col-md-3 col-lg-2 sidebar">
            <div class="text-center mb-4">
                <img src="images/logo.png" alt="ENSAH" class="img-fluid mb-3" style="filter: drop-shadow(0 0 5px var(--primary-blue));">
                <h5 class="text-white" style="text-shadow: 0 0 10px var(--primary-blue);">Administration ENSAH</h5>
            </div>
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" href="admin_dashboard.php">
                        <i class="fas fa-chart-line me-2"></i>Tableau de bord
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link dropdown-toggle active" href="#userSubmenu" data-bs-toggle="collapse" aria-expanded="true">
                        <i class="fas fa-users-cog me-2"></i>
                        Gestion Utilisateurs
                    </a>
                    <ul class="collapse show" id="userSubmenu">
                        <li class="nav-item">
                            <a class="nav-link ms-3" href="gestion_chef_departement.php">
                                <i class="fas fa-user-tie me-2"></i> Chefs de département
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link ms-3" href="gestion_coordinateur.php">
                                <i class="fas fa-user-cog me-2"></i> Coordinateurs
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link ms-3 active" href="gestion_enseignant.php" style="
                                background: rgba(30, 144, 255, 0.2);
                                border-left: 4px solid var(--primary-blue);
                                transform: translateX(8px);
                            ">
                                <i class="fas fa-chalkboard-teacher me-2"></i> Enseignants
                            </a>
                        </li>
                    </ul>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="gestion_modules.php">
                        <i class="fas fa-book-open me-2"></i> Modules
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="affectation_ue.php">
                        <i class="fas fa-tasks me-2"></i> Affectations
                    </a>
                </li>
                <li class="nav-item mt-4">
                    <a class="nav-link text-danger" href="logout.php">
                        <i class="fas fa-sign-out-alt me-2"></i>Déconnexion
                    </a>
                </li>
            </ul>
        </nav>

        <!-- Main Content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 p-4" style="margin-left: 250px;">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="ms-4">Gestion des Enseignants</h1>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#ajouterModal">
                    <i class="fas fa-plus-circle me-2"></i>Ajouter un enseignant
                </button>
            </div>

            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= htmlspecialchars($error) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= htmlspecialchars($success) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h5 class="m-0 text-white ms-3"><i class="fas fa-users me-2"></i>Liste des enseignants</h5>
                </div>
                <div class="card-body">

                    <div>
                            <div class="table-responsive">
                                <table class="table table-hover" id="enseignantsTable">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Nom</th>
                                            <th>Prénom</th>
                                            <th>Email</th>

                                            <th>Département</th>
                                            <th>Spécialité</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($enseignants as $ens): ?>
                                        <tr>
                                            <td><?= htmlspecialchars($ens['id']) ?></td>
                                            <td><?= htmlspecialchars($ens['nom']) ?></td>
                                            <td><?= htmlspecialchars($ens['prenom']) ?></td>
                                            <td><?= htmlspecialchars($ens['email']) ?></td>

                                            <td><?= htmlspecialchars($ens['nom_departement'] ?? 'N/A') ?></td>
                                            <td><?= htmlspecialchars($ens['nom_specialite'] ?? 'N/A') ?></td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary me-1 btn-modifier"
                                                        data-id="<?= $ens['id'] ?>"
                                                        data-nom="<?= htmlspecialchars($ens['nom']) ?>"
                                                        data-prenom="<?= htmlspecialchars($ens['prenom']) ?>"
                                                        data-email="<?= htmlspecialchars($ens['email']) ?>"
                                                        data-type="<?= $ens['type_enseignant'] === 'Permanent' ? 'permanent' : 'vacataire' ?>"
                                                        data-departement="<?= $ens['departement_id'] ?? '' ?>"
                                                        data-specialite="<?= $ens['specialite_id'] ?? '' ?>">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-sm btn-outline-danger btn-supprimer"
                                                        data-id="<?= $ens['id'] ?>"
                                                        data-nom="<?= htmlspecialchars($ens['prenom'] . ' ' . $ens['nom']) ?>">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- Modal Ajouter -->
<div class="modal fade" id="ajouterModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Ajouter un enseignant</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" class="needs-validation" novalidate>
                <div class="modal-body">
                    <input type="hidden" name="action" value="ajouter">

                    <div class="mb-3">
                        <label for="nom" class="form-label">Nom</label>
                        <input type="text" class="form-control" id="nom" name="nom" required>
                        <div class="invalid-feedback">Veuillez entrer le nom</div>
                    </div>

                    <div class="mb-3">
                        <label for="prenom" class="form-label">Prénom</label>
                        <input type="text" class="form-control" id="prenom" name="prenom" required>
                        <div class="invalid-feedback">Veuillez entrer le prénom</div>
                    </div>

                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                        <div class="invalid-feedback">Veuillez entrer un email valide</div>
                    </div>

                    <input type="hidden" name="type" value="permanent">

                    <div class="mb-3">
                        <label for="departement_id" class="form-label">Département</label>
                        <select class="form-select" id="departement_id" name="departement_id" required onchange="chargerSpecialites(this.value)">
                            <option value="" selected disabled>Sélectionnez un département</option>
                            <?php foreach ($departements as $departement): ?>
                                <option value="<?= $departement['id_departement'] ?>"><?= htmlspecialchars($departement['nom_departement']) ?></option>
                            <?php endforeach; ?>
                        </select>
                        <div class="invalid-feedback">Veuillez sélectionner un département</div>
                    </div>

                    <div class="mb-3">
                        <label for="specialite_id" class="form-label">Spécialité</label>
                        <select class="form-select" id="specialite_id" name="specialite_id">
                            <option value="" selected disabled>Sélectionnez d'abord un département</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="password" class="form-label">Mot de passe</label>
                        <input type="password" class="form-control" id="password" name="password" required minlength="8">
                        <div class="invalid-feedback">Le mot de passe doit contenir au moins 8 caractères</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">Ajouter</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Modifier -->
<div class="modal fade" id="modifierModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Modifier l'enseignant</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" class="needs-validation" novalidate>
                <div class="modal-body">
                    <input type="hidden" name="action" value="modifier">
                    <input type="hidden" name="id" id="modifier_id">

                    <div class="mb-3">
                        <label for="modifier_nom" class="form-label">Nom</label>
                        <input type="text" class="form-control" id="modifier_nom" name="nom" required>
                        <div class="invalid-feedback">Veuillez entrer le nom</div>
                    </div>

                    <div class="mb-3">
                        <label for="modifier_prenom" class="form-label">Prénom</label>
                        <input type="text" class="form-control" id="modifier_prenom" name="prenom" required>
                        <div class="invalid-feedback">Veuillez entrer le prénom</div>
                    </div>

                    <div class="mb-3">
                        <label for="modifier_email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="modifier_email" name="email" required>
                        <div class="invalid-feedback">Veuillez entrer un email valide</div>
                    </div>

                    <input type="hidden" name="type" id="modifier_type" value="permanent">

                    <div class="mb-3">
                        <label for="modifier_departement_id" class="form-label">Département</label>
                        <select class="form-select" id="modifier_departement_id" name="departement_id" required onchange="chargerSpecialitesModifier(this.value)">
                            <?php foreach ($departements as $departement): ?>
                                <option value="<?= $departement['id_departement'] ?>"><?= htmlspecialchars($departement['nom_departement']) ?></option>
                            <?php endforeach; ?>
                        </select>
                        <div class="invalid-feedback">Veuillez sélectionner un département</div>
                    </div>

                    <div class="mb-3">
                        <label for="modifier_specialite_id" class="form-label">Spécialité</label>
                        <select class="form-select" id="modifier_specialite_id" name="specialite_id">
                            <option value="" selected disabled>Sélectionnez d'abord un département</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="modifier_password" class="form-label">Mot de passe (laisser vide pour ne pas changer)</label>
                        <input type="password" class="form-control" id="modifier_password" name="password" minlength="8">
                        <div class="invalid-feedback">Le mot de passe doit contenir au moins 8 caractères</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">Enregistrer</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Supprimer -->
<div class="modal fade" id="supprimerModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmer la suppression</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Êtes-vous sûr de vouloir supprimer l'enseignant <span id="supprimer_nom"></span> ?</p>
                <p class="text-danger">Cette action est irréversible.</p>
            </div>
            <form method="post">
                <input type="hidden" name="action" value="supprimer">
                <input type="hidden" name="user_id" id="supprimer_id">
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-danger">Supprimer</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.4.1/js/dataTables.responsive.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.4.1/js/responsive.bootstrap5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.bootstrap5.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.print.min.js"></script>
<script>
    // Fonction pour charger les spécialités en fonction du département sélectionné
    function chargerSpecialites(departementId) {
        if (!departementId) {
            $('#specialite_id').html('<option value="" selected disabled>Sélectionnez d\'abord un département</option>');
            return;
        }

        $.ajax({
            url: 'get_specialites.php',
            type: 'GET',
            data: { departement_id: departementId },
            dataType: 'json',
            success: function(response) {
                if (response.success && response.specialites.length > 0) {
                    let options = '<option value="" selected disabled>Sélectionnez une spécialité</option>';
                    response.specialites.forEach(function(specialite) {
                        options += `<option value="${specialite.id_specialite}">${specialite.nom_specialite}</option>`;
                    });
                    $('#specialite_id').html(options);
                } else {
                    $('#specialite_id').html('<option value="" selected disabled>Aucune spécialité disponible</option>');
                }
            },
            error: function() {
                $('#specialite_id').html('<option value="" selected disabled>Erreur lors du chargement des spécialités</option>');
            }
        });
    }

    // Fonction pour charger les spécialités dans le formulaire de modification
    function chargerSpecialitesModifier(departementId) {
        if (!departementId) {
            $('#modifier_specialite_id').html('<option value="" selected disabled>Sélectionnez d\'abord un département</option>');
            return;
        }

        $.ajax({
            url: 'get_specialites.php',
            type: 'GET',
            data: { departement_id: departementId },
            dataType: 'json',
            success: function(response) {
                if (response.success && response.specialites.length > 0) {
                    let options = '<option value="" selected disabled>Sélectionnez une spécialité</option>';
                    response.specialites.forEach(function(specialite) {
                        options += `<option value="${specialite.id_specialite}">${specialite.nom_specialite}</option>`;
                    });
                    $('#modifier_specialite_id').html(options);
                } else {
                    $('#modifier_specialite_id').html('<option value="" selected disabled>Aucune spécialité disponible</option>');
                }
            },
            error: function() {
                $('#modifier_specialite_id').html('<option value="" selected disabled>Erreur lors du chargement des spécialités</option>');
            }
        });
    }

    $(document).ready(function() {
        // Initialisation de DataTables
        $('#enseignantsTable').DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/fr-FR.json'
            },
            dom: '<"row"<"col-sm-12 col-md-6"B><"col-sm-12 col-md-6"f>>rt<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
            responsive: true,
            autoWidth: false,
            pageLength: 10,
            lengthMenu: [[10, 25, 50, -1], [10, 25, 50, "Tous"]],
            columnDefs: [
                { orderable: false, targets: [6] },
                { className: "text-center", targets: [6] }
            ],
            buttons: [
                {
                    extend: 'excel',
                    text: '<i class="fas fa-file-excel"></i> Excel',
                    className: 'btn btn-sm btn-outline-primary me-1',
                    exportOptions: {
                        columns: [0, 1, 2, 3, 4, 5]
                    }
                },
                {
                    extend: 'pdf',
                    text: '<i class="fas fa-file-pdf"></i> PDF',
                    className: 'btn btn-sm btn-outline-primary me-1',
                    exportOptions: {
                        columns: [0, 1, 2, 3, 4, 5]
                    }
                },
                {
                    extend: 'print',
                    text: '<i class="fas fa-print"></i> Imprimer',
                    className: 'btn btn-sm btn-outline-primary',
                    exportOptions: {
                        columns: [0, 1, 2, 3, 4, 5]
                    }
                }
            ]
        });

        // Réinitialiser le formulaire d'ajout quand le modal est ouvert
        $('#ajouterModal').on('show.bs.modal', function() {
            $('#ajouterModal form')[0].reset();
            $('#specialite_id').html('<option value="" selected disabled>Sélectionnez d\'abord un département</option>');
        });

        // Gérer le clic sur le bouton modifier
        $('.btn-modifier').click(function() {
            const id = $(this).data('id');
            const nom = $(this).data('nom');
            const prenom = $(this).data('prenom');
            const email = $(this).data('email');
            const type = $(this).data('type');
            const departement = $(this).data('departement');
            const specialite = $(this).data('specialite');

            $('#modifier_id').val(id);
            $('#modifier_nom').val(nom);
            $('#modifier_prenom').val(prenom);
            $('#modifier_email').val(email);
            $('#modifier_type').val(type);
            $('#modifier_departement_id').val(departement);

            // Charger les spécialités pour ce département
            chargerSpecialitesModifier(departement);

            // Sélectionner la spécialité après le chargement des options
            setTimeout(function() {
                $('#modifier_specialite_id').val(specialite);
            }, 500);

            $('#modifierModal').modal('show');
        });

        // Gérer le clic sur le bouton supprimer
        $('.btn-supprimer').click(function() {
            const id = $(this).data('id');
            const nom = $(this).data('nom');

            $('#supprimer_id').val(id);
            $('#supprimer_nom').text(nom);

            $('#supprimerModal').modal('show');
        });

        // Validation côté client
        const forms = document.querySelectorAll('.needs-validation');
        Array.from(forms).forEach(form => {
            form.addEventListener('submit', event => {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }

                // Aucune vérification de correspondance de mot de passe nécessaire

                form.classList.add('was-validated');
            }, false);
        });
    });
</script>
</body>
</html>