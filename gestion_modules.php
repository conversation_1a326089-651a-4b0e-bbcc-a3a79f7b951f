<?php
// Inclure le fichier de configuration
require_once 'config.php';

// Gestion de la session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Vérification de l'authentification
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php?error=session_invalide");
    exit();
}

// Pour le débogage
error_log("Session user_id: " . $_SESSION['user_id']);
error_log("Session role: " . ($_SESSION['role'] ?? 'non défini'));
error_log("Session user_type: " . ($_SESSION['user_type'] ?? 'non défini'));

// Forcer le type d'utilisateur à chef_departement pour cette page
$_SESSION['user_type'] = 'chef_departement';
$_SESSION['role'] = 'chef_departement';

try {
    // Connexion à la base de données
    $pdo = new PDO(
        "mysql:host=".DB_HOST.";dbname=".DB_NAME.";charset=utf8",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
} catch (PDOException $e) {
    die("Erreur de connexion : " . $e->getMessage());
}

// Récupérer toutes les unités d'enseignement
function getUnitesEnseignement() {
    global $pdo;

    try {
        // Vérifier si la table departements existe
        $stmt = $pdo->query("SHOW TABLES LIKE 'departements'");
        $depTableExists = $stmt->rowCount() > 0;

        // Vérifier si la colonne departement_id existe dans unites_enseignements
        $stmt = $pdo->query("SHOW COLUMNS FROM unites_enseignements LIKE 'departement_id'");
        $depIdExists = $stmt->rowCount() > 0;

        // Vérifier si la colonne code_ue existe dans unites_enseignements
        $stmt = $pdo->query("SHOW COLUMNS FROM unites_enseignements LIKE 'code_ue'");
        $codeUeExists = $stmt->rowCount() > 0;

        if ($depTableExists && $depIdExists) {
            // Si la table departements existe et la colonne departement_id existe
            $query = "
                SELECT ue.*, d.nom as nom_departement
                FROM unites_enseignements ue
                LEFT JOIN departements d ON ue.departement_id = d.departement_id
                ORDER BY " . ($codeUeExists ? "ue.code_ue" : "ue.id_ue");
        } else {
            // Si la table departements n'existe pas ou la colonne departement_id n'existe pas
            $query = "
                SELECT
                    ue.*,
                    'Non spécifié' as nom_departement
                FROM
                    unites_enseignements ue
                ORDER BY " . ($codeUeExists ? "ue.code_ue" : "ue.id_ue");
        }

        $stmt = $pdo->query($query);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        // En cas d'erreur, retourner un tableau vide
        error_log("Erreur dans getUnitesEnseignement: " . $e->getMessage());
        return [];
    }
}

$unites_enseignement = getUnitesEnseignement();
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Liste des Unités d'Enseignement</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/buttons/2.4.1/css/buttons.bootstrap5.min.css" rel="stylesheet">
    <style>
        :root {
            --royal-blue: #4169E1;     /* Bleu royal */
            --dark-blue: #0038a8;      /* Bleu royal foncé */
            --accent-gold: #FFD700;    /* Or pour accents */
            --light-bg: #f0f5ff;       /* Fond bleu très clair */
            --text-dark: #0d1b3e;      /* Texte bleu foncé */
            --text-light: #ffffff;     /* Texte blanc */
        }

        /* Animation du fond d'écran */
        @keyframes animatedBackground {
            0% { background-position: 0 0; }
            50% { background-position: 50% 50%; }
            100% { background-position: 0 0; }
        }

        body {
            background-color: var(--light-bg);
            background-image: url('images/background-blue.jpg');
            background-size: cover;
            background-attachment: fixed;
            animation: animatedBackground 30s linear infinite;
            color: var(--text-dark);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }

        /* Sidebar bleu royal */
        .sidebar {
            background: linear-gradient(to bottom, var(--royal-blue), var(--dark-blue));
            color: var(--text-light);
            height: 100vh;
            position: fixed;
            width: 280px;
            box-shadow: 5px 0 25px rgba(0,0,0,0.3);
            z-index: 1000;
        }

        /* Zone du logo - taille augmentée */
        .logo-container {
            padding: 30px 20px 20px;
            text-align: center;
            background: rgba(0,0,0,0.1);
            border-bottom: 1px solid rgba(255,255,255,0.1);
            margin-bottom: 10px;
        }

        /* Logo plus grand */
        .logo {
            height: 90px;               /* Taille augmentée */
            width: auto;
            max-width: 100%;
            margin-bottom: 15px;
            transition: transform 0.3s ease;
            object-fit: contain;
        }

        .logo-container:hover .logo {
            transform: scale(1.05);
        }

        .sidebar-title {
            color: white;
            font-size: 1.3rem;
            font-weight: 500;
            margin-top: 5px;
            text-shadow: 0 1px 3px rgba(0,0,0,0.3);
        }

        /* Navigation */
        .sidebar .nav-link {
            color: rgba(255,255,255,0.9);
            padding: 14px 25px;
            margin: 6px 15px;
            border-radius: 6px;
            transition: all 0.3s;
            font-size: 0.95rem;
        }

        .sidebar .nav-link:hover {
            background-color: rgba(255,255,255,0.15);
            transform: translateX(8px);
        }

        .sidebar .nav-link.active {
            background-color: rgba(255,255,255,0.2);
            border-left: 4px solid var(--accent-gold);
            font-weight: 500;
        }

        .sidebar .nav-link i {
            width: 25px;
            text-align: center;
            margin-right: 12px;
            font-size: 1.1rem;
        }

        /* Contenu principal */
        .main-content {
            margin-left: 280px;
            padding: 40px;
            width: calc(100% - 280px);
            background-color: rgba(255,255,255,0.92);
            min-height: 100vh;
            backdrop-filter: blur(8px);
        }

        /* Tableau */
        .table-container {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: transform 0.3s ease;
        }

        .table-container:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
        }

        .table-modules {
            margin-bottom: 0;
            width: 100%;
        }

        .table-modules thead {
            background: linear-gradient(135deg, var(--royal-blue), var(--dark-blue));
            color: white;
        }

        .table-modules th {
            font-weight: 500;
            padding: 16px 25px;
            border: none;
            font-size: 0.95rem;
        }

        .table-modules td {
            padding: 14px 25px;
            border-top: 1px solid rgba(0,0,0,0.05);
            vertical-align: middle;
        }

        .table-modules tbody tr:hover {
            background-color: rgba(65,105,225,0.08);
        }

        /* Badges */
        .badge-semestre {
            background-color: #2e8b57;
            color: white;
            padding: 6px 14px;
            border-radius: 20px;
            font-weight: 500;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            font-size: 0.85rem;
        }

        /* Titre */
        h1 {
            color: var(--dark-blue);
            margin-bottom: 30px;
            font-weight: 700;
            position: relative;
            padding-bottom: 15px;
        }

        h1:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 70px;
            height: 4px;
            background: linear-gradient(90deg, var(--accent-gold), var(--royal-blue));
            border-radius: 2px;
        }

        /* Message vide */
        .no-data {
            padding: 50px;
            text-align: center;
            background-color: rgba(255,255,255,0.7);
            border-radius: 10px;
            margin: 20px 0;
        }

        .no-data i {
            color: var(--royal-blue);
            font-size: 60px;
            margin-bottom: 20px;
            opacity: 0.8;
        }

        /* Styles pour les boutons d'export */
        .export-buttons {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
        }

        .export-btn {
            padding: 10px 20px;
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .export-btn i {
            font-size: 1.1rem;
        }

        .export-btn-excel {
            background-color: #1D6F42;
            color: white;
            border: none;
        }

        .export-btn-excel:hover {
            background-color: #155a35;
            transform: translateY(-2px);
            box-shadow: 0 6px 8px rgba(0,0,0,0.15);
        }

        .export-btn-print {
            background-color: var(--royal-blue);
            color: white;
            border: none;
        }

        .export-btn-print:hover {
            background-color: var(--dark-blue);
            transform: translateY(-2px);
            box-shadow: 0 6px 8px rgba(0,0,0,0.15);
        }

        /* Style pour les boutons DataTables */
        .dt-buttons {
            margin-bottom: 15px;
        }

        .dt-button {
            background: var(--royal-blue) !important;
            color: white !important;
            border: none !important;
            border-radius: 4px !important;
            padding: 8px 16px !important;
            margin-right: 8px !important;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1) !important;
            transition: all 0.3s ease !important;
        }

        .dt-button:hover {
            background: var(--dark-blue) !important;
            box-shadow: 0 4px 8px rgba(0,0,0,0.15) !important;
            transform: translateY(-2px) !important;
        }

        /* Responsive */
        @media (max-width: 992px) {
            .sidebar {
                width: 250px;
            }
            .main-content {
                margin-left: 250px;
                width: calc(100% - 250px);
                padding: 30px;
            }
            .logo {
                height: 80px;
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }
            .main-content {
                margin-left: 0;
                width: 100%;
                padding: 20px;
            }
            .logo-container {
                padding: 20px;
            }
            .logo {
                height: 70px;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar avec logo agrandi -->
    <nav class="sidebar">
        <div class="logo-container">
            <img src="images/logo.png" alt="Logo de l'établissement" class="logo">
            <div class="sidebar-title">Gestion Académique</div>
        </div>

        <ul class="nav flex-column px-3">
            <li class="nav-item">
                <a class="nav-link" href="chef_dashboard.php">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="gestion_modules.php">
                    <i class="fas fa-book-open"></i> Modules
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="gestion_professeurs.php">
                    <i class="fas fa-chalkboard-teacher"></i> Professeurs
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="affectation_ue.php">
                    <i class="fas fa-tasks"></i> Affectations
                </a>
            </li>
            <li class="nav-item mt-4">
                <a class="nav-link" href="logout.php" style="color: var(--accent-gold);">
                    <i class="fas fa-sign-out-alt"></i> Déconnexion
                </a>
            </li>
        </ul>
    </nav>

    <!-- Contenu principal -->
    <div class="main-content">
        <h1>
            <i class="fas fa-book-open me-3"></i>
            Liste des Unités d'Enseignement
        </h1>

        <div class="export-buttons">
            <button id="export-excel" class="btn export-btn export-btn-excel">
                <i class="fas fa-file-excel"></i> Exporter vers Excel
            </button>
            <button id="export-print" class="btn export-btn export-btn-print">
                <i class="fas fa-print"></i> Imprimer
            </button>
        </div>

        <div class="table-container">
            <?php if (count($unites_enseignement) > 0): ?>
                <table class="table table-modules">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Code UE</th>
                            <th>Intitulé</th>
                            <th>Crédits</th>
                            <th>Volume Horaire</th>
                            <th>Semestre</th>
                            <th>Département</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($unites_enseignement as $ue): ?>
                            <tr>
                                <td><?= htmlspecialchars($ue['id_ue'] ?? $ue['id'] ?? '0') ?></td>
                                <td><?= htmlspecialchars($ue['code_ue'] ?? 'UE' . ($ue['id_ue'] ?? $ue['id'] ?? '0')) ?></td>
                                <td><?= htmlspecialchars($ue['intitule'] ?? $ue['nom'] ?? $ue['filiere'] ?? 'Non spécifié') ?></td>
                                <td><?= htmlspecialchars($ue['credit'] ?? $ue['credits'] ?? '0') ?></td>
                                <td><?= htmlspecialchars($ue['volume_horaire'] ?? '0') ?> h</td>
                                <td>
                                    <span class="badge badge-semestre">
                                        <?= htmlspecialchars($ue['semestre'] ?? '1') ?>
                                    </span>
                                </td>
                                <td><?= htmlspecialchars($ue['nom_departement'] ?? 'Non spécifié') ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php else: ?>
                <div class="no-data">
                    <i class="fas fa-book-open"></i>
                    <h3>Aucune unité d'enseignement trouvée</h3>
                    <p class="text-muted">La liste des unités d'enseignement est vide</p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>

    <!-- Scripts pour l'export -->
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.bootstrap5.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.html5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.print.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.colVis.min.js"></script>
    <script>
        $(document).ready(function() {
            // Initialisation de DataTables avec les boutons d'export
            var table = $('.table-modules').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/fr-FR.json'
                },
                responsive: true,
                dom: '<"top"f>rt<"bottom"ip><"clear">',
                pageLength: 10,
                buttons: [
                    {
                        extend: 'excel',
                        text: '<i class="fas fa-file-excel"></i> Excel',
                        title: 'Liste des Unités d\'Enseignement',
                        exportOptions: {
                            columns: ':visible'
                        },
                        className: 'btn-excel'
                    },
                    {
                        extend: 'print',
                        text: '<i class="fas fa-print"></i> Imprimer',
                        title: 'Liste des Unités d\'Enseignement',
                        exportOptions: {
                            columns: ':visible'
                        },
                        className: 'btn-print'
                    }
                ],
                initComplete: function() {
                    $('.dataTables_filter input').addClass('form-control');
                    $('.dataTables_length select').addClass('form-select');
                }
            });

            // Lier les boutons personnalisés aux fonctions d'export
            $('#export-excel').on('click', function() {
                table.button('.buttons-excel').trigger();
            });

            $('#export-print').on('click', function() {
                table.button('.buttons-print').trigger();
            });
        });
    </script>
</body>
</html>each