<?php
// Activer l'affichage des erreurs pour le débogage
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Connexion à la base de données
$host = '127.0.0.1';
$dbname = 'gestion_coordinteur'; // Vérifiez si ce nom est correct
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Vérifier la connexion
    echo "<!-- Connexion à la base de données réussie -->";

    // Lister toutes les tables dans la base de données
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "<!-- Tables dans la base de données: " . implode(", ", $tables) . " -->";

    // Vérifier si la table utilisateurs existe
    if (in_array('utilisateurs', $tables)) {
        echo "<!-- La table 'utilisateurs' existe dans la base de données -->";
    } else {
        echo "<!-- ATTENTION: La table 'utilisateurs' n'existe PAS dans la base de données -->";
    }
} catch (PDOException $e) {
    die("Erreur de connexion : " . $e->getMessage());
}

// Récupérer tous les enseignants depuis la table utilisateurs
function getEnseignants() {
    global $pdo;

    try {
        // Vérifier si la table utilisateurs existe
        $stmt = $pdo->query("SHOW TABLES LIKE 'utilisateurs'");
        $tableExists = $stmt->rowCount() > 0;

        if ($tableExists) {
            echo "<!-- Table utilisateurs existe -->";

            // Vérifier si la colonne type_utilisateur existe
            $stmt = $pdo->query("SHOW COLUMNS FROM utilisateurs LIKE 'type_utilisateur'");
            $typeColExists = $stmt->rowCount() > 0;

            if ($typeColExists) {
                echo "<!-- Colonne type_utilisateur existe -->";

                // Récupérer tous les types d'utilisateurs disponibles pour le débogage
                $stmt = $pdo->query("SELECT DISTINCT type_utilisateur FROM utilisateurs");
                $types = $stmt->fetchAll(PDO::FETCH_COLUMN);
                echo "<!-- Types d'utilisateurs disponibles: " . implode(", ", $types) . " -->";

                // Compter le nombre d'enseignants
                $stmt = $pdo->query("SELECT COUNT(*) FROM utilisateurs WHERE type_utilisateur = 'enseignant'");
                $count = $stmt->fetchColumn();
                echo "<!-- Nombre d'enseignants trouvés: " . $count . " -->";

                // Récupérer uniquement les utilisateurs de type "enseignant"
                $query = "
                    SELECT
                        *
                    FROM
                        utilisateurs
                    WHERE
                        type_utilisateur = 'enseignant'
                    ORDER BY
                        nom, prenom
                ";

                // Afficher également tous les utilisateurs pour le débogage
                echo "<!-- Tous les utilisateurs dans la table: -->";
                $debug_stmt = $pdo->query("SELECT * FROM utilisateurs LIMIT 5");
                $debug_users = $debug_stmt->fetchAll(PDO::FETCH_ASSOC);
                foreach ($debug_users as $user) {
                    echo "<!-- Utilisateur ID: " . $user['id'] . " -->";
                    foreach ($user as $key => $value) {
                        echo "<!-- " . $key . ": " . $value . " -->";
                    }
                }

                // Afficher la structure de la table pour le débogage
                echo "<!-- Structure de la table utilisateurs: -->";
                $stmt = $pdo->query("DESCRIBE utilisateurs");
                $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
                foreach ($columns as $column) {
                    echo "<!-- Colonne: " . $column['Field'] . " - Type: " . $column['Type'] . " -->";
                }
                echo "<!-- Requête SQL: " . htmlspecialchars($query) . " -->";

                $stmt = $pdo->prepare($query);
                $stmt->execute();
                $result = $stmt->fetchAll(PDO::FETCH_ASSOC);

                echo "<!-- Nombre de résultats: " . count($result) . " -->";

                // Afficher les résultats pour le débogage
                foreach ($result as $index => $row) {
                    echo "<!-- Résultat " . $index . ": -->";
                    foreach ($row as $key => $value) {
                        echo "<!-- " . $key . ": " . $value . " -->";
                    }
                }

                // Si aucun résultat n'est trouvé, vérifier s'il y a des utilisateurs dans la table
                if (count($result) == 0) {
                    echo "<!-- Aucun enseignant trouvé, vérification des utilisateurs existants -->";

                    // Vérifier s'il y a des utilisateurs dans la table
                    $stmt = $pdo->query("SELECT COUNT(*) FROM utilisateurs");
                    $totalUsers = $stmt->fetchColumn();
                    echo "<!-- Nombre total d'utilisateurs: " . $totalUsers . " -->";

                    if ($totalUsers > 0) {
                        // Il y a des utilisateurs mais aucun enseignant
                        echo "<!-- Des utilisateurs existent mais aucun n'est de type 'enseignant' -->";
                    } else {
                        // Aucun utilisateur dans la table
                        echo "<!-- Aucun utilisateur dans la table -->";
                    }
                }

                return $result;
            } else {
                echo "<!-- Colonne type_utilisateur n'existe pas -->";
            }
        } else {
            echo "<!-- Table utilisateurs n'existe pas -->";
        }

        // Si la table utilisateurs n'existe pas ou la colonne type_utilisateur n'existe pas,
        // essayer de récupérer les données de la table professeurs
        $stmt = $pdo->query("SHOW TABLES LIKE 'professeurs'");
        $profsTableExists = $stmt->rowCount() > 0;

        if ($profsTableExists) {
            echo "<!-- Table professeurs existe, utilisation comme fallback -->";
            $stmt = $pdo->query("SELECT * FROM professeurs ORDER BY nom, prenom");
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } else {
            echo "<!-- Table professeurs n'existe pas non plus -->";
        }

        // Si aucune table n'existe, retourner un tableau vide
        echo "<!-- Aucune table valide trouvée, retour tableau vide -->";
        return [];
    } catch (PDOException $e) {
        // En cas d'erreur, retourner un tableau vide
        echo "<!-- Erreur dans getEnseignants: " . htmlspecialchars($e->getMessage()) . " -->";
        error_log("Erreur dans getEnseignants: " . $e->getMessage());
        return [];
    }
}

$professeurs = getEnseignants();
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Liste des Professeurs</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/buttons/2.4.1/css/buttons.bootstrap5.min.css" rel="stylesheet">
    <style>
        :root {
            --royal-blue: #4169E1;     /* Bleu royal */
            --dark-blue: #0038a8;      /* Bleu royal foncé */
            --accent-gold: #FFD700;    /* Or pour accents */
            --light-bg: #f0f5ff;       /* Fond bleu très clair */
            --text-dark: #0d1b3e;      /* Texte bleu foncé */
            --text-light: #ffffff;     /* Texte blanc */
        }

        /* Animation du fond d'écran */
        @keyframes animatedBackground {
            0% { background-position: 0 0; }
            50% { background-position: 50% 50%; }
            100% { background-position: 0 0; }
        }

        body {
            background-color: var(--light-bg);
            background-image: url('images/background-blue.jpg');
            background-size: cover;
            background-attachment: fixed;
            animation: animatedBackground 30s linear infinite;
            color: var(--text-dark);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }

        /* Sidebar bleu royal */
        .sidebar {
            background: linear-gradient(to bottom, var(--royal-blue), var(--dark-blue));
            color: var(--text-light);
            height: 100vh;
            position: fixed;
            width: 280px;
            box-shadow: 5px 0 25px rgba(0,0,0,0.3);
            z-index: 1000;
        }

        /* Zone du logo - taille augmentée */
        .logo-container {
            padding: 30px 20px 20px;
            text-align: center;
            background: rgba(0,0,0,0.1);
            border-bottom: 1px solid rgba(255,255,255,0.1);
            margin-bottom: 10px;
        }

        /* Logo plus grand */
        .logo {
            height: 90px;               /* Taille augmentée */
            width: auto;
            max-width: 100%;
            margin-bottom: 15px;
            transition: transform 0.3s ease;
            object-fit: contain;
        }

        .logo-container:hover .logo {
            transform: scale(1.05);
        }

        .sidebar-title {
            color: white;
            font-size: 1.3rem;
            font-weight: 500;
            margin-top: 5px;
            text-shadow: 0 1px 3px rgba(0,0,0,0.3);
        }

        /* Navigation */
        .sidebar .nav-link {
            color: rgba(255,255,255,0.9);
            padding: 14px 25px;
            margin: 6px 15px;
            border-radius: 6px;
            transition: all 0.3s;
            font-size: 0.95rem;
        }

        .sidebar .nav-link:hover {
            background-color: rgba(255,255,255,0.15);
            transform: translateX(8px);
        }

        .sidebar .nav-link.active {
            background-color: rgba(255,255,255,0.2);
            border-left: 4px solid var(--accent-gold);
            font-weight: 500;
        }

        .sidebar .nav-link i {
            width: 25px;
            text-align: center;
            margin-right: 12px;
            font-size: 1.1rem;
        }

        /* Contenu principal */
        .main-content {
            margin-left: 280px;
            padding: 40px;
            width: calc(100% - 280px);
            background-color: rgba(255,255,255,0.92);
            min-height: 100vh;
            backdrop-filter: blur(8px);
        }

        /* Tableau */
        .table-container {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: transform 0.3s ease;
        }

        .table-container:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
        }

        .table-professeurs {
            margin-bottom: 0;
            width: 100%;
        }

        .table-professeurs thead {
            background: linear-gradient(135deg, var(--royal-blue), var(--dark-blue));
            color: white;
        }

        .table-professeurs th {
            font-weight: 500;
            padding: 16px 25px;
            border: none;
            font-size: 0.95rem;
        }

        .table-professeurs td {
            padding: 14px 25px;
            border-top: 1px solid rgba(0,0,0,0.05);
            vertical-align: middle;
        }

        .table-professeurs tbody tr:hover {
            background-color: rgba(65,105,225,0.08);
        }

        /* Badges */
        .badge-permanent {
            background-color: #2e8b57;
            color: white;
            padding: 6px 14px;
            border-radius: 20px;
            font-weight: 500;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            font-size: 0.85rem;
        }

        .badge-vacataire {
            background-color: #daa520;
            color: white;
            padding: 6px 14px;
            border-radius: 20px;
            font-weight: 500;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            font-size: 0.85rem;
        }

        /* Titre */
        h1 {
            color: var(--dark-blue);
            margin-bottom: 30px;
            font-weight: 700;
            position: relative;
            padding-bottom: 15px;
        }

        h1:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 70px;
            height: 4px;
            background: linear-gradient(90deg, var(--accent-gold), var(--royal-blue));
            border-radius: 2px;
        }

        /* Message vide */
        .no-data {
            padding: 50px;
            text-align: center;
            background-color: rgba(255,255,255,0.7);
            border-radius: 10px;
            margin: 20px 0;
        }

        .no-data i {
            color: var(--royal-blue);
            font-size: 60px;
            margin-bottom: 20px;
            opacity: 0.8;
        }

        /* Styles pour les boutons d'export */
        .export-buttons {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
        }

        .export-btn {
            padding: 10px 20px;
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .export-btn i {
            font-size: 1.1rem;
        }

        .export-btn-excel {
            background-color: #1D6F42;
            color: white;
            border: none;
        }

        .export-btn-excel:hover {
            background-color: #155a35;
            transform: translateY(-2px);
            box-shadow: 0 6px 8px rgba(0,0,0,0.15);
        }

        .export-btn-print {
            background-color: var(--royal-blue);
            color: white;
            border: none;
        }

        .export-btn-print:hover {
            background-color: var(--dark-blue);
            transform: translateY(-2px);
            box-shadow: 0 6px 8px rgba(0,0,0,0.15);
        }

        /* Style pour les boutons DataTables */
        .dt-buttons {
            margin-bottom: 15px;
        }

        .dt-button {
            background: var(--royal-blue) !important;
            color: white !important;
            border: none !important;
            border-radius: 4px !important;
            padding: 8px 16px !important;
            margin-right: 8px !important;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1) !important;
            transition: all 0.3s ease !important;
        }

        .dt-button:hover {
            background: var(--dark-blue) !important;
            box-shadow: 0 4px 8px rgba(0,0,0,0.15) !important;
            transform: translateY(-2px) !important;
        }

        /* Responsive */
        @media (max-width: 992px) {
            .sidebar {
                width: 250px;
            }
            .main-content {
                margin-left: 250px;
                width: calc(100% - 250px);
                padding: 30px;
            }
            .logo {
                height: 80px;
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }
            .main-content {
                margin-left: 0;
                width: 100%;
                padding: 20px;
            }
            .logo-container {
                padding: 20px;
            }
            .logo {
                height: 70px;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar avec logo agrandi -->
    <nav class="sidebar">
        <div class="logo-container">
            <img src="images/logo.png" alt="Logo de l'établissement" class="logo">
            <div class="sidebar-title">Gestion Académique</div>
        </div>

        <ul class="nav flex-column px-3">
            <li class="nav-item">
                <a class="nav-link" href="chef_dashboard.php">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="gestion_modules.php">
                    <i class="fas fa-book-open"></i> Modules
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="gestion_professeurs.php">
                    <i class="fas fa-chalkboard-teacher"></i> Professeurs
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="affectation_ue.php">
                    <i class="fas fa-tasks"></i> Affectations
                </a>
            </li>
            <li class="nav-item mt-4">
                <a class="nav-link" href="logout.php" style="color: var(--accent-gold);">
                    <i class="fas fa-sign-out-alt"></i> Déconnexion
                </a>
            </li>
        </ul>
    </nav>

    <!-- Contenu principal -->
    <div class="main-content">
        <h1>
            <i class="fas fa-chalkboard-teacher me-3"></i>
            Liste des Enseignants
        </h1>

        <div class="export-buttons">
            <button id="export-excel" class="btn export-btn export-btn-excel">
                <i class="fas fa-file-excel"></i> Exporter vers Excel
            </button>
            <button id="export-print" class="btn export-btn export-btn-print">
                <i class="fas fa-print"></i> Imprimer
            </button>
        </div>

        <div class="table-container">
            <?php if (count($professeurs) > 0): ?>
                <table class="table table-professeurs">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Nom</th>
                            <th>Prénom</th>
                            <th>Département</th>
                            <th>Type</th>
                            <th>Email</th>
                            <th>Date création</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($professeurs as $prof): ?>
                            <tr>
                                <td><?= htmlspecialchars($prof['id'] ?? 'N/A') ?></td>
                                <td><?= htmlspecialchars($prof['nom'] ?? $prof['name'] ?? 'Non spécifié') ?></td>
                                <td><?= htmlspecialchars($prof['prenom'] ?? $prof['firstname'] ?? 'Non spécifié') ?></td>
                                <td><?= htmlspecialchars($prof['id_departement'] ?? $prof['departement_id'] ?? 'Non spécifié') ?></td>
                                <td>
                                    <span class="badge badge-permanent">
                                        Enseignant
                                    </span>
                                </td>
                                <td><?= htmlspecialchars($prof['email'] ?? 'Non spécifié') ?></td>
                                <td><?= isset($prof['date_creation']) ? date('d/m/Y', strtotime($prof['date_creation'])) : (isset($prof['created_at']) ? date('d/m/Y', strtotime($prof['created_at'])) : 'Non spécifié') ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php else: ?>
                <div class="no-data">
                    <i class="fas fa-user-slash"></i>
                    <h3>Aucun enseignant trouvé</h3>
                    <p class="text-muted">La liste des enseignants est vide</p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>

    <!-- Scripts pour l'export -->
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.bootstrap5.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.html5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.print.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.colVis.min.js"></script>
    <script>
        $(document).ready(function() {
            // Initialisation de DataTables avec les boutons d'export
            var table = $('.table-professeurs').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/fr-FR.json'
                },
                responsive: true,
                dom: '<"top"f>rt<"bottom"ip><"clear">',
                pageLength: 10,
                buttons: [
                    {
                        extend: 'excel',
                        text: '<i class="fas fa-file-excel"></i> Excel',
                        title: 'Liste des Enseignants',
                        exportOptions: {
                            columns: ':visible'
                        },
                        className: 'btn-excel'
                    },
                    {
                        extend: 'print',
                        text: '<i class="fas fa-print"></i> Imprimer',
                        title: 'Liste des Enseignants',
                        exportOptions: {
                            columns: ':visible'
                        },
                        className: 'btn-print'
                    }
                ],
                initComplete: function() {
                    $('.dataTables_filter input').addClass('form-control');
                    $('.dataTables_length select').addClass('form-select');
                }
            });

            // Lier les boutons personnalisés aux fonctions d'export
            $('#export-excel').on('click', function() {
                table.button('.buttons-excel').trigger();
            });

            $('#export-print').on('click', function() {
                table.button('.buttons-print').trigger();
            });
        });
    </script>
</body>
</html>