<?php
session_start();

// Vérifier si l'utilisateur est connecté et est un enseignant
if (!isset($_SESSION['user_id']) || ($_SESSION['user_type'] !== 'enseignant' && !isset($_SESSION['id_enseignant']))) {
    // Rediriger vers la page de connexion si non connecté ou pas un enseignant
    header('Location: login_enseignant.php');
    exit();
}

if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: login_coordinateur.php');
    exit();
}

try {
    $pdo = new PDO("mysql:host=localhost;dbname=gestion_coordinteur;charset=utf8", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Erreur de connexion : " . $e->getMessage());
}

// Récupérer l'ID de l'utilisateur
$user_id = $_SESSION['user_id'];
$user_type = $_SESSION['user_type'] ?? '';
$info = null;

// Si c'est un enseignant de la table enseignants
if (isset($_SESSION['id_enseignant'])) {
    $id_enseignant = $_SESSION['id_enseignant'];
    $enseignant = $pdo->prepare("SELECT * FROM enseignants WHERE id_enseignant = ?");
    $enseignant->execute([$id_enseignant]);
    $info = $enseignant->fetch();
}
// Si c'est un utilisateur de type enseignant de la table utilisateurs
elseif ($user_type === 'enseignant') {
    $enseignant = $pdo->prepare("SELECT * FROM utilisateurs WHERE id = ? AND type_utilisateur = 'enseignant'");
    $enseignant->execute([$user_id]);
    $user_info = $enseignant->fetch();

    if ($user_info) {
        // Créer un objet info avec les données disponibles
        $info = [
            'id_enseignant' => $user_id,
            'nom' => $user_info['nom'] ?? 'Non défini',
            'prenom' => $user_info['prenom'] ?? 'Non défini',
            'email' => $user_info['email'],
            'specialite' => 'Non définie'
        ];

        // Si l'utilisateur a une spécialité, essayer de la récupérer
        if (!empty($user_info['id_specialite'])) {
            $stmt = $pdo->prepare("SELECT nom_specialite FROM specialite WHERE id_specialite = ?");
            $stmt->execute([$user_info['id_specialite']]);
            $specialite = $stmt->fetch();

            if ($specialite) {
                $info['specialite'] = $specialite['nom_specialite'];
            }
        }

        // Définir id_enseignant pour les requêtes suivantes
        $_SESSION['id_enseignant'] = $user_id;
    }
}

// Si aucune information n'a été trouvée, rediriger vers la page de connexion
if (!$info) {
    $_SESSION['error'] = "Aucune information d'enseignant trouvée.";
    header('Location: login_coordinateur.php');
    exit();
}

// ID à utiliser pour les requêtes suivantes
$id = $info['id_enseignant'];

// Initialiser les variables pour éviter les erreurs
$matieres = [];
$groupes = [];
$emploi = [];

try {
    // Vérifier si la table enseignants_matieres existe
    $stmt = $pdo->query("SHOW TABLES LIKE 'enseignants_matieres'");
    if ($stmt->rowCount() > 0) {
        $matieres_stmt = $pdo->prepare("
            SELECT m.nom, m.code
            FROM enseignants_matieres em
            JOIN matieres m ON em.id_matiere = m.id_matiere
            WHERE em.id_enseignant = ?");
        $matieres_stmt->execute([$id]);
        $matieres = $matieres_stmt->fetchAll();
    }

    // Vérifier si la table groupes_enseignants existe
    $stmt = $pdo->query("SHOW TABLES LIKE 'groupes_enseignants'");
    if ($stmt->rowCount() > 0) {
        $groupes_stmt = $pdo->prepare("
            SELECT g.nom, g.type, g.filiere, g.niveau, g.annee_scolaire, ge.role
            FROM groupes_enseignants ge
            JOIN groupes g ON ge.id_groupe = g.id_groupe
            WHERE ge.id_enseignant = ?");
        $groupes_stmt->execute([$id]);
        $groupes = $groupes_stmt->fetchAll();
    }

    // Vérifier si la table enseignants_creneaux existe
    $stmt = $pdo->query("SHOW TABLES LIKE 'enseignants_creneaux'");
    if ($stmt->rowCount() > 0) {
        $emploi_stmt = $pdo->prepare("
            SELECT c.jour, c.heure_debut, c.heure_fin, g.nom AS groupe, s.nom AS salle
            FROM enseignants_creneaux ec
            JOIN creneaux c ON ec.id_creneau = c.id_creneau
            JOIN groupes g ON c.id_groupe = g.id_groupe
            LEFT JOIN salles s ON c.id_salle = s.id_salle
            WHERE ec.id_enseignant = ?
            ORDER BY FIELD(c.jour, 'Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi'), c.heure_debut");
        $emploi_stmt->execute([$id]);
        $emploi = $emploi_stmt->fetchAll();
    }
} catch (PDOException $e) {
    // Gérer l'erreur silencieusement
    $error_message = "Erreur lors de la récupération des données: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Dashboard Enseignant</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #1E90FF;
            --primary-magenta: magenta;
            --blue-transparent: rgba(30, 144, 255, 0.3);
        }

        body {
            background: url('image copy 4.png') no-repeat center center fixed;
            background-size: cover;
            color: white;
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            display: flex;
            color: white;
        }

        body::before {
            content: "";
            position: fixed;
            top: 0; left: 0; right: 0; bottom: 0;
            background-color: rgba(10, 25, 47, 0.85);
            z-index: -1;
        }

        .sidebar {
            width: 250px;
            background-color: rgba(10, 25, 47, 0.95);
            padding: 2rem 1rem;
            height: 100vh;
            position: fixed;
            top: 0;
            left: 0;
            border-right: 2px solid var(--primary-magenta);
            box-shadow: 2px 0 10px rgba(0,0,0,0.2);
        }

        .sidebar h2 {
            font-size: 1.8rem;
            color: var(--primary-blue);
            text-align: center;
            margin-bottom: 2rem;
        }

        .sidebar a {
            display: block;
            padding: 10px 15px;
            color: white;
            text-decoration: none;
            margin-bottom: 10px;
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 6px;
            transition: all 0.3s;
        }

        .sidebar a:hover {
            background-color: var(--primary-blue);
            color: white;
        }

        .main-content {
            margin-left: 250px;
            width: calc(100% - 250px);
        }

        .header-container {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 2rem;
            background: linear-gradient(90deg, var(--primary-blue) 0%, #0a192f 100%);
            border-bottom: 2px solid var(--primary-magenta);
            box-shadow: 0 2px 15px var(--blue-transparent);
            animation: headerGlow 8s infinite alternate;
        }

        @keyframes headerGlow {
            0%, 100% { border-bottom-color: var(--primary-blue); }
            50% { border-bottom-color: var(--primary-magenta); }
        }

        .header-title {
            font-size: 2.5rem;
            font-weight: bold;
            text-shadow: 0 0 10px var(--primary-blue);
            animation: textPulse 5s infinite;
        }

        @keyframes textPulse {
            0%, 100% { text-shadow: 0 0 10px var(--primary-blue); }
            50% { text-shadow: 0 0 15px var(--primary-magenta); }
        }

        .container {
            padding: 2rem;
        }

        .card {
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--primary-blue);
            border-radius: 12px;
            margin-bottom: 2rem;
            box-shadow: 0 0 10px rgba(30, 144, 255, 0.2);
        }

        .table th, .table td {
            color: white;
        }
        .sidebar img {
    max-width: 150px;
    width: 100%;
    height: auto;
    display: block;
    margin: 0 auto 20px;
}
.container > .card h3, .container > .card p {
    color: white !important;
}

    </style>
</head>
<body>

    <!-- Barre latérale -->
    <div class="sidebar">
        <img src="image copy 5.png" alt="Logo">
        <a href="Affichage_liste_UE.php">Affichage la liste de UE</a>
        <a href="souhaits_enseignants.php">Souhaits Enseignants</a>
        <a href="Calcul_automatique_charge_horaire.php">Calcul automatique de la charge horaire</a>
        <a href="Notification_non-respect_charge_minimale.php ">Notification en cas de non-respect de la charge minimale</a>
        <a href=" Consulter_modules_assurés_assure.php "> Consulter la liste des modules assurés et qu'il assure. </a>
        <a href="Uploader_notes_session_normale_rattrapage.php ">Uploader les notes de la session normale et rattrapage. </a>
        <a href="Consulter_historique_années_passées. ">Consulter l’historique des années passées. </a>
        <a href="?logout=true" class="btn btn-danger w-100 mt-3">Déconnexion</a>
    </div>

    <!-- Contenu principal -->
    <div class="main-content">
        <div class="header-container">
            <div class="header-title">Tableau de Bord - Enseignant</div>
        </div>

        <div class="container"style="float: right; width: 400px;color: white !important;">
            <div class="card p-3">
                <h3>Bienvenue <?= htmlspecialchars($info['prenom'] . ' ' . $info['nom']) ?></h3>
                <p>Email : <?= htmlspecialchars($info['email']) ?> | Spécialité : <?= htmlspecialchars($info['specialite']) ?></p>
            </div>

            <!-- <div class="card p-3">
                <h4>Matières enseignées</h4>
                <ul>
                    <?php if (!empty($matieres)): ?>
                        <?php foreach ($matieres as $m): ?>
                            <li><?= htmlspecialchars($m['code'] . ' - ' . $m['nom']) ?></li>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <li>Aucune matière assignée pour le moment</li>
                    <?php endif; ?> -->
                </ul>
            </div>

            <!-- <div class="card p-3">
                <h4>Groupes encadrés</h4>
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Nom</th><th>Type</th><th>Filière</th><th>Niveau</th><th>Année</th><th>Rôle</th>
                        </tr>
                    </thead>
                    <tbody> -->
                        <?php if (!empty($groupes)): ?>
                            <?php foreach ($groupes as $g): ?>
                                <tr>
                                    <td><?= htmlspecialchars($g['nom']) ?></td>
                                    <td><?= htmlspecialchars($g['type']) ?></td>
                                    <td><?= htmlspecialchars($g['filiere']) ?></td>
                                    <td><?= htmlspecialchars($g['niveau']) ?></td>
                                    <td><?= htmlspecialchars($g['annee_scolaire']) ?></td>
                                    <td><?= htmlspecialchars($g['role']) ?></td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="6" class="text-center">Aucun groupe assigné pour le moment</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- <div class="card p-3">
                <h4>Emploi du temps</h4>
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Jour</th><th>Début</th><th>Fin</th><th>Groupe</th><th>Salle</th>
                        </tr>
                    </thead> -->
                    <tbody>
                        <?php if (!empty($emploi)): ?>
                            <?php foreach ($emploi as $e): ?>
                                <tr>
                                    <td><?= htmlspecialchars($e['jour']) ?></td>
                                    <td><?= substr($e['heure_debut'], 0, 5) ?></td>
                                    <td><?= substr($e['heure_fin'], 0, 5) ?></td>
                                    <td><?= htmlspecialchars($e['groupe']) ?></td>
                                    <td><?= htmlspecialchars($e['salle'] ?? 'Non définie') ?></td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="5" class="text-center">Aucun emploi du temps disponible pour le moment</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

</body>
</html>