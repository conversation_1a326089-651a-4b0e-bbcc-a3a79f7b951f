<?php
session_start();

// Debug: Afficher les variables de session
echo "<!-- Debug Session: ";
print_r($_SESSION);
echo " -->";

// Vérification de la session - doit être un enseignant
if (!isset($_SESSION['user_type']) || $_SESSION['user_type'] !== 'enseignant') {
    header('Location: ../login_coordinateur.php');
    exit();
}

if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: ../login_coordinateur.php');
    exit();
}

try {
    $pdo = new PDO("mysql:host=localhost;dbname=gestion-coordinteur;charset=utf8", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Erreur de connexion : " . $e->getMessage());
}

$user_id = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null;
$user_type = isset($_SESSION['user_type']) ? $_SESSION['user_type'] : '';
$info = null;

// Utiliser uniquement la table utilisateurs avec type_utilisateur = 'enseignant'
if ($user_type === 'enseignant' && $user_id) {
    // D'abord, récupérer les informations de base de l'utilisateur
    $enseignant = $pdo->prepare("SELECT * FROM utilisateurs WHERE id = ? AND type_utilisateur = 'enseignant'");
    $enseignant->execute(array($user_id));
    $user_info = $enseignant->fetch();

    if ($user_info) {
        $info = array(
            'id_enseignant' => $user_id,
            'nom' => isset($user_info['nom']) ? $user_info['nom'] : 'Non défini',
            'prenom' => isset($user_info['prenom']) ? $user_info['prenom'] : 'Non défini',
            'email' => isset($user_info['email']) ? $user_info['email'] : 'Non défini',
            'specialite' => 'Non définie',
            'departement' => 'Non défini'
        );

        // Essayer de récupérer la spécialité si la colonne existe
        if (isset($user_info['id_specialite']) && !empty($user_info['id_specialite'])) {
            try {
                $stmt_spec = $pdo->prepare("SELECT nom_specialite FROM specialite WHERE id_specialite = ?");
                $stmt_spec->execute(array($user_info['id_specialite']));
                $specialite = $stmt_spec->fetch();
                if ($specialite) {
                    $info['specialite'] = $specialite['nom_specialite'];
                }
            } catch (PDOException $e) {
                // Ignorer l'erreur si la table ou colonne n'existe pas
            }
        }

        // Essayer de récupérer le département si la colonne existe
        if (isset($user_info['id_departement']) && !empty($user_info['id_departement'])) {
            try {
                $stmt_dept = $pdo->prepare("SELECT nom_departement FROM departement WHERE id_departement = ?");
                $stmt_dept->execute(array($user_info['id_departement']));
                $departement = $stmt_dept->fetch();
                if ($departement) {
                    $info['departement'] = $departement['nom_departement'];
                }
            } catch (PDOException $e) {
                // Ignorer l'erreur si la table ou colonne n'existe pas
            }
        }

        // Définir id_enseignant pour compatibilité
        $_SESSION['id_enseignant'] = $user_id;
    }
}

// Récupérer les matières enseignées par cet enseignant
$matieres_enseignees = array();
if ($info && $user_id) {
    try {
        $stmt_matieres = $pdo->prepare("SELECT * FROM matieres WHERE id_utilisateur = ? ORDER BY nom");
        $stmt_matieres->execute(array($user_id));
        $matieres_enseignees = $stmt_matieres->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        // Ignorer l'erreur si la table matieres n'existe pas
        $matieres_enseignees = array();
    }
}

if (!$info) {
    $_SESSION['error'] = "Aucune information d'enseignant trouvée.";
    header('Location: ../login_coordinateur.php');
    exit();
}
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Dashboard Enseignant</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: url('../image copy 4.png') no-repeat center center fixed;
            background-size: cover;
            color: white;
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            display: flex;
        }
        body::before {
            content: "";
            position: fixed;
            top: 0; left: 0; right: 0; bottom: 0;
            background-color: rgba(10, 25, 47, 0.85);
            z-index: -1;
        }
        .sidebar {
            width: 250px;
            background-color: rgba(10, 25, 47, 0.95);
            padding: 2rem 1rem;
            height: 100vh;
            position: fixed;
            top: 0;
            left: 0;
            border-right: 2px solid magenta;
            box-shadow: 2px 0 10px rgba(0,0,0,0.2);
        }
        .sidebar a {
            display: block;
            padding: 10px 15px;
            color: white;
            text-decoration: none;
            margin-bottom: 10px;
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 6px;
            transition: all 0.3s;
        }
        .sidebar a:hover {
            background-color: #1E90FF;
            color: white;
        }
        .main-content {
            margin-left: 250px;
            width: calc(100% - 250px);
        }
        .header-container {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 2rem;
            background: linear-gradient(90deg, #1E90FF 0%, #0a192f 100%);
            border-bottom: 2px solid magenta;
        }
        .header-title {
            font-size: 2.5rem;
            font-weight: bold;
            text-shadow: 0 0 10px #1E90FF;
        }
        .container {
            padding: 2rem;
        }
        .card {
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid #1E90FF;
            border-radius: 12px;
            margin-bottom: 2rem;
            box-shadow: 0 0 10px rgba(30, 144, 255, 0.2);
        }
        .sidebar img {
            max-width: 150px;
            width: 100%;
            height: auto;
            display: block;
            margin: 0 auto 20px;
        }
        .container > .card h3, .container > .card p {
            color: white !important;
        }
        .table-dark {
            background-color: rgba(0, 0, 0, 0.3);
        }
        .table-dark th, .table-dark td {
            border-color: rgba(255, 255, 255, 0.2);
            color: white;
        }
        .table-dark thead th {
            background-color: rgba(30, 144, 255, 0.3);
            border-color: #1E90FF;
        }
        .text-info {
            color: #17a2b8 !important;
        }
        .text-warning {
            color: #ffc107 !important;
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <img src="image copy 5.png" alt="Logo">
        <a href="Affichage_liste_UE.php">Affichage la liste de UE</a>
        <a href="souhaits_enseignants.php">Souhaits Enseignants</a>
        <a href="Calcul_automatique_charge_horaire.php">Calcul automatique de la charge horaire</a>
        <a href="Notification_non-respect_charge_minimale.php">Notification en cas de non-respect de la charge minimale</a>
        <a href="Consulter_modules_assurés_assure.php">Consulter la liste des modules assurés et qu'il assure.</a>
        <a href="Uploader_notes_session_normale_rattrapage.php">Uploader les notes de la session normale et rattrapage.</a>
        <a href="Consulter_historique_années_passées.">Consulter l'historique des années passées.</a>
        <a href="?logout=true" class="btn btn-danger w-100 mt-3">Déconnexion</a>
    </div>
    <div class="main-content">
        <div class="header-container">
            <div class="header-title">Tableau de Bord - Enseignant</div>
        </div>
        <div class="container" style="float: right; width: 400px; color: white !important;">
            <div class="card p-3">
                <h3>Bienvenue <?php echo htmlspecialchars($info['prenom'] . ' ' . $info['nom']); ?></h3>
                <p><strong>Email :</strong> <?php echo htmlspecialchars($info['email']); ?></p>
                <p><strong>Département :</strong> <?php echo htmlspecialchars($info['departement']); ?></p>
                <p><strong>Spécialité :</strong> <?php echo htmlspecialchars($info['specialite']); ?></p>
            </div>

            <div class="card p-3">
                <h4>Matières Enseignées</h4>
                <?php if (!empty($matieres_enseignees)): ?>
                    <div class="table-responsive">
                        <table class="table table-dark table-striped">
                            <thead>
                                <tr>
                                    <th>Code</th>
                                    <th>Nom de la Matière</th>
                                    <th>Crédits</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($matieres_enseignees as $matiere): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($matiere['code']); ?></td>
                                    <td><?php echo htmlspecialchars($matiere['nom']); ?></td>
                                    <td><?php echo htmlspecialchars($matiere['credit']); ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <p class="text-info"><strong>Total :</strong> <?php echo count($matieres_enseignees); ?> matière(s)</p>
                <?php else: ?>
                    <p class="text-warning">Aucune matière assignée pour le moment.</p>
                <?php endif; ?>
            </div>

            <div class="card p-3">
                <h4>Informations de session (Debug)</h4>
                <p><strong>User Type:</strong> <?php echo isset($_SESSION['user_type']) ? $_SESSION['user_type'] : 'Non défini'; ?></p>
                <p><strong>User ID:</strong> <?php echo isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 'Non défini'; ?></p>
                <p><strong>ID Enseignant:</strong> <?php echo isset($_SESSION['id_enseignant']) ? $_SESSION['id_enseignant'] : 'Non défini'; ?></p>
                <p><strong>Email:</strong> <?php echo isset($_SESSION['email']) ? $_SESSION['email'] : 'Non défini'; ?></p>
            </div>
        </div>
    </div>
</body>
</html>
