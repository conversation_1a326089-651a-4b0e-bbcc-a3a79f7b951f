<?php
// Connexion à la base de données
$host = 'localhost';
$dbname = 'gestion_coordinteur';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die("Erreur de connexion : " . $e->getMessage());
}

// Récupérer toutes les unités d'enseignement
function getUnitesEnseignements() {
    global $pdo;
    $stmt = $pdo->query("
        SELECT * FROM unites_enseignements
        ORDER BY filiere, niveau, type_enseignement
    ");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Récupérer les matières pour le formulaire d'ajout
function getMatieres() {
    global $pdo;
    try {
        $stmt = $pdo->query("SELECT id_matiere, nom FROM matieres ORDER BY nom");
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        // Si la table n'existe pas, retourner un tableau vide
        return [];
    }
}

// Traitement de l'ajout d'une UE
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['ajouter'])) {
    try {
        $id_matiere = $_POST['id_matiere'];
        $filiere = $_POST['filiere'];
        $niveau = $_POST['niveau'];
        $annee_scolaire = $_POST['annee_scolaire'];
        $type_enseignement = $_POST['type_enseignement'];
        $volume_horaire = $_POST['volume_horaire'];

        $stmt = $pdo->prepare("
            INSERT INTO unites_enseignements 
            (id_matiere, filiere, niveau, annee_scolaire, type_enseignement, volume_horaire)
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([$id_matiere, $filiere, $niveau, $annee_scolaire, $type_enseignement, $volume_horaire]);
        
        $message = "Unité d'enseignement ajoutée avec succès";
        $messageType = "success";
    } catch (PDOException $e) {
        $message = "Erreur lors de l'ajout : " . $e->getMessage();
        $messageType = "danger";
    }
}

// Traitement de la modification d'une UE
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['modifier'])) {
    try {
        $id_ue = $_POST['id_ue'];
        $id_matiere = $_POST['id_matiere'];
        $filiere = $_POST['filiere'];
        $niveau = $_POST['niveau'];
        $annee_scolaire = $_POST['annee_scolaire'];
        $type_enseignement = $_POST['type_enseignement'];
        $volume_horaire = $_POST['volume_horaire'];

        $stmt = $pdo->prepare("
            UPDATE unites_enseignements 
            SET id_matiere = ?, filiere = ?, niveau = ?, annee_scolaire = ?, 
                type_enseignement = ?, volume_horaire = ?
            WHERE id_ue = ?
        ");
        
        $stmt->execute([$id_matiere, $filiere, $niveau, $annee_scolaire, $type_enseignement, $volume_horaire, $id_ue]);
        
        $message = "Unité d'enseignement modifiée avec succès";
        $messageType = "success";
    } catch (PDOException $e) {
        $message = "Erreur lors de la modification : " . $e->getMessage();
        $messageType = "danger";
    }
}

// Traitement de la suppression d'une UE
if (isset($_GET['supprimer'])) {
    try {
        $id_ue = $_GET['supprimer'];
        
        $stmt = $pdo->prepare("DELETE FROM unites_enseignements WHERE id_ue = ?");
        $stmt->execute([$id_ue]);
        
        $message = "Unité d'enseignement supprimée avec succès";
        $messageType = "success";
    } catch (PDOException $e) {
        $message = "Erreur lors de la suppression : " . $e->getMessage();
        $messageType = "danger";
    }
}

$unites_enseignements = getUnitesEnseignements();
$matieres = getMatieres();
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion des Unités d'Enseignement</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/buttons/2.4.1/css/buttons.bootstrap5.min.css" rel="stylesheet">
    <style>
        :root {
            --royal-blue: #4169E1;     /* Bleu royal */
            --dark-blue: #0038a8;      /* Bleu royal foncé */
            --accent-gold: #FFD700;    /* Or pour accents */
            --light-gray: #f8f9fa;     /* Gris clair pour le fond */
            --white: #ffffff;          /* Blanc */
            --dark-text: #333333;      /* Texte foncé */
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--light-gray);
            color: var(--dark-text);
        }

        /* Sidebar */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: 280px;
            background: linear-gradient(135deg, var(--royal-blue), var(--dark-blue));
            color: var(--white);
            padding-top: 20px;
            z-index: 1000;
            box-shadow: 4px 0 10px rgba(0,0,0,0.1);
        }

        .sidebar-header {
            padding: 20px 25px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            margin-bottom: 20px;
        }

        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 25px;
            margin: 8px 0;
            border-radius: 0 30px 30px 0;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover, .sidebar .nav-link.active {
            background-color: rgba(255,255,255,0.1);
            color: var(--white);
            padding-left: 30px;
        }

        .sidebar .nav-link i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }

        /* Contenu principal */
        .main-content {
            margin-left: 280px;
            padding: 40px;
            width: calc(100% - 280px);
            background-color: rgba(255,255,255,0.92);
            min-height: 100vh;
            backdrop-filter: blur(8px);
        }

        /* Tableau */
        .table-container {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: transform 0.3s ease;
        }

        .table-container:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
        }

        .table-ue {
            margin-bottom: 0;
            width: 100%;
        }

        .table-ue thead {
            background: linear-gradient(135deg, var(--royal-blue), var(--dark-blue));
            color: white;
        }

        .table-ue th {
            font-weight: 500;
            padding: 16px 25px;
            border: none;
            font-size: 0.95rem;
        }

        .table-ue td {
            padding: 16px 25px;
            vertical-align: middle;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }

        /* Boutons */
        .btn-action {
            padding: 8px 15px;
            border-radius: 50px;
            font-size: 0.85rem;
            font-weight: 500;
            margin-right: 5px;
            transition: all 0.3s ease;
        }

        .btn-edit {
            background-color: var(--royal-blue);
            color: white;
            border: none;
        }

        .btn-delete {
            background-color: #dc3545;
            color: white;
            border: none;
        }

        .btn-add {
            background: linear-gradient(135deg, var(--royal-blue), var(--dark-blue));
            color: white;
            border: none;
            padding: 10px 25px;
            border-radius: 50px;
            font-weight: 500;
            box-shadow: 0 4px 15px rgba(65,105,225,0.3);
            transition: all 0.3s ease;
        }

        .btn-add:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(65,105,225,0.4);
            background: linear-gradient(135deg, var(--dark-blue), var(--royal-blue));
            color: white;
        }

        /* Formulaires */
        .form-control, .form-select {
            padding: 12px 15px;
            border-radius: 8px;
            border: 1px solid rgba(0,0,0,0.1);
            background-color: rgba(255,255,255,0.9);
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--royal-blue);
            box-shadow: 0 0 0 3px rgba(65,105,225,0.25);
        }

        /* DataTables */
        .dataTables_wrapper .dataTables_filter input {
            padding: 8px 15px;
            border-radius: 50px;
            border: 1px solid rgba(0,0,0,0.1);
            margin-left: 10px;
        }

        .dataTables_wrapper .dataTables_length select {
            padding: 8px 15px;
            border-radius: 8px;
            border: 1px solid rgba(0,0,0,0.1);
        }

        .dataTables_wrapper .dataTables_info {
            padding-top: 20px;
            font-size: 0.9rem;
            color: rgba(0,0,0,0.6);
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button {
            padding: 8px 15px;
            border-radius: 50px;
            margin: 0 3px;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button.current {
            background: linear-gradient(135deg, var(--royal-blue), var(--dark-blue));
            color: white !important;
            border: none;
        }

        /* Boutons d'export */
        .export-buttons {
            margin-bottom: 20px;
        }

        .btn-excel, .btn-print {
            padding: 10px 20px;
            border-radius: 50px;
            font-weight: 500;
            margin-right: 10px;
            transition: all 0.3s ease;
        }

        .btn-excel {
            background-color: #1D6F42;
            color: white;
            border: none;
        }

        .btn-print {
            background-color: #6c757d;
            color: white;
            border: none;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar">
        <div class="sidebar-header">
            <h3><i class="fas fa-university"></i> ENSAH</h3>
            <p>Système de Gestion</p>
        </div>
        <ul class="nav flex-column px-3">
            <li class="nav-item">
                <a class="nav-link" href="dashboard_coordinateur.php">
                    <i class="fas fa-tachometer-alt"></i> Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="gestion_unites_enseignements.php">
                    <i class="fas fa-book"></i> Unités d'Enseignement
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="gestion_professeurs.php">
                    <i class="fas fa-chalkboard-teacher"></i> Professeurs
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="emplois_temps.php">
                    <i class="fas fa-calendar-alt"></i> Emplois du temps
                </a>
            </li>
            <li class="nav-item mt-4">
                <a class="nav-link" href="logout.php" style="color: var(--accent-gold);">
                    <i class="fas fa-sign-out-alt"></i> Déconnexion
                </a>
            </li>
        </ul>
    </nav>

    <!-- Contenu principal -->
    <div class="main-content">
        <div class="container-fluid">
            <div class="row mb-4">
                <div class="col-md-8">
                    <h2><i class="fas fa-book"></i> Gestion des Unités d'Enseignement</h2>
                    <p class="text-muted">Gérez les unités d'enseignement de l'établissement</p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-add" data-bs-toggle="modal" data-bs-target="#ajoutModal">
                        <i class="fas fa-plus-circle"></i> Ajouter une UE
                    </button>
                </div>
            </div>

            <?php if (isset($message)): ?>
                <div class="alert alert-<?= $messageType ?> alert-dismissible fade show" role="alert">
                    <?= $message ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-12">
                    <div class="card table-container">
                        <div class="card-body">
                            <div class="export-buttons mb-4">
                                <button id="export-excel" class="btn btn-excel">
                                    <i class="fas fa-file-excel"></i> Exporter Excel
                                </button>
                                <button id="print-table" class="btn btn-print">
                                    <i class="fas fa-print"></i> Imprimer
                                </button>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-ue" id="table-ue">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Matière</th>
                                            <th>Filière</th>
                                            <th>Niveau</th>
                                            <th>Année Scolaire</th>
                                            <th>Type</th>
                                            <th>Volume Horaire</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($unites_enseignements as $ue): ?>
                                            <tr>
                                                <td><?= $ue['id_ue'] ?></td>
                                                <td><?= $ue['id_matiere'] ?></td>
                                                <td><?= htmlspecialchars($ue['filiere']) ?></td>
                                                <td><?= htmlspecialchars($ue['niveau']) ?></td>
                                                <td><?= htmlspecialchars($ue['annee_scolaire']) ?></td>
                                                <td><?= htmlspecialchars($ue['type_enseignement']) ?></td>
                                                <td><?= $ue['volume_horaire'] ?> h</td>
                                                <td>
                                                    <button class="btn btn-action btn-edit" 
                                                            data-bs-toggle="modal" 
                                                            data-bs-target="#modifierModal"
                                                            data-id="<?= $ue['id_ue'] ?>"
                                                            data-matiere="<?= $ue['id_matiere'] ?>"
                                                            data-filiere="<?= htmlspecialchars($ue['filiere']) ?>"
                                                            data-niveau="<?= htmlspecialchars($ue['niveau']) ?>"
                                                            data-annee="<?= htmlspecialchars($ue['annee_scolaire']) ?>"
                                                            data-type="<?= htmlspecialchars($ue['type_enseignement']) ?>"
                                                            data-volume="<?= $ue['volume_horaire'] ?>">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <a href="?supprimer=<?= $ue['id_ue'] ?>" class="btn btn-action btn-delete" 
                                                       onclick="return confirm('Êtes-vous sûr de vouloir supprimer cette UE ?')">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Ajout -->
    <div class="modal fade" id="ajoutModal" tabindex="-1" aria-labelledby="ajoutModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="ajoutModalLabel">Ajouter une Unité d'Enseignement</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form action="" method="POST">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="id_matiere" class="form-label">Matière</label>
                                <select class="form-select" id="id_matiere" name="id_matiere" required>
                                    <option value="">Sélectionner une matière</option>
                                    <?php foreach ($matieres as $matiere): ?>
                                        <option value="<?= $matiere['id_matiere'] ?>"><?= htmlspecialchars($matiere['nom']) ?></option>
                                    <?php endforeach; ?>
                                    <?php if (empty($matieres)): ?>
                                        <option value="1">Matière 1</option>
                                        <option value="2">Matière 2</option>
                                    <?php endif; ?>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="filiere" class="form-label">Filière</label>
                                <input type="text" class="form-control" id="filiere" name="filiere" required>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="niveau" class="form-label">Niveau</label>
                                <input type="text" class="form-control" id="niveau" name="niveau" required>
                            </div>
                            <div class="col-md-6">
                                <label for="annee_scolaire" class="form-label">Année Scolaire</label>
                                <input type="text" class="form-control" id="annee_scolaire" name="annee_scolaire" required>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="type_enseignement" class="form-label">Type d'Enseignement</label>
                                <select class="form-select" id="type_enseignement" name="type_enseignement" required>
                                    <option value="">Sélectionner un type</option>
                                    <option value="Cours">Cours</option>
                                    <option value="TD">TD</option>
                                    <option value="TP">TP</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="volume_horaire" class="form-label">Volume Horaire (heures)</label>
                                <input type="number" class="form-control" id="volume_horaire" name="volume_horaire" required>
                            </div>
                        </div>
                        <div class="text-end">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                            <button type="submit" name="ajouter" class="btn btn-primary">Ajouter</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Modifier -->
    <div class="modal fade" id="modifierModal" tabindex="-1" aria-labelledby="modifierModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modifierModalLabel">Modifier une Unité d'Enseignement</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form action="" method="POST">
                        <input type="hidden" id="edit_id_ue" name="id_ue">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="edit_id_matiere" class="form-label">Matière</label>
                                <select class="form-select" id="edit_id_matiere" name="id_matiere" required>
                                    <option value="">Sélectionner une matière</option>
                                    <?php foreach ($matieres as $matiere): ?>
                                        <option value="<?= $matiere['id_matiere'] ?>"><?= htmlspecialchars($matiere['nom']) ?></option>
                                    <?php endforeach; ?>
                                    <?php if (empty($matieres)): ?>
                                        <option value="1">Matière 1</option>
                                        <option value="2">Matière 2</option>
                                    <?php endif; ?>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="edit_filiere" class="form-label">Filière</label>
                                <input type="text" class="form-control" id="edit_filiere" name="filiere" required>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="edit_niveau" class="form-label">Niveau</label>
                                <input type="text" class="form-control" id="edit_niveau" name="niveau" required>
                            </div>
                            <div class="col-md-6">
                                <label for="edit_annee_scolaire" class="form-label">Année Scolaire</label>
                                <input type="text" class="form-control" id="edit_annee_scolaire" name="annee_scolaire" required>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="edit_type_enseignement" class="form-label">Type d'Enseignement</label>
                                <select class="form-select" id="edit_type_enseignement" name="type_enseignement" required>
                                    <option value="">Sélectionner un type</option>
                                    <option value="Cours">Cours</option>
                                    <option value="TD">TD</option>
                                    <option value="TP">TP</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="edit_volume_horaire" class="form-label">Volume Horaire (heures)</label>
                                <input type="number" class="form-control" id="edit_volume_horaire" name="volume_horaire" required>
                            </div>
                        </div>
                        <div class="text-end">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                            <button type="submit" name="modifier" class="btn btn-primary">Enregistrer</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.bootstrap5.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.html5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.print.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // Initialisation de DataTables
            var table = $('#table-ue').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/fr-FR.json'
                },
                responsive: true,
                dom: '<"top"f>rt<"bottom"ip><"clear">',
                pageLength: 10
            });
            
            // Bouton d'export Excel
            $('#export-excel').on('click', function() {
                table.button('.buttons-excel').trigger();
            });
            
            // Bouton d'impression
            $('#print-table').on('click', function() {
                table.button('.buttons-print').trigger();
            });
            
            // Remplir le formulaire de modification
            $('#modifierModal').on('show.bs.modal', function (event) {
                var button = $(event.relatedTarget);
                var id = button.data('id');
                var matiere = button.data('matiere');
                var filiere = button.data('filiere');
                var niveau = button.data('niveau');
                var annee = button.data('annee');
                var type = button.data('type');
                var volume = button.data('volume');
                
                var modal = $(this);
                modal.find('#edit_id_ue').val(id);
                modal.find('#edit_id_matiere').val(matiere);
                modal.find('#edit_filiere').val(filiere);
                modal.find('#edit_niveau').val(niveau);
                modal.find('#edit_annee_scolaire').val(annee);
                modal.find('#edit_type_enseignement').val(type);
                modal.find('#edit_volume_horaire').val(volume);
            });
        });
    </script>
</body>
</html>
