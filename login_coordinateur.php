<?php
session_start();

// Paramètres de connexion à la base de données
$host = 'localhost';
$user = 'root';
$pass = '';
$dbname = 'gestion_coordinteur';

// Connexion à la base de données
$conn = new mysqli($host, $user, $pass, $dbname);
if ($conn->connect_error) {
    die("Échec de la connexion : " . $conn->connect_error);
}

// Activer la journalisation des erreurs
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Créer un fichier de log pour le débogage
$log_file = 'login_debug.log';
file_put_contents($log_file, "=== Nouvelle tentative de connexion ===\n", FILE_APPEND);

// Traitement du formulaire de connexion
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = $_POST['email'];
    $mot_de_passe = $_POST['mot_de_passe'];
    $user_found = false;

    // Journaliser les informations de connexion (sans le mot de passe)
    file_put_contents($log_file, "Email: $email\n", FILE_APPEND);

    // 1. Vérification dans la table coordinateurs
    file_put_contents($log_file, "Vérification dans la table coordinateurs...\n", FILE_APPEND);
    $stmt = $conn->prepare("SELECT * FROM coordinateurs WHERE email = ?");
    if ($stmt === false) {
        file_put_contents($log_file, "Erreur de préparation de la requête coordinateurs: " . $conn->error . "\n", FILE_APPEND);
    } else {
        $stmt->bind_param("s", $email);
        $stmt->execute();
        $result = $stmt->get_result();
        file_put_contents($log_file, "Résultat coordinateurs: " . $result->num_rows . " ligne(s) trouvée(s)\n", FILE_APPEND);

        if ($result->num_rows === 1) {
        $user = $result->fetch_assoc();
        file_put_contents($log_file, "Utilisateur trouvé dans coordinateurs: ID=" . $user['id_coordinateur'] . "\n", FILE_APPEND);
        file_put_contents($log_file, "Vérification du mot de passe avec password_verify()...\n", FILE_APPEND);

        // Vérifier si le mot de passe est stocké avec password_hash
        $password_verify_result = password_verify($mot_de_passe, $user['mot_de_passe']);
        file_put_contents($log_file, "Résultat password_verify: " . ($password_verify_result ? "SUCCÈS" : "ÉCHEC") . "\n", FILE_APPEND);

        if ($password_verify_result) {
            // Récupérer les infos de l'enseignant associé
            $stmt_enseignant = $conn->prepare("SELECT * FROM enseignants WHERE id_enseignant = ?");
            $stmt_enseignant->bind_param("i", $user['id_enseignant']);
            $stmt_enseignant->execute();
            $enseignant = $stmt_enseignant->get_result()->fetch_assoc();

            // Stocker les informations en session
            $_SESSION['user_id'] = $user['id_coordinateur'];
            $_SESSION['email'] = $user['email'];
            $_SESSION['user_type'] = 'coordinateur';
            $_SESSION['id_enseignant'] = $user['id_enseignant'];
            $_SESSION['filiere'] = $user['filiere'];
            $_SESSION['annee_scolaire'] = $user['annee_scolaire'];
            $_SESSION['nom'] = $enseignant['nom'] ?? '';
            $_SESSION['prenom'] = $enseignant['prenom'] ?? '';
            $_SESSION['specialite'] = $enseignant['specialite'] ?? '';

            // Redirection via le fichier de débogage
            $_SESSION['redirect_from'] = 'login_coordinateur.php - section 1';
            header("Location: debug_redirect.php");
            exit;
            exit;
        } else {
            // Essayer une comparaison directe (au cas où le mot de passe n'est pas hashé)
            file_put_contents($log_file, "Essai de comparaison directe du mot de passe...\n", FILE_APPEND);
            $direct_compare = ($mot_de_passe === $user['mot_de_passe']);
            file_put_contents($log_file, "Résultat comparaison directe: " . ($direct_compare ? "SUCCÈS" : "ÉCHEC") . "\n", FILE_APPEND);

            if ($direct_compare) {
                // Récupérer les infos de l'enseignant associé
                $stmt_enseignant = $conn->prepare("SELECT * FROM enseignants WHERE id_enseignant = ?");
                $stmt_enseignant->bind_param("i", $user['id_enseignant']);
                $stmt_enseignant->execute();
                $enseignant = $stmt_enseignant->get_result()->fetch_assoc();

                // Stocker les informations en session
                $_SESSION['user_id'] = $user['id_coordinateur'];
                $_SESSION['email'] = $user['email'];
                $_SESSION['user_type'] = 'coordinateur';
                $_SESSION['id_enseignant'] = $user['id_enseignant'];
                $_SESSION['filiere'] = $user['filiere'];
                $_SESSION['annee_scolaire'] = $user['annee_scolaire'];
                $_SESSION['nom'] = $enseignant['nom'] ?? '';
                $_SESSION['prenom'] = $enseignant['prenom'] ?? '';
                $_SESSION['specialite'] = $enseignant['specialite'] ?? '';

                // Redirection via le fichier de débogage
                $_SESSION['redirect_from'] = 'login_coordinateur.php - section 2';
                header("Location: debug_redirect.php");
                exit;
                exit;
            } else {
                $erreur = "Mot de passe incorrect pour coordinateur.";
                $user_found = true;
            }
        }
    }
    }

    // 2. Si pas trouvé comme coordinateur, vérification dans la table enseignants
    if (!$user_found) {
        file_put_contents($log_file, "Vérification dans la table enseignants...\n", FILE_APPEND);
        $stmt = $conn->prepare("SELECT * FROM enseignants WHERE email = ?");
        if ($stmt === false) {
            file_put_contents($log_file, "Erreur de préparation de la requête enseignants: " . $conn->error . "\n", FILE_APPEND);
        } else {
            $stmt->bind_param("s", $email);
            $stmt->execute();
            $result = $stmt->get_result();
            file_put_contents($log_file, "Résultat enseignants: " . $result->num_rows . " ligne(s) trouvée(s)\n", FILE_APPEND);

        if ($result->num_rows === 1) {
            $user = $result->fetch_assoc();
            file_put_contents($log_file, "Utilisateur trouvé dans enseignants: ID=" . $user['id_enseignant'] . "\n", FILE_APPEND);
            file_put_contents($log_file, "Vérification du mot de passe avec password_verify()...\n", FILE_APPEND);

            // Vérifier si le mot de passe est stocké avec password_hash
            $password_verify_result = password_verify($mot_de_passe, $user['mot_de_passe']);
            file_put_contents($log_file, "Résultat password_verify: " . ($password_verify_result ? "SUCCÈS" : "ÉCHEC") . "\n", FILE_APPEND);

            if ($password_verify_result) {
                // Stocker les informations en session
                $_SESSION['user_id'] = $user['id_enseignant'];
                $_SESSION['email'] = $user['email'];
                $_SESSION['user_type'] = 'enseignant'; // Type d'utilisateur pour les enseignants
                $_SESSION['nom'] = $user['nom'] ?? '';
                $_SESSION['prenom'] = $user['prenom'] ?? '';
                $_SESSION['specialite'] = $user['specialite'] ?? '';
                $_SESSION['id_enseignant'] = $user['id_enseignant'];
               //

                // Vérifier si c'est aussi un admin
                $stmt_admin = $conn->prepare("SELECT * FROM utilisateurs WHERE email = ? AND type_utilisateur = 'admin'");
                $stmt_admin->bind_param("s", $user['email']);
                $stmt_admin->execute();
                $admin_result = $stmt_admin->get_result();

                if ($admin_result && $admin_result->num_rows > 0) {
                    $admin_user = $admin_result->fetch_assoc();
                    $_SESSION['user_id'] = $admin_user['id'] ?? $user['id_enseignant'];
                    $_SESSION['user_type'] = 'admin';
                    header("Location: admin_dashboard.php");
                    exit;
                }
                //


                // Vérifier si c'est aussi un coordinateur
                $stmt_coord = $conn->prepare("SELECT * FROM coordinateurs WHERE id_enseignant = ?");
                $stmt_coord->bind_param("i", $user['id_enseignant']);
                $stmt_coord->execute();
                if ($stmt_coord->get_result()->num_rows > 0) {
                    $_SESSION['user_type'] = 'coordinateur';
                    // Redirection via le fichier de débogage
                    $_SESSION['redirect_from'] = 'login_coordinateur.php - section 3';
                    header("Location: debug_redirect.php");
                    exit;
                } else {
                    // Redirection forcée vers dashboard_enseignant.php avec chemin complet
                    echo "<script>window.location.href = '/gestion_professeurs/dashboard_enseignant.php';</script>";
                    header("Location: /gestion_professeurs/dashboard_enseignant.php");
                    die("Redirection vers le tableau de bord enseignant...");
                }
                exit;
            } else {
                // Essayer une comparaison directe (au cas où le mot de passe n'est pas hashé)
                file_put_contents($log_file, "Essai de comparaison directe du mot de passe pour enseignant...\n", FILE_APPEND);
                $direct_compare = ($mot_de_passe === $user['mot_de_passe']);
                file_put_contents($log_file, "Résultat comparaison directe: " . ($direct_compare ? "SUCCÈS" : "ÉCHEC") . "\n", FILE_APPEND);
                file_put_contents($log_file, "Mot de passe entré: " . substr($mot_de_passe, 0, 3) . "... | Mot de passe stocké: " . substr($user['mot_de_passe'], 0, 3) . "...\n", FILE_APPEND);

                if ($direct_compare) {
                    // Stocker les informations en session
                    $_SESSION['user_id'] = $user['id_enseignant'];
                    $_SESSION['email'] = $user['email'];
                    $_SESSION['user_type'] = 'enseignant'; // Type d'utilisateur pour les enseignants
                    $_SESSION['nom'] = $user['nom'] ?? '';
                    $_SESSION['prenom'] = $user['prenom'] ?? '';
                    $_SESSION['specialite'] = $user['specialite'] ?? '';
                    $_SESSION['id_enseignant'] = $user['id_enseignant'];

                    // Vérifier si c'est aussi un admin
                    $stmt_admin = $conn->prepare("SELECT * FROM utilisateurs WHERE email = ? AND type_utilisateur = 'admin'");
                    $stmt_admin->bind_param("s", $user['email']);
                    $stmt_admin->execute();
                    $admin_result = $stmt_admin->get_result();

                    if ($admin_result && $admin_result->num_rows > 0) {
                        $admin_user = $admin_result->fetch_assoc();
                        $_SESSION['user_id'] = $admin_user['id'] ?? $user['id_enseignant'];
                        $_SESSION['user_type'] = 'admin';
                        header("Location: admin_dashboard.php");
                        exit;
                    }

                    // Vérifier si c'est aussi un coordinateur
                    $stmt_coord = $conn->prepare("SELECT * FROM coordinateurs WHERE id_enseignant = ?");
                    $stmt_coord->bind_param("i", $user['id_enseignant']);
                    $stmt_coord->execute();
                    if ($stmt_coord->get_result()->num_rows > 0) {
                        $_SESSION['user_type'] = 'coordinateur';
                        // Redirection via le fichier de débogage
                        $_SESSION['redirect_from'] = 'login_coordinateur.php - section 4';
                        header("Location: debug_redirect.php");
                        exit;
                    } else {
                        // Redirection vers dashboard_enseignant.php
                        echo "<script>window.location.href = '/gestion_professeurs/dashboard_enseignant.php';</script>";
                        header("Location: /gestion_professeurs/dashboard_enseignant.php");
                        die("Redirection vers le tableau de bord enseignant...");
                    }
                    exit;
                } else {
                    $erreur = "Mot de passe incorrect pour enseignant.";
                    $user_found = true;
                }
            }
        }
    }
    }

    // 3. Si pas trouvé comme enseignant, vérification dans la table utilisateurs
    if (!$user_found) {
        file_put_contents($log_file, "Vérification dans la table utilisateurs...\n", FILE_APPEND);
        $stmt = $conn->prepare("SELECT * FROM utilisateurs WHERE email = ?");
        if ($stmt === false) {
            file_put_contents($log_file, "Erreur de préparation de la requête utilisateurs: " . $conn->error . "\n", FILE_APPEND);
        } else {
            $stmt->bind_param("s", $email);
            $stmt->execute();
            $result = $stmt->get_result();
            file_put_contents($log_file, "Résultat utilisateurs: " . $result->num_rows . " ligne(s) trouvée(s)\n", FILE_APPEND);

        if ($result->num_rows === 1) {
            $user = $result->fetch_assoc();
            // Vérifier si le mot de passe est stocké avec password_hash
            if (password_verify($mot_de_passe, $user['mot_de_passe'])) {
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['email'] = $user['email'];
                $_SESSION['user_type'] = $user['type_utilisateur'];
                $_SESSION['nom'] = $user['nom'] ?? '';
                $_SESSION['prenom'] = $user['prenom'] ?? '';
                $_SESSION['id_departement'] = $user['id_departement'] ?? null;
                $_SESSION['id_specialite'] = $user['id_specialite'] ?? null;


                // Redirection en fonction du type
                if ($user['type_utilisateur'] === 'coordinateur') {
                    // Forcer la redirection vers dashborde_coordinateur.php pour les coordinateurs
                    $_SESSION['user_type'] = 'coordinateur';
                    echo "<script>alert('Redirection vers le tableau de bord coordinateur...');</script>";
                    echo "<script>window.location.href = 'dashborde_coordinateur.php';</script>";
                    header("Location: dashborde_coordinateur.php");
                    die("Redirection vers le tableau de bord coordinateur...");
                } elseif ($user['type_utilisateur'] === 'enseignant') {
                    // Redirection forcée vers dashboard_enseignant.php avec chemin complet
                    $_SESSION['user_type'] = 'enseignant';
                    echo "<script>window.location.href = '/gestion_professeurs/dashboard_enseignant.php';</script>";
                    header("Location: /gestion_professeurs/dashboard_enseignant.php");
                    die("Redirection vers le tableau de bord enseignant...");
                } elseif ($user['type_utilisateur'] === 'admin') {
                    // S'assurer que le type d'utilisateur est correctement défini
                    $_SESSION['user_type'] = 'admin';
                    header("Location: admin_dashboard.php");
                    exit;
                } else {
                    header("Location: chef_dashboard.php");
                    exit;
                }
            } else {
                // Essayer une comparaison directe (au cas où le mot de passe n'est pas hashé)
                if ($mot_de_passe === $user['mot_de_passe']) {
                    $_SESSION['user_id'] = $user['id'];
                    $_SESSION['email'] = $user['email'];
                    $_SESSION['user_type'] = $user['type_utilisateur'];
                    $_SESSION['nom'] = $user['nom'] ?? '';
                    $_SESSION['prenom'] = $user['prenom'] ?? '';
                    $_SESSION['id_departement'] = $user['id_departement'] ?? null;
                    $_SESSION['id_specialite'] = $user['id_specialite'] ?? null;

                    // Redirection en fonction du type
                    if ($user['type_utilisateur'] === 'coordinateur') {
                        // Forcer la redirection vers dashborde_coordinateur.php pour les coordinateurs
                        $_SESSION['user_type'] = 'coordinateur';
                        echo "<script>alert('Redirection vers le tableau de bord coordinateur...');</script>";
                        echo "<script>window.location.href = 'dashborde_coordinateur.php';</script>";
                        header("Location: dashborde_coordinateur.php");
                        die("Redirection vers le tableau de bord coordinateur...");
                    } elseif ($user['type_utilisateur'] === 'enseignant') {
                        // Redirection vers dashboard_enseignant.php
                        $_SESSION['user_type'] = 'enseignant';
                        echo "<script>window.location.href = '/gestion_professeurs/dashboard_enseignant.php';</script>";
                        header("Location: /gestion_professeurs/dashboard_enseignant.php");
                        die("Redirection vers le tableau de bord enseignant...");
                    } elseif ($user['type_utilisateur'] === 'admin') {
                        // S'assurer que le type d'utilisateur est correctement défini
                        $_SESSION['user_type'] = 'admin';
                        header("Location: admin_dashboard.php");
                        exit;
                    } elseif ($user['type_utilisateur'] === 'chef_departement') {
                        // Ajout spécifique pour les chefs de département
                        $_SESSION['user_type'] = 'chef_departement';
                        header("Location: chef_dashboard.php");
                        exit;
                    } else {
                        header("Location: chef_dashboard.php");
                        exit;
                    }
                } else {
                    $erreur = "Mot de passe incorrect pour utilisateur.";
                    $user_found = true;
                }
            }
        }
    }
    }

    // Si aucun utilisateur n'a été trouvé dans aucune table
    if (!$user_found) {
        $erreur = "Aucun utilisateur trouvé avec cet email.";
    }

    // Ajouter des informations de débogage si une erreur s'est produite
    if (isset($erreur)) {
        // Vérifier si l'email existe dans les différentes tables
        $check_coord = $conn->prepare("SELECT COUNT(*) as count FROM coordinateurs WHERE email = ?");
        if ($check_coord === false) {
            file_put_contents($log_file, "Erreur de préparation de la requête de vérification coordinateurs: " . $conn->error . "\n", FILE_APPEND);
            $coord_count = 0;
        } else {
            $check_coord->bind_param("s", $email);
            $check_coord->execute();
            $coord_count = $check_coord->get_result()->fetch_assoc()['count'];
        }

        $check_ens = $conn->prepare("SELECT COUNT(*) as count FROM enseignants WHERE email = ?");
        if ($check_ens === false) {
            file_put_contents($log_file, "Erreur de préparation de la requête de vérification enseignants: " . $conn->error . "\n", FILE_APPEND);
            $ens_count = 0;
        } else {
            $check_ens->bind_param("s", $email);
            $check_ens->execute();
            $ens_count = $check_ens->get_result()->fetch_assoc()['count'];
        }

        $check_user = $conn->prepare("SELECT COUNT(*) as count, type_utilisateur FROM utilisateurs WHERE email = ? GROUP BY type_utilisateur");
        if ($check_user === false) {
            file_put_contents($log_file, "Erreur de préparation de la requête de vérification utilisateurs: " . $conn->error . "\n", FILE_APPEND);
            $user_info = "";
        } else {
            $check_user->bind_param("s", $email);
            $check_user->execute();
            $user_result = $check_user->get_result();
            $user_info = "";
            while ($row = $user_result->fetch_assoc()) {
                $user_info .= "Type: " . $row['type_utilisateur'] . " (Count: " . $row['count'] . ") ";
            }
        }

        $debug_info = "Email trouvé dans: ";
        $debug_info .= $coord_count > 0 ? "coordinateurs (" . $coord_count . ") " : "";
        $debug_info .= $ens_count > 0 ? "enseignants (" . $ens_count . ") " : "";
        $debug_info .= !empty($user_info) ? "utilisateurs (" . $user_info . ")" : "";
        $debug_info = empty(trim($debug_info)) ? "Email non trouvé dans aucune table" : $debug_info;

        $erreur .= "<br><small>" . $debug_info . "</small>";
    }
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connexion</title>
    <style>
        /* Style général avec image de fond */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7ff 0%, #c3c7f7 100%);
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            color: #333333;
            position: relative;
            overflow: hidden;
        }

        /* Formes décoratives */
        body::before {
            content: '';
            position: absolute;
            top: -10%;
            right: -10%;
            width: 40%;
            height: 40%;
            background-color: #d9d2ff;
            border-radius: 50%;
            z-index: -1;
        }

        body::after {
            content: '';
            position: absolute;
            bottom: -5%;
            left: 0;
            width: 100%;
            height: 30%;
            background: linear-gradient(180deg, transparent 0%, #6a11cb 100%);
            border-radius: 50% 50% 0 0 / 100% 100% 0 0;
            z-index: -1;
        }

        /* Navigation (comme dans l'image) */
        .nav-links {
            position: absolute;
            top: 20px;
            left: 0;
            width: 100%;
            display: flex;
            justify-content: center;
            gap: 30px;
            z-index: 10;
        }

        .nav-links a {
            color: #6a11cb;
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .nav-links a:hover {
            color: #5a0cb2;
            text-decoration: underline;
        }

        /* Conteneur du formulaire */
        .login-container {
            background-color: #ffffff;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(106, 17, 203, 0.1);
            width: 90%;
            max-width: 450px;
            text-align: center;
            position: relative;
            z-index: 1;
        }

        /* Titres */
        .login-container h1 {
            font-size: 2.5rem;
            margin-bottom: 25px;
            font-weight: 700;
            color: #6a11cb;
            letter-spacing: 1px;
        }

        /* Champs du formulaire */
        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #e0e0ff;
            border-radius: 30px;
            background-color: #f5f7ff;
            color: #333333;
            font-size: 1rem;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #6a11cb;
            box-shadow: 0 0 0 3px rgba(106, 17, 203, 0.1);
            background-color: #ffffff;
        }

        /* Lien mot de passe oublié */
        .forgot-password {
            display: block;
            text-align: right;
            color: #6a11cb;
            font-size: 0.85rem;
            margin-bottom: 15px;
            text-decoration: none;
        }

        .forgot-password:hover {
            text-decoration: underline;
        }

        /* Bouton de soumission */
        .login-button {
            width: 100%;
            padding: 12px 15px;
            background-color: #6a11cb;
            color: #ffffff;
            border: none;
            border-radius: 30px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            margin-top: 15px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(106, 17, 203, 0.3);
        }

        .login-button:hover {
            background-color: #5a0cb2;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(106, 17, 203, 0.4);
        }

        /* Lien d'inscription */
        .signup-link {
            margin-top: 20px;
            display: block;
            color: #6a11cb;
            opacity: 0.9;
            transition: all 0.3s ease;
            font-weight: 500;
            font-size: 0.9rem;
            text-align: center;
            text-decoration: none;
        }

        .signup-link:hover {
            color: #5a0cb2;
            opacity: 1;
            text-decoration: underline;
        }

        /* Message d'erreur */
        .error-message {
            color: #ff4757;
            margin-top: 15px;
            font-weight: 500;
            font-size: 0.9rem;
            text-align: center;
        }

        /* Placeholder */
        ::placeholder {
            color: #b8b8d9;
            opacity: 1;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .login-container {
                padding: 30px 20px;
            }

            body::before, body::after {
                opacity: 0.5;
            }

            .nav-links {
                flex-wrap: wrap;
                gap: 15px;
            }
        }
    </style>
</head>
<body>


    <div class="login-container">
        <h1>WELCOME!</h1>

        <form method="post">
            <div class="form-group">
                <input type="email" name="email" id="email" placeholder="USERNAME" required>
            </div>
            <div class="form-group">
                <input type="password" name="mot_de_passe" id="mot_de_passe" placeholder="PASSWORD" required>
            </div>
            <a href="reset_password.php" class="forgot-password">Forgot Password?</a>
            <button type="submit" class="login-button">LOGIN</button>
        </form>

        <?php if (isset($erreur)) { ?>
            <div class="error-message"><?php echo $erreur; ?></div>
        <?php } ?>

        <a href="inscription.php" class="signup-link">CREATE ACCOUNT</a>
        <a href="login_enseignant.php" class="signup-link" style="margin-top: 10px;">CONNEXION ENSEIGNANT</a>
    </div>
</body>
</html>