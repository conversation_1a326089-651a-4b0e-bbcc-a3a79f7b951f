/* ------- G<PERSON><PERSON>l ------- */
body {
    font-family: Arial, sans-serif;
    background-color: #f4f4f4;
    margin: 0;
    padding: 0;
}

/* ------- Header ------- */
.container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #fff;
    color: #007bff;
    padding: 10px;
    width: 100%;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

h1 {
    text-align: center;
    font-size: 24px;
    flex-grow: 1;
    margin: 0;
    color: #007bff;
}

h1:hover {
    color: #3898ff;
}

.logo1, .logo2 {
    width: 60px;
    height: auto;
}

.logo1 {
    margin-right: 50px;
}

.logo2 {
    margin-left: 50px;
}

/* ------- Authentification ------- */
.auth-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 700px;
    margin: 50px auto;
    background: #fff;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
}

.auth-container img {
    width: 290px;
    height: 290px;
    margin-right: 30px;
}

.auth-content {
    flex-grow: 1;
}

/* ------- Icônes des Rôles ------- */
.roles-icons {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    justify-content: center;
}

.role {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.role i {
    font-size: 30px;
    color: #007bff;
}

.role p {
    margin-top: 5px;
    font-size: 16px;
    color: #333;
}

/* ------- Effet au survol des rôles ------- */
.role {
    cursor: pointer; /* Change le curseur pour indiquer qu'on peut cliquer */
    transition: transform 0.2s, background 0.3s;
    padding: 10px;
    border-radius: 10px;
}

.role:hover {
    background: #e3f2fd; /* Couleur légèrement bleue au survol */
    transform: scale(1.05);
}

/* ------- Effet au clic ------- */
.role.selected {
    background: #007bff; /* Même couleur que le bouton */
    color: white;
    transform: scale(1.05);
}

.role.selected i {
    color: white; /* Change la couleur de l'icône */
}

/* ------- Formulaire ------- */
form {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 10px;
    width: 100%;
}

.input-group {
    display: flex;
    align-items: center;
    background: white;
    padding: 12px;
    border-radius: 5px;
    border: 1px solid #ccc;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
}

.input-group i {
    margin-right: 10px;
    color: #555;
}

.input-group input {
    flex: 1;
    border: none;
    outline: none;
    padding: 8px;
    font-size: 14px;
}

/* ------- Bouton de Connexion ------- */
button {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 12px 20px;
    cursor: pointer;
    border-radius: 5px;
    font-size: 16px;
    transition: background 0.3s ease, transform 0.2s;
    width: 100%;
    text-align: center;
    display: block;
}

button:hover {
    background-color: #0056b3;
    transform: scale(1.05);
}

/* ------- Mot de passe oublié ------- */
.password-reset {
    margin-top: 10px;
    text-align: center;
}

.password-reset a {
    color: #007bff;
    text-decoration: none;
}

.password-reset a:hover {
    text-decoration: underline;
}

/* ------- Responsiveness ------- */
@media (max-width: 768px) {
    .container {
        flex-direction: column;
        align-items: center;
        padding: 10px;
    }

    h1 {
        font-size: 20px;
    }

    .logo1, .logo2 {
        width: 50px;
    }

    .auth-container {
        flex-direction: column;
        padding: 20px;
        margin: 10px;
    }

    .auth-container img {
        width: 100%;
        max-width: 250px;
        margin: 0 auto;
    }

    .roles-icons {
        flex-direction: column;
        gap: 10px;
    }

    .role {
        font-size: 14px;
        text-align: center;
        margin-bottom: 20px;
    }

    .input-group input {
        font-size: 14px;
    }

    button {
        font-size: 14px;
        padding: 10px;
    }
}
