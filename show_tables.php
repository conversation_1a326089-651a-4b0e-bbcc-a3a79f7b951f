<?php
// Inclure le fichier de configuration
require_once 'config.php';

// Afficher les erreurs pour le débogage
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Tables dans la base de données " . DB_NAME . "</h1>";

try {
    // Connexion à la base de données
    $pdo = new PDO(
        "mysql:host=".DB_HOST.";dbname=".DB_NAME.";charset=utf8",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    // Récupérer la liste des tables
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<h2>Liste des tables</h2>";
    echo "<ul>";
    foreach ($tables as $table) {
        echo "<li>$table</li>";
    }
    echo "</ul>";
    
    // Vérifier si unites_enseignement existe
    if (in_array('unites_enseignement', $tables)) {
        echo "<p style='color:green'>La table 'unites_enseignement' existe dans la base de données.</p>";
    } else {
        echo "<p style='color:red'>La table 'unites_enseignement' n'existe pas dans la base de données.</p>";
    }
    
    // Vérifier si unites_enseignements existe
    if (in_array('unites_enseignements', $tables)) {
        echo "<p style='color:green'>La table 'unites_enseignements' existe dans la base de données.</p>";
        
        // Afficher la structure de la table unites_enseignements
        echo "<h2>Structure de la table unites_enseignements</h2>";
        $stmt = $pdo->query("DESCRIBE unites_enseignements");
        echo "<pre>";
        print_r($stmt->fetchAll());
        echo "</pre>";
        
        // Afficher les données de la table unites_enseignements
        echo "<h2>Données de la table unites_enseignements</h2>";
        $stmt = $pdo->query("SELECT * FROM unites_enseignements LIMIT 10");
        echo "<pre>";
        print_r($stmt->fetchAll());
        echo "</pre>";
    } else {
        echo "<p style='color:red'>La table 'unites_enseignements' n'existe pas dans la base de données.</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color:red'>Erreur de connexion à la base de données: " . $e->getMessage() . "</p>";
}
?>
