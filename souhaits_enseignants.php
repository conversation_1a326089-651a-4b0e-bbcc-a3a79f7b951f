<?php
// Activation du rapport d'erreurs
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Configuration de la session
session_start();

// Connexion à la base de données
$mysqli = new mysqli("localhost", "root", "", "gestion_coordinteur");
if ($mysqli->connect_error) {
    die("Erreur de connexion : " . $mysqli->connect_error);
}

// Vérification d'authentification renforcée
if (!isset($_SESSION['user_id']) || !in_array($_SESSION['user_type'], ['enseignant', 'chef_departement', 'admin'])) {
    header("Location: login_coordinateur.php");
    exit();
}

// Initialisation des variables
$id_utilisateur = $_SESSION['user_id'];
$user_type = $_SESSION['user_type'];
$annee_courante = "2024-2025"; // Année scolaire courante

// Traitement du formulaire
if ($_SERVER["REQUEST_METHOD"] === "POST" && isset($_POST['souhaits']) && $user_type === 'enseignant') {
    $souhaits = $_POST['souhaits'];
    $resultats = ['success' => 0, 'errors' => []];

    foreach ($souhaits as $id_ue) {
        try {
            // Vérification de l'existence de l'UE
            $check_ue = $mysqli->prepare("SELECT id_ue FROM unites_enseignements WHERE id_ue = ?");
            $check_ue->bind_param("i", $id_ue);
            $check_ue->execute();

            if (!$check_ue->get_result()->num_rows) {
                $resultats['errors'][] = "UE $id_ue introuvable";
                continue;
            }

            // Insertion sécurisée
            $stmt = $mysqli->prepare("INSERT INTO souhaits_enseignants
                                    (id_enseignant, id_ue, annee_scolaire, date_souhait)
                                    VALUES (?, ?, ?, NOW())
                                    ON DUPLICATE KEY UPDATE date_souhait = NOW()");
            $stmt->bind_param("iis", $id_utilisateur, $id_ue, $annee_courante);

            if ($stmt->execute()) {
                $resultats['success']++;
            } else {
                $resultats['errors'][] = "Erreur UE $id_ue : " . $stmt->error;
            }
        } catch (Exception $e) {
            $resultats['errors'][] = "Erreur système : " . $e->getMessage();
        }
    }

    // Stockage des résultats dans la session
    $_SESSION['form_result'] = $resultats;
    $_SESSION['souhaits_soumis'] = $souhaits;
    header("Location: resultat_souhaits.php");
    exit();
}

// Récupération de TOUTES les UE (sans filtre d'année)
$sql = $user_type === 'enseignant'
    ? "SELECT ue.*, m.nom AS matiere
       FROM unites_enseignements ue
       LEFT JOIN matieres m ON ue.id_matiere = m.id_matiere
       ORDER BY ue.annee_scolaire DESC, ue.filiere, ue.niveau, m.nom"
    : "SELECT ue.*, m.nom AS matiere, u.nom, u.prenom
       FROM unites_enseignements ue
       LEFT JOIN souhaits_enseignants se ON ue.id_ue = se.id_ue
       LEFT JOIN utilisateurs u ON se.id_enseignant = u.id
       LEFT JOIN matieres m ON ue.id_matiere = m.id_matiere
       ORDER BY ue.annee_scolaire DESC, ue.filiere, ue.niveau, m.nom";

$result = $mysqli->query($sql);
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion des Souhaits</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --background-dark: #1a1a1a;
        }

        body {
            background: var(--background-dark);
            color: #fff;
            min-height: 100vh;
        }

        .sidebar {
            width: 280px;
            background: var(--primary-color);
            position: fixed;
            height: 100vh;
            padding: 20px;
            overflow-y: auto;
        }

        .sidebar img {
            max-width: 150px;
            display: block;
            margin: 0 auto 20px auto;
        }

        .sidebar a {
            display: block;
            color: white;
            text-decoration: none;
            padding: 12px 15px;
            margin: 5px 0;
            border-radius: 5px;
            transition: background-color 0.3s;
        }

        .sidebar a:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .main-content {
            margin-left: 280px;
            padding: 30px;
        }

        .table-custom {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            overflow: hidden;
        }

        .table-custom th {
            background: var(--secondary-color) !important;
        }

        .alert {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            width: 300px;
        }
    </style>
</head>
<body>

<!-- Sidebar -->
<div class="sidebar">
    <img src="image copy 5.png" alt="Logo">
    <a href="Affichage_liste_UE.php">Affichage la liste de UE</a>
    <a href="souhaits_enseignants.php">Souhaits Enseignants</a>
    <a href="Calcul_automatique_charge_horaire.php">Calcul automatique de la charge horaire</a>
    <a href="Notification_non-respect_charge_minimale.php">Notification en cas de non-respect de la charge minimale</a>
    <a href="Consulter_modules_assurés_assure.php">Consulter la liste des modules assurés et qu'il assure.</a>
    <a href="Uploader_notes_session_normale_rattrapage.php">Uploader les notes de la session normale et rattrapage.</a>
    <a href="Consulter_historique_années_passées.">Consulter l'historique des années passées.</a>
    <a href="?logout=true" class="btn btn-danger w-100 mt-3">Déconnexion</a>
</div>

<!-- Contenu principal -->
<div class="main-content">
    <h2 class="mb-4"><?= $user_type === 'enseignant' ? 'Exprimer mes souhaits' : 'Souhaits des enseignants' ?></h2>

    <form method="post">
        <div class="table-responsive">
            <table class="table table-custom table-hover">
                <thead>
                    <tr>
                        <?php if ($user_type === 'enseignant'): ?>
                            <th>Sélection</th>
                        <?php else: ?>
                            <th>Enseignant</th>
                        <?php endif; ?>
                        <th>Matière</th>
                        <th>Filière</th>
                        <th>Niveau</th>
                        <th>Type</th>
                        <th>Année Scolaire</th>
                    </tr>
                </thead>
                <tbody>
                    <?php while ($row = $result->fetch_assoc()): ?>
                        <tr>
                            <?php if ($user_type === 'enseignant'): ?>
                                <td>
                                    <input type="checkbox"
                                           name="souhaits[]"
                                           value="<?= $row['id_ue'] ?>"
                                           <?= isset($row['id_enseignant']) ? 'disabled' : '' ?>>
                                </td>
                            <?php else: ?>
                                <td><?= $row['nom'] ?? 'Non attribué' ?> <?= $row['prenom'] ?? '' ?></td>
                            <?php endif; ?>
                            <td><?= htmlspecialchars($row['matiere']) ?></td>
                            <td><?= htmlspecialchars($row['filiere']) ?></td>
                            <td><?= htmlspecialchars($row['niveau']) ?></td>
                            <td><?= htmlspecialchars($row['type_enseignement']) ?></td>
                            <td><?= $row['annee_scolaire'] ?></td>
                        </tr>
                    <?php endwhile; ?>
                </tbody>
            </table>
        </div>

        <?php if ($user_type === 'enseignant'): ?>
            <button type="submit" class="btn btn-primary btn-lg">
                <i class="fas fa-save me-2"></i>Enregistrer
            </button>
        <?php endif; ?>
    </form>
</div>

<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script>
// Validation client
$(document).ready(function() {
    $('form').submit(function(e) {
        const checkboxes = $('input[name="souhaits[]"]:checked');
        if (checkboxes.length === 0) {
            e.preventDefault();
            alert('Veuillez sélectionner au moins un UE !');
        }
    });
});
</script>

</body>
</html>