<?php
// Démarrer la session et établir la connexion à la base de données
session_start();
$mysqli = new mysqli("localhost", "root", "", "gestion_coordinteur");

// Vérifier si l'enseignant est connecté
if (!isset($_SESSION['id_enseignant'])) {
    header("Location: login_coordinateur.php");
    exit();
}

$id_enseignant = $_SESSION['id_enseignant'];
$annee_suivante = "2025-2026"; // Définir l'année scolaire suivante

// Traitement du formulaire de soumission des souhaits
if ($_SERVER["REQUEST_METHOD"] === "POST" && isset($_POST['souhaits'])) {
    $souhaits = $_POST['souhaits'];

    // Boucle pour chaque UE choisie par l'enseignant
    foreach ($souhaits as $id_ue) {
        // Vérifier si l'enseignant a déjà exprimé ce souhait
        $check = $mysqli->prepare("SELECT * FROM souhaits_enseignants WHERE id_enseignant = ? AND id_ue = ? AND annee_scolaire = ?");
        $check->bind_param("iis", $id_enseignant, $id_ue, $annee_suivante);
        $check->execute();
        $res = $check->get_result();

        // Si le souhait n'existe pas déjà, l'ajouter à la base de données
        if ($res->num_rows === 0) {
            $stmt = $mysqli->prepare("INSERT INTO souhaits_enseignants (id_enseignant, id_ue, annee_scolaire) VALUES (?, ?, ?)");
            $stmt->bind_param("iis", $id_enseignant, $id_ue, $annee_suivante);
            $stmt->execute();
        }
    }

    // Afficher un message de confirmation
    echo "<div style='color: green;'>Souhaits enregistrés avec succès.</div>";
}

// Récupérer les unités d'enseignement disponibles
$sql = "
SELECT ue.id_ue, ue.filiere, ue.niveau, ue.annee_scolaire, ue.type_enseignement, m.nom AS nom_matiere
FROM unites_enseignements ue
JOIN matieres m ON ue.id_matiere = m.id_matiere
ORDER BY ue.filiere, ue.niveau, m.nom
";
$result = $mysqli->query($sql);
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Exprimer les souhaits - Enseignant</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css">
    <style>
        /* Styles personnalisés */
        :root {
            --primary-blue: #1E90FF;
            --primary-magenta: magenta;
            --blue-transparent: rgba(30, 144, 255, 0.3);
        }

        body {
            background: url('image copy 4.png') no-repeat center center fixed;
            background-size: cover;
            color: white;
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            display: flex;
        }

        body::before {
            content: "";
            position: fixed;
            top: 0; left: 0; right: 0; bottom: 0;
            background-color: rgba(10, 25, 47, 0.85);
            z-index: -1;
        }

        .sidebar {
            width: 250px;
            background-color: rgba(10, 25, 47, 0.95);
            padding: 2rem 1rem;
            height: 100vh;
            position: fixed;
            top: 0;
            left: 0;
            border-right: 2px solid var(--primary-magenta);
            box-shadow: 2px 0 10px rgba(0,0,0,0.2);
        }

        .sidebar img {
            max-width: 150px;
            width: 100%;
            height: auto;
            display: block;
            margin: 0 auto 20px;
        }

        .sidebar a {
            display: block;
            padding: 10px 15px;
            color: white;
            text-decoration: none;
            margin-bottom: 10px;
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 6px;
            transition: all 0.3s;
        }

        .sidebar a:hover {
            background-color: var(--primary-blue);
            color: white;
        }

        .container {
            margin-left: 250px;
            padding: 2rem;
            width: calc(100% - 250px);
        }

        h2 {
            text-align: center;
            font-size: 2rem;
            color: var(--primary-blue);
            text-shadow: 0 0 10px var(--blue-transparent);
            border-bottom: 2px solid var(--primary-magenta);
            padding-bottom: 1rem;
            margin-bottom: 2rem;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 0 10px rgba(30, 144, 255, 0.2);
        }

        th, td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid var(--blue-transparent);
            color: white;
        }

        th {
            background-color: var(--primary-blue);
            color: white;
        }

        tr:nth-child(even) {
            background-color: rgba(255, 255, 255, 0.05);
        }

        button {
            background-color: var(--primary-blue);
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        button:hover {
            background-color: var(--primary-magenta);
        }
    </style>
</head>

<body>
    <!-- Sidebar avec navigation -->
    <div class="sidebar">
        <img src="image copy 5.png" alt="Logo">
        <a href="Affichage_liste_UE.php">Affichage la liste de UE</a>
        <a href="souhaits_enseignants.php">Souhaits Enseignants</a>
        <a href="Calcul_automatique_charge_horaire.php">Calcul automatique de la charge horaire</a>
        <a href="Notification_non-respect_charge_minimale.php ">Notification en cas de non-respect de la charge minimale</a>
        <a href=" Consulter_modules_assurés_assure.php "> Consulter la liste des modules assurés et qu'il assure. </a>
        <a href="Uploader_notes_session_normale_rattrapage.php ">Uploader les notes de la session normale et rattrapage. </a>
        <a href="Consulter_historique_années_passées. ">Consulter l’historique des années passées. </a>
        <a href="?logout=true" class="btn btn-danger w-100 mt-3">Déconnexion</a>
    </div>
    <!-- Contenu principal -->
    <div class="container mt-5">
        <h2 class="mb-4">Exprimer mes souhaits d’enseignement pour l’année <?= $annee_suivante ?></h2>
        
        <!-- Formulaire pour soumettre les souhaits -->
        <form method="post">
            <div class="table-responsive">
                <table class="table table-bordered table-striped">
                    <thead class="table-dark">
                        <tr>
                            <th>Choisir</th>
                            <th>Matière</th>
                            <th>Filière</th>
                            <th>Niveau</th>
                            <th>Type</th>
                            <th>Année scolaire</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php while ($row = $result->fetch_assoc()): ?>
                            <tr>
                                <td>
                                    <input type="checkbox" name="souhaits[]" value="<?= $row['id_ue'] ?>">
                                </td>
                                <td><?= htmlspecialchars($row['nom_matiere']) ?></td>
                                <td><?= htmlspecialchars($row['filiere']) ?></td>
                                <td><?= htmlspecialchars($row['niveau']) ?></td>
                                <td><?= htmlspecialchars($row['type_enseignement']) ?></td>
                                <td><?= htmlspecialchars($row['annee_scolaire']) ?></td>
                            </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
            </div>

            <button type="submit" class="btn btn-primary">Enregistrer mes souhaits</button>
        </form>
    </div>
</body>
</html>