<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test AJAX pour les spécialités</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            padding: 20px;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            max-height: 400px;
            overflow: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">Test AJAX pour les spécialités</h1>
        
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">Sélectionner un département</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="departement_id" class="form-label">Département</label>
                            <select class="form-select" id="departement_id">
                                <option value="" selected disabled>Sélectionnez un département</option>
                                <option value="1">Informatique</option>
                                <option value="2">Physique</option>
                                <option value="3">Chimie</option>
                                <option value="4">Biologie</option>
                            </select>
                        </div>
                        <button id="testBtn" class="btn btn-primary">Tester</button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">Spécialités chargées</h5>
                    </div>
                    <div class="card-body">
                        <select class="form-select mb-3" id="specialite_id">
                            <option value="" selected disabled>Sélectionnez d'abord un département</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">Résultat de la requête AJAX</h5>
            </div>
            <div class="card-body">
                <pre id="result">Aucun résultat</pre>
            </div>
        </div>
    </div>
    
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        $(document).ready(function() {
            // Fonction pour charger les spécialités
            function chargerSpecialites(departementId) {
                if (!departementId) {
                    $('#specialite_id').html('<option value="" selected disabled>Sélectionnez d\'abord un département</option>');
                    $('#result').text('Aucun département sélectionné');
                    return;
                }
                
                $('#result').text('Chargement en cours...');
                
                $.ajax({
                    url: 'get_specialites.php',
                    type: 'GET',
                    data: { departement_id: departementId },
                    dataType: 'json',
                    success: function(response) {
                        // Afficher la réponse complète
                        $('#result').text(JSON.stringify(response, null, 2));
                        
                        if (response.success && response.specialites && response.specialites.length > 0) {
                            let options = '<option value="" selected disabled>Sélectionnez une spécialité</option>';
                            response.specialites.forEach(function(specialite) {
                                options += `<option value="${specialite.id}">${specialite.nom}</option>`;
                            });
                            $('#specialite_id').html(options);
                        } else {
                            $('#specialite_id').html('<option value="" selected disabled>Aucune spécialité disponible</option>');
                        }
                    },
                    error: function(xhr, status, error) {
                        $('#result').text('Erreur: ' + status + '\n' + error + '\n\nRéponse: ' + xhr.responseText);
                        $('#specialite_id').html('<option value="" selected disabled>Erreur lors du chargement des spécialités</option>');
                    }
                });
            }
            
            // Événement de changement du département
            $('#departement_id').change(function() {
                chargerSpecialites($(this).val());
            });
            
            // Événement du bouton de test
            $('#testBtn').click(function() {
                chargerSpecialites($('#departement_id').val());
            });
        });
    </script>
</body>
</html>
