<?php
// Inclure le fichier de configuration
require_once 'config.php';

// Démarrer la session
session_start();

// Afficher les erreurs pour le débogage
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Titre et description de la page
$titre = 'Test Chefs de département';
$description = 'Test de gestion des chefs de département par spécialité';

// Connexion à la base de données
try {
    $pdo = new PDO(
        "mysql:host=".DB_HOST.";dbname=".DB_NAME.";charset=utf8",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    // Créer des données de test pour les départements puisque la table n'existe pas
    $departements = [
        ['id' => 1, 'nom' => 'Informatique/Mathématiques'],
        ['id' => 2, 'nom' => 'Physique'],
        ['id' => 3, 'nom' => 'Chimie'],
        ['id' => 4, 'nom' => 'Biologie']
    ];
    
    // Créer des données de test pour les spécialités puisque la table n'existe pas
    $specialites = [
        ['id' => 1, 'nom' => 'Informatique', 'id_departement' => 1],
        ['id' => 2, 'nom' => 'Mathématiques', 'id_departement' => 1],
        ['id' => 3, 'nom' => 'Physique Quantique', 'id_departement' => 2],
        ['id' => 4, 'nom' => 'Physique Nucléaire', 'id_departement' => 2],
        ['id' => 5, 'nom' => 'Chimie Organique', 'id_departement' => 3],
        ['id' => 6, 'nom' => 'Biochimie', 'id_departement' => 4]
    ];
    
    // Récupérer la liste des chefs de département directement depuis la table utilisateurs
    try {
        // Vérifier si la colonne type_utilisateur existe
        $checkColumn = $pdo->query("SHOW COLUMNS FROM utilisateurs LIKE 'type_utilisateur'");
        $columnExists = $checkColumn->rowCount() > 0;
        
        if ($columnExists) {
            // Récupérer uniquement les utilisateurs qui ont type_utilisateur = 'chef_departement'
            $sql = "SELECT id, nom, prenom, email, id_departement, type_utilisateur
                    FROM utilisateurs
                    WHERE type_utilisateur = 'chef_departement'
                    ORDER BY nom, prenom";
            
            // Log pour le débogage
            error_log("Requête SQL pour récupérer les chefs de département: " . $sql);
        } else {
            // Si la colonne n'existe pas, ajouter la colonne type_utilisateur
            try {
                $pdo->exec("ALTER TABLE utilisateurs ADD COLUMN type_utilisateur VARCHAR(50) DEFAULT 'utilisateur' AFTER mot_de_passe");
                error_log("Colonne type_utilisateur ajoutée à la table utilisateurs");
                
                // Récupérer les utilisateurs qui ont type_utilisateur = 'chef_departement'
                // (il n'y en aura probablement aucun pour l'instant)
                $sql = "SELECT id, nom, prenom, email, id_departement, type_utilisateur
                        FROM utilisateurs
                        WHERE type_utilisateur = 'chef_departement'
                        ORDER BY nom, prenom";
                
                // Log pour le débogage
                error_log("Requête SQL après ajout de la colonne: " . $sql);
            } catch (PDOException $alterError) {
                error_log("Erreur lors de l'ajout de la colonne type_utilisateur: " . $alterError->getMessage());
                // Si on ne peut pas ajouter la colonne, récupérer tous les utilisateurs
                // (ce n'est pas idéal, mais c'est mieux que rien)
                $sql = "SELECT id, nom, prenom, email, id_departement
                        FROM utilisateurs
                        ORDER BY nom, prenom";
                
                // Log pour le débogage
                error_log("Requête SQL sans la colonne type_utilisateur: " . $sql);
            }
        }
        
        $chefs = $pdo->query($sql)->fetchAll();
        
        // Log pour le débogage
        error_log("Nombre de chefs de département trouvés: " . count($chefs));
        error_log("Chefs de département trouvés: " . print_r($chefs, true));
        
        // Ajouter les informations du département à partir de notre tableau de données
        foreach ($chefs as &$chef) {
            foreach ($departements as $dept) {
                if ($dept['id'] == $chef['id_departement']) {
                    $chef['nom_departement'] = $dept['nom'];
                    break;
                }
            }
            if (!isset($chef['nom_departement'])) {
                $chef['nom_departement'] = 'Département inconnu';
            }
            
            // Log pour le débogage
            error_log("Chef de département après traitement: " . print_r($chef, true));
        }
        
    } catch (PDOException $e) {
        // Si erreur, créer un tableau vide et logger l'erreur
        error_log("Erreur SQL: " . $e->getMessage());
        $chefs = [];
    }
    
} catch(PDOException $e) {
    die("Erreur de base de données : " . htmlspecialchars($e->getMessage()));
}

// Traitement des actions (ajout, modification, suppression)
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'ajouter':
                // Code pour ajouter un chef de département
                try {
                    $nom = trim($_POST['nom']);
                    $prenom = trim($_POST['prenom']);
                    $email = trim($_POST['email']);
                    $unite_id = $_POST['unite_id'];
                    $specialite_id = $_POST['specialite_id'];
                    $mot_de_passe = $_POST['mot_de_passe'];
                    
                    // Validation
                    if (empty($nom) || empty($prenom) || empty($email) || empty($unite_id) || empty($specialite_id) || empty($mot_de_passe)) {
                        throw new Exception('Tous les champs sont obligatoires');
                    }
                    
                    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                        throw new Exception('Email invalide');
                    }
                    
                    if (strlen($mot_de_passe) < 8) {
                        throw new Exception('Le mot de passe doit contenir au moins 8 caractères');
                    }
                    
                    // Vérifier si l'email existe déjà
                    $stmt = $pdo->prepare("SELECT id FROM utilisateurs WHERE email = ?");
                    $stmt->execute([$email]);
                    
                    if ($stmt->rowCount() > 0) {
                        throw new Exception('Cet email est déjà utilisé');
                    }
                    
                    // Hachage du mot de passe
                    $password_hash = password_hash($mot_de_passe, PASSWORD_DEFAULT);
                    
                    // Vérifier si la colonne type_utilisateur existe dans la table utilisateurs
                    $checkColumn = $pdo->query("SHOW COLUMNS FROM utilisateurs LIKE 'type_utilisateur'");
                    $columnExists = $checkColumn->rowCount() > 0;
                    
                    // Si la colonne n'existe pas, l'ajouter
                    if (!$columnExists) {
                        try {
                            $pdo->exec("ALTER TABLE utilisateurs ADD COLUMN type_utilisateur VARCHAR(50) DEFAULT 'utilisateur' AFTER mot_de_passe");
                            error_log("Colonne type_utilisateur ajoutée à la table utilisateurs");
                            $columnExists = true;
                        } catch (PDOException $alterError) {
                            error_log("Erreur lors de l'ajout de la colonne type_utilisateur: " . $alterError->getMessage());
                        }
                    }
                    
                    // Insertion directe sans transaction
                    if ($columnExists) {
                        // Si la colonne type_utilisateur existe, l'utiliser
                        $query = "INSERT INTO utilisateurs (nom, prenom, email, mot_de_passe, type_utilisateur, id_departement)
                                 VALUES (?, ?, ?, ?, 'chef_departement', ?)";
                        
                        // Log pour le débogage
                        error_log("Requête d'insertion: " . $query);
                        error_log("Paramètres: nom=" . $nom . ", prenom=" . $prenom . ", email=" . $email . ", unite_id=" . $unite_id);
                        
                        $stmt = $pdo->prepare($query);
                        $result = $stmt->execute([$nom, $prenom, $email, $password_hash, $unite_id]);
                        
                        // Log pour le débogage
                        error_log("Résultat de l'exécution: " . ($result ? "succès" : "échec"));
                        
                        if ($result) {
                            $lastId = $pdo->lastInsertId();
                            $message = 'Chef de département ajouté avec succès (ID: ' . $lastId . ')';
                            $messageType = 'success';
                            
                            // Vérifier que l'utilisateur a bien été inséré avec le bon type
                            $verif = $pdo->prepare("SELECT id, type_utilisateur FROM utilisateurs WHERE id = ?");
                            $verif->execute([$lastId]);
                            $result = $verif->fetch();
                            
                            if ($result && $result['type_utilisateur'] === 'chef_departement') {
                                error_log("Chef de département ajouté avec succès - ID: $lastId, Type: chef_departement");
                            } else {
                                error_log("Erreur: Utilisateur créé mais pas comme chef_departement - ID: $lastId, Type: " . ($result ? $result['type_utilisateur'] : 'inconnu'));
                            }
                        } else {
                            $errorInfo = $stmt->errorInfo();
                            error_log("Erreur SQL: " . print_r($errorInfo, true));
                            $message = 'Erreur lors de l\'insertion dans la base de données: ' . $errorInfo[2];
                            $messageType = 'danger';
                        }
                    } else {
                        // Si la colonne n'existe pas, afficher un message d'erreur
                        $message = 'Impossible d\'ajouter un chef de département car la colonne type_utilisateur n\'existe pas dans la table utilisateurs.';
                        $messageType = 'danger';
                    }
                } catch (Exception $e) {
                    $message = 'Erreur lors de l\'ajout : ' . $e->getMessage();
                    $messageType = 'danger';
                }
                break;
        }
    }
}

// Rafraîchir la liste des chefs de département après l'ajout
if ($messageType === 'success') {
    try {
        $sql = "SELECT id, nom, prenom, email, id_departement, type_utilisateur
                FROM utilisateurs
                WHERE type_utilisateur = 'chef_departement'
                ORDER BY nom, prenom";
        $chefs = $pdo->query($sql)->fetchAll();
        
        // Ajouter les informations du département
        foreach ($chefs as &$chef) {
            foreach ($departements as $dept) {
                if ($dept['id'] == $chef['id_departement']) {
                    $chef['nom_departement'] = $dept['nom'];
                    break;
                }
            }
            if (!isset($chef['nom_departement'])) {
                $chef['nom_departement'] = 'Département inconnu';
            }
        }
    } catch (PDOException $e) {
        error_log("Erreur lors du rafraîchissement de la liste: " . $e->getMessage());
    }
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($titre) ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            padding: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1><?= htmlspecialchars($titre) ?></h1>
        <p><?= htmlspecialchars($description) ?></p>
        
        <?php if ($message): ?>
        <div class="alert alert-<?= $messageType ?> alert-dismissible fade show" role="alert">
            <?= htmlspecialchars($message) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>
        
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Ajouter un chef de département</h5>
                    </div>
                    <div class="card-body">
                        <form method="post">
                            <input type="hidden" name="action" value="ajouter">
                            
                            <div class="mb-3">
                                <label for="nom" class="form-label">Nom</label>
                                <input type="text" class="form-control" id="nom" name="nom" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="prenom" class="form-label">Prénom</label>
                                <input type="text" class="form-control" id="prenom" name="prenom" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="unite_id" class="form-label">Département</label>
                                <select class="form-select" id="unite_id" name="unite_id" required>
                                    <option value="" selected disabled>Sélectionnez un département</option>
                                    <?php foreach ($departements as $dept): ?>
                                    <option value="<?= $dept['id'] ?>"><?= htmlspecialchars($dept['nom']) ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="specialite_id" class="form-label">Spécialité</label>
                                <select class="form-select" id="specialite_id" name="specialite_id" required>
                                    <option value="" selected disabled>Sélectionnez d'abord un département</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="mot_de_passe" class="form-label">Mot de passe</label>
                                <input type="password" class="form-control" id="mot_de_passe" name="mot_de_passe" required minlength="8">
                                <div class="form-text">Le mot de passe doit contenir au moins 8 caractères</div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">Ajouter</button>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Chefs de département existants</h5>
                    </div>
                    <div class="card-body">
                        <?php if (count($chefs) > 0): ?>
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Nom</th>
                                    <th>Prénom</th>
                                    <th>Email</th>
                                    <th>Département</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($chefs as $chef): ?>
                                <tr>
                                    <td><?= htmlspecialchars($chef['id']) ?></td>
                                    <td><?= htmlspecialchars($chef['nom']) ?></td>
                                    <td><?= htmlspecialchars($chef['prenom']) ?></td>
                                    <td><?= htmlspecialchars($chef['email']) ?></td>
                                    <td><?= htmlspecialchars($chef['nom_departement']) ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                        <?php else: ?>
                        <div class="alert alert-info">
                            Aucun chef de département trouvé.
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Liens utiles</h5>
                    </div>
                    <div class="card-body">
                        <ul>
                            <li><a href="check_session.php">Vérifier la session</a></li>
                            <li><a href="check_table.php">Vérifier la structure de la table</a></li>
                            <li><a href="gestion_chef_departement.php">Page originale de gestion des chefs de département</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Fonction pour remplir les spécialités en fonction du département sélectionné
        $(document).ready(function() {
            $('#unite_id').change(function() {
                const departementId = $(this).val();
                const specialiteSelect = $('#specialite_id');
                
                // Vider la liste des spécialités
                specialiteSelect.empty();
                specialiteSelect.append('<option value="" selected disabled>Chargement des spécialités...</option>');
                
                // Définir les spécialités par département
                const specialitesParDept = {
                    <?php foreach ($departements as $dept): ?>
                    <?= $dept['id'] ?>: [
                        <?php 
                        $first = true;
                        foreach ($specialites as $specialite) {
                            if ($specialite['id_departement'] == $dept['id']) {
                                if (!$first) echo ",";
                                echo '{id: ' . $specialite['id'] . ', nom: "' . htmlspecialchars($specialite['nom']) . '"}';
                                $first = false;
                            }
                        }
                        ?>
                    ],
                    <?php endforeach; ?>
                };
                
                // Remplir la liste des spécialités
                specialiteSelect.empty();
                specialiteSelect.append('<option value="" selected disabled>Sélectionnez une spécialité</option>');
                
                if (specialitesParDept[departementId] && specialitesParDept[departementId].length > 0) {
                    specialitesParDept[departementId].forEach(specialite => {
                        specialiteSelect.append(`<option value="${specialite.id}">${specialite.nom}</option>`);
                    });
                } else {
                    specialiteSelect.append('<option value="" disabled>Aucune spécialité disponible</option>');
                }
            });
        });
    </script>
</body>
</html>
