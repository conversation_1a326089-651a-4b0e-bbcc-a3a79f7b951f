<?php
// Inclure le fichier de configuration
require_once 'config.php';

// Récupérer les départements
try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    // Définir les départements (données statiques pour le test)
    $departements = [
        ['id' => 1, 'nom' => 'Informatique/Mathématiques'],
        ['id' => 2, 'nom' => 'Physique'],
        ['id' => 3, 'nom' => 'Chimie'],
        ['id' => 4, 'nom' => 'Biologie']
    ];
    
} catch (PDOException $e) {
    die("Erreur de base de données : " . htmlspecialchars($e->getMessage()));
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Département-Spécialité</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 800px;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1 {
            margin-bottom: 20px;
            color: #333;
        }
        .form-group {
            margin-bottom: 20px;
        }
        #debug {
            margin-top: 30px;
            padding: 15px;
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 5px;
            height: 300px;
            overflow-y: auto;
        }
        .debug-entry {
            margin-bottom: 5px;
            padding: 5px;
            border-bottom: 1px solid #eee;
        }
        .debug-entry.error {
            color: red;
        }
        .debug-entry.success {
            color: green;
        }
        .debug-entry.info {
            color: blue;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Département-Spécialité</h1>
        
        <form id="testForm">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="departement">Département</label>
                        <select class="form-control" id="departement" name="departement">
                            <option value="" selected disabled>Sélectionnez un département</option>
                            <?php foreach ($departements as $dept): ?>
                                <option value="<?= $dept['id'] ?>"><?= htmlspecialchars($dept['nom']) ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="specialite">Spécialité</label>
                        <select class="form-control" id="specialite" name="specialite">
                            <option value="" selected disabled>Sélectionnez d'abord un département</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <button type="button" class="btn btn-primary" id="testButton">Tester la connexion AJAX</button>
        </form>
        
        <div id="debug">
            <h3>Console de débogage</h3>
            <div id="debugContent"></div>
        </div>
    </div>
    
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Fonction pour ajouter un message à la console de débogage
        function debugLog(message, type = 'info') {
            const now = new Date();
            const timestamp = now.toLocaleTimeString() + '.' + now.getMilliseconds();
            const entry = $('<div class="debug-entry ' + type + '"></div>').text(`[${timestamp}] ${message}`);
            $('#debugContent').prepend(entry);
        }
        
        // Fonction pour charger les spécialités via AJAX
        function chargerSpecialites(departementId) {
            debugLog(`Chargement des spécialités pour le département ID: ${departementId}`);
            
            // Vider la liste déroulante
            $('#specialite').empty();
            $('#specialite').append('<option value="" selected disabled>Chargement des spécialités...</option>');
            
            // Utiliser AJAX pour charger les spécialités
            $.ajax({
                url: 'get_specialites.php',
                type: 'GET',
                data: { departement_id: departementId },
                dataType: 'json',
                success: function(response) {
                    debugLog(`Réponse AJAX reçue: ${JSON.stringify(response)}`, 'success');
                    
                    // Vider à nouveau la liste déroulante
                    $('#specialite').empty();
                    $('#specialite').append('<option value="" selected disabled>Sélectionnez une spécialité</option>');
                    
                    if (response.success && response.specialites && response.specialites.length > 0) {
                        // Ajouter les spécialités du département
                        response.specialites.forEach(specialite => {
                            $('#specialite').append(`<option value="${specialite.id}">${specialite.nom}</option>`);
                            debugLog(`Ajout de la spécialité: ${specialite.id} - ${specialite.nom}`, 'success');
                        });
                        debugLog(`${response.specialites.length} spécialités ajoutées`, 'success');
                    } else {
                        debugLog("Aucune spécialité trouvée ou erreur dans la réponse", 'error');
                        if (response.error) {
                            debugLog(`Erreur: ${response.error}`, 'error');
                        }
                    }
                },
                error: function(xhr, status, error) {
                    debugLog(`Erreur AJAX: ${error}`, 'error');
                    debugLog(`Statut: ${status}`, 'error');
                    debugLog(`Réponse: ${xhr.responseText}`, 'error');
                    
                    $('#specialite').empty();
                    $('#specialite').append('<option value="" selected disabled>Erreur lors du chargement des spécialités</option>');
                }
            });
        }
        
        $(document).ready(function() {
            debugLog("Page chargée, prête pour les tests");
            
            // Gestionnaire d'événements pour le changement de département
            $('#departement').change(function() {
                const departementId = $(this).val();
                debugLog(`Département sélectionné: ${departementId}`);
                
                if (departementId) {
                    chargerSpecialites(departementId);
                } else {
                    debugLog("Aucun département sélectionné", 'error');
                    $('#specialite').empty();
                    $('#specialite').append('<option value="" selected disabled>Sélectionnez d\'abord un département</option>');
                }
            });
            
            // Gestionnaire d'événements pour le bouton de test
            $('#testButton').click(function() {
                const departementId = $('#departement').val();
                debugLog(`Test manuel pour le département ID: ${departementId || 'non sélectionné'}`);
                
                if (departementId) {
                    chargerSpecialites(departementId);
                } else {
                    debugLog("Veuillez d'abord sélectionner un département", 'error');
                }
            });
        });
    </script>
</body>
</html>
