<?php
session_start();

// Connexion à la base de données
$mysqli = new mysqli("localhost", "root", "", "gestion_coordinteur");
if ($mysqli->connect_error) {
    die("Erreur de connexion : " . $mysqli->connect_error);
}

// Traitement du formulaire de test
if ($_SERVER["REQUEST_METHOD"] === "POST" && isset($_POST['test_souhaits'])) {
    echo "<div style='background: #e7f3ff; padding: 15px; margin: 10px; border: 1px solid #b3d9ff; border-radius: 5px;'>";
    echo "<h4>🧪 Résultats du test d'enregistrement :</h4>";
    
    $souhaits = $_POST['test_souhaits'];
    $id_enseignant = $_SESSION['user_id'] ?? 1; // Utiliser l'ID de session ou 1 par défaut
    $annee_scolaire = "2024-2025";
    
    echo "<strong>Données envoyées :</strong><br>";
    echo "- ID Enseignant: $id_enseignant<br>";
    echo "- Souhaits sélectionnés: " . implode(", ", $souhaits) . "<br>";
    echo "- Année scolaire: $annee_scolaire<br><br>";
    
    foreach ($souhaits as $id_ue) {
        echo "<strong>Test pour UE ID: $id_ue</strong><br>";
        
        // Vérifier si l'UE existe
        $check_ue = $mysqli->prepare("SELECT id_ue FROM unites_enseignements WHERE id_ue = ?");
        $check_ue->bind_param("i", $id_ue);
        $check_ue->execute();
        $result_ue = $check_ue->get_result();
        
        if ($result_ue->num_rows === 0) {
            echo "❌ UE $id_ue n'existe pas<br>";
            continue;
        }
        echo "✅ UE $id_ue existe<br>";
        
        // Vérifier si le souhait existe déjà
        $check_souhait = $mysqli->prepare("SELECT * FROM souhaits_enseignants WHERE id_enseignant = ? AND id_ue = ? AND annee_scolaire = ?");
        $check_souhait->bind_param("iis", $id_enseignant, $id_ue, $annee_scolaire);
        $check_souhait->execute();
        $result_souhait = $check_souhait->get_result();
        
        if ($result_souhait->num_rows > 0) {
            echo "⚠️ Souhait déjà existant pour cette UE<br>";
        } else {
            // Insérer le souhait
            $insert = $mysqli->prepare("INSERT INTO souhaits_enseignants (id_enseignant, id_ue, annee_scolaire, date_souhait) VALUES (?, ?, ?, NOW())");
            $insert->bind_param("iis", $id_enseignant, $id_ue, $annee_scolaire);
            
            if ($insert->execute()) {
                echo "✅ Souhait inséré avec succès (ID: " . $mysqli->insert_id . ")<br>";
            } else {
                echo "❌ Erreur lors de l'insertion: " . $insert->error . "<br>";
            }
        }
        echo "<br>";
    }
    echo "</div>";
}

// Récupérer les UE pour le formulaire
$ues = $mysqli->query("SELECT ue.id_ue, ue.filiere, ue.niveau, m.nom as matiere FROM unites_enseignements ue LEFT JOIN matieres m ON ue.id_matiere = m.id_matiere LIMIT 10");
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Formulaire Souhaits</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-container { background: #f9f9f9; padding: 20px; border-radius: 8px; margin: 20px 0; }
        .checkbox-item { margin: 10px 0; padding: 10px; background: white; border-radius: 4px; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .session-info { background: #fff3cd; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
    </style>
</head>
<body>

<h1>🧪 Test Formulaire Souhaits Enseignants</h1>

<div class="session-info">
    <h3>📋 Informations de session :</h3>
    <p><strong>User ID:</strong> <?= $_SESSION['user_id'] ?? 'Non défini' ?></p>
    <p><strong>User Type:</strong> <?= $_SESSION['user_type'] ?? 'Non défini' ?></p>
    <p><strong>Email:</strong> <?= $_SESSION['email'] ?? 'Non défini' ?></p>
</div>

<div class="form-container">
    <h3>🎯 Formulaire de test :</h3>
    <form method="POST">
        <p>Sélectionnez les UE pour lesquelles vous souhaitez exprimer un souhait :</p>
        
        <?php if ($ues && $ues->num_rows > 0): ?>
            <?php while ($ue = $ues->fetch_assoc()): ?>
                <div class="checkbox-item">
                    <label>
                        <input type="checkbox" name="test_souhaits[]" value="<?= $ue['id_ue'] ?>">
                        <strong>UE <?= $ue['id_ue'] ?></strong> - <?= htmlspecialchars($ue['matiere'] ?? 'Matière inconnue') ?> 
                        (<?= htmlspecialchars($ue['filiere']) ?> - <?= htmlspecialchars($ue['niveau']) ?>)
                    </label>
                </div>
            <?php endwhile; ?>
            
            <button type="submit">🚀 Tester l'enregistrement</button>
        <?php else: ?>
            <p>❌ Aucune UE trouvée dans la base de données</p>
        <?php endif; ?>
    </form>
</div>

<div style="margin-top: 30px;">
    <h3>🔗 Liens utiles :</h3>
    <a href="debug_souhaits.php">🔍 Debug complet</a> | 
    <a href="souhaits_enseignants.php">📝 Page souhaits officielle</a> | 
    <a href="test_session.php">👤 Vérifier session</a>
</div>

<div style="margin-top: 20px; background: #d1ecf1; padding: 15px; border-radius: 5px;">
    <h4>📊 Contenu actuel de la table souhaits_enseignants :</h4>
    <?php
    $souhaits_actuels = $mysqli->query("SELECT * FROM souhaits_enseignants ORDER BY date_souhait DESC");
    if ($souhaits_actuels && $souhaits_actuels->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>ID Enseignant</th><th>ID UE</th><th>Année</th><th>Date</th></tr>";
        while ($souhait = $souhaits_actuels->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $souhait['id_souhait'] . "</td>";
            echo "<td>" . $souhait['id_enseignant'] . "</td>";
            echo "<td>" . $souhait['id_ue'] . "</td>";
            echo "<td>" . $souhait['annee_scolaire'] . "</td>";
            echo "<td>" . $souhait['date_souhait'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>📝 Aucun souhait enregistré pour le moment</p>";
    }
    ?>
</div>

</body>
</html>
