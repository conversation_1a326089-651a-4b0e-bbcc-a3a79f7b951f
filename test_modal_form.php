<?php
// Inclure le fichier de configuration
require_once 'config.php';

// Récupérer les départements
try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    // Définir les départements (données statiques pour le test)
    $departements = [
        ['id' => 1, 'nom' => 'Informatique/Mathématiques'],
        ['id' => 2, 'nom' => 'Physique'],
        ['id' => 3, 'nom' => 'Chimie'],
        ['id' => 4, 'nom' => 'Biologie']
    ];
    
} catch (PDOException $e) {
    die("Erreur de base de données : " . htmlspecialchars($e->getMessage()));
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Modal Form</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 800px;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1 {
            margin-bottom: 20px;
            color: #333;
        }
        .form-group {
            margin-bottom: 20px;
        }
        #debug {
            margin-top: 30px;
            padding: 15px;
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 5px;
            height: 300px;
            overflow-y: auto;
        }
        .debug-entry {
            margin-bottom: 5px;
            padding: 5px;
            border-bottom: 1px solid #eee;
        }
        .debug-entry.error {
            color: red;
        }
        .debug-entry.success {
            color: green;
        }
        .debug-entry.info {
            color: blue;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Modal Form</h1>
        
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#testModal">
            Ouvrir le modal de test
        </button>
        
        <!-- Modal de test -->
        <div class="modal fade" id="testModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Test Département-Spécialité</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form id="testForm">
                            <div class="mb-3">
                                <label for="departement" class="form-label">Département</label>
                                <select class="form-control" id="departement" name="departement">
                                    <option value="" selected disabled>Sélectionnez un département</option>
                                    <?php foreach ($departements as $dept): ?>
                                        <option value="<?= $dept['id'] ?>"><?= htmlspecialchars($dept['nom']) ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="specialite" class="form-label">Spécialité</label>
                                <select class="form-control" id="specialite" name="specialite">
                                    <option value="" selected disabled>Sélectionnez d'abord un département</option>
                                </select>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                        <button type="button" class="btn btn-primary" id="testButton">Tester</button>
                    </div>
                </div>
            </div>
        </div>
        
        <div id="debug">
            <h3>Console de débogage</h3>
            <div id="debugContent"></div>
        </div>
    </div>
    
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Fonction pour ajouter un message à la console de débogage
        function debugLog(message, type = 'info') {
            const now = new Date();
            const timestamp = now.toLocaleTimeString() + '.' + now.getMilliseconds();
            const entry = $('<div class="debug-entry ' + type + '"></div>').text(`[${timestamp}] ${message}`);
            $('#debugContent').prepend(entry);
        }
        
        // Fonction pour charger les spécialités via AJAX
        function chargerSpecialites(departementId) {
            debugLog(`Chargement des spécialités pour le département ID: ${departementId}`);
            
            // Vider la liste déroulante
            $('#specialite').empty();
            $('#specialite').append('<option value="" selected disabled>Chargement des spécialités...</option>');
            
            // Définir les spécialités par département (solution de secours)
            const specialitesParDept = {
                1: [ // Informatique/Mathématiques
                    { id: 1, nom: "Développement logiciel" },
                    { id: 2, nom: "Intelligence Artificielle" },
                    { id: 3, nom: "Mathématiques Appliquées" },
                    { id: 4, nom: "Développement Web" },
                    { id: 5, nom: "Base de Données" },
                    { id: 6, nom: "Réseaux" }
                ],
                2: [ // Physique
                    { id: 7, nom: "Physique Fondamentale" },
                    { id: 8, nom: "Physique Appliquée" },
                    { id: 9, nom: "Électronique" },
                    { id: 10, nom: "Physique Nucléaire" }
                ],
                3: [ // Chimie
                    { id: 11, nom: "Chimie Organique" },
                    { id: 12, nom: "Chimie Inorganique" },
                    { id: 13, nom: "Biochimie" }
                ],
                4: [ // Biologie
                    { id: 14, nom: "Microbiologie" },
                    { id: 15, nom: "Génétique" },
                    { id: 16, nom: "Écologie" }
                ]
            };
            
            // Fonction pour ajouter les spécialités à partir des données statiques
            function ajouterSpecialitesStatiques() {
                debugLog(`Ajout des spécialités à partir des données statiques pour le département ID: ${departementId}`, 'success');
                
                if (specialitesParDept[departementId] && specialitesParDept[departementId].length > 0) {
                    // Vider la liste déroulante
                    $('#specialite').empty();
                    $('#specialite').append('<option value="" selected disabled>Sélectionnez une spécialité</option>');
                    
                    // Ajouter les spécialités
                    specialitesParDept[departementId].forEach(specialite => {
                        $('#specialite').append(`<option value="${specialite.id}">${specialite.nom}</option>`);
                        debugLog(`Ajout manuel de la spécialité: ${specialite.id} - ${specialite.nom}`, 'success');
                    });
                    debugLog(`${specialitesParDept[departementId].length} spécialités ajoutées manuellement`, 'success');
                    
                    return true;
                } else {
                    debugLog("Aucune spécialité statique disponible pour ce département", 'error');
                    return false;
                }
            }
            
            // Utiliser AJAX pour charger les spécialités
            $.ajax({
                url: 'get_specialites.php',
                type: 'GET',
                data: { departement_id: departementId },
                dataType: 'json',
                timeout: 5000, // Timeout de 5 secondes
                success: function(response) {
                    debugLog(`Réponse AJAX reçue: ${JSON.stringify(response)}`, 'success');
                    
                    // Vider à nouveau la liste déroulante
                    $('#specialite').empty();
                    $('#specialite').append('<option value="" selected disabled>Sélectionnez une spécialité</option>');
                    
                    if (response.success && response.specialites && response.specialites.length > 0) {
                        // Ajouter les spécialités du département
                        response.specialites.forEach(specialite => {
                            $('#specialite').append(`<option value="${specialite.id}">${specialite.nom}</option>`);
                            debugLog(`Ajout de la spécialité via AJAX: ${specialite.id} - ${specialite.nom}`, 'success');
                        });
                        debugLog(`${response.specialites.length} spécialités ajoutées via AJAX`, 'success');
                    } else {
                        debugLog("Aucune spécialité trouvée via AJAX, utilisation de la solution de secours", 'error');
                        
                        // Si aucune spécialité n'a été trouvée, utiliser les données statiques
                        if (!ajouterSpecialitesStatiques()) {
                            // Si aucune spécialité statique n'est disponible, afficher un message
                            $('#specialite').empty();
                            $('#specialite').append('<option value="" selected disabled>Aucune spécialité disponible</option>');
                        }
                    }
                },
                error: function(xhr, status, error) {
                    debugLog(`Erreur AJAX: ${error}`, 'error');
                    debugLog(`Statut: ${status}`, 'error');
                    debugLog(`Réponse: ${xhr.responseText}`, 'error');
                    
                    // En cas d'erreur, utiliser les données statiques
                    if (!ajouterSpecialitesStatiques()) {
                        // Si aucune spécialité statique n'est disponible, afficher un message d'erreur
                        $('#specialite').empty();
                        $('#specialite').append('<option value="" selected disabled>Erreur lors du chargement des spécialités</option>');
                    }
                }
            });
        }
        
        $(document).ready(function() {
            debugLog("Page chargée, prête pour les tests");
            
            // Gestionnaire d'événements pour l'ouverture du modal
            $('#testModal').on('shown.bs.modal', function() {
                debugLog("Modal ouvert");
            });
            
            // Gestionnaire d'événements pour le changement de département
            $('#departement').on('change', function() {
                const departementId = $(this).val();
                debugLog(`Département sélectionné: ${departementId}`);
                
                if (departementId) {
                    // Utiliser un délai court pour s'assurer que l'événement est bien traité
                    setTimeout(function() {
                        chargerSpecialites(departementId);
                    }, 100);
                } else {
                    debugLog("Aucun département sélectionné", 'error');
                    $('#specialite').empty();
                    $('#specialite').append('<option value="" selected disabled>Sélectionnez d\'abord un département</option>');
                }
            });
            
            // Ajouter un gestionnaire d'événements pour le clic sur le select (pour les appareils mobiles)
            $('#departement').on('click', function() {
                debugLog("Clic sur le select de département");
            });
            
            // Ajouter un gestionnaire d'événements pour le focus sur le select
            $('#departement').on('focus', function() {
                debugLog("Focus sur le select de département");
            });
            
            // Gestionnaire d'événements pour le bouton de test
            $('#testButton').click(function() {
                const departementId = $('#departement').val();
                const specialiteId = $('#specialite').val();
                
                debugLog(`Test avec département ID: ${departementId || 'non sélectionné'} et spécialité ID: ${specialiteId || 'non sélectionnée'}`);
                
                if (!departementId) {
                    debugLog("Veuillez sélectionner un département", 'error');
                }
                
                if (!specialiteId) {
                    debugLog("Veuillez sélectionner une spécialité", 'error');
                }
                
                if (departementId && specialiteId) {
                    debugLog("Formulaire valide !", 'success');
                }
            });
        });
    </script>
</body>
</html>
