<?php
session_start();

echo "<h2>Variables de session actives :</h2>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

echo "<h2>Informations de débogage :</h2>";
echo "<p><strong>user_id:</strong> " . ($_SESSION['user_id'] ?? 'Non défini') . "</p>";
echo "<p><strong>user_type:</strong> " . ($_SESSION['user_type'] ?? 'Non défini') . "</p>";
echo "<p><strong>id_enseignant:</strong> " . ($_SESSION['id_enseignant'] ?? 'Non défini') . "</p>";
echo "<p><strong>id_coordinateur:</strong> " . ($_SESSION['id_coordinateur'] ?? 'Non défini') . "</p>";
echo "<p><strong>email:</strong> " . ($_SESSION['email'] ?? 'Non défini') . "</p>";

echo "<h2>Test de redirection :</h2>";
if (!isset($_SESSION['user_id']) && !isset($_SESSION['id_enseignant']) && !isset($_SESSION['id_coordinateur'])) {
    echo "<p style='color: red;'>❌ Aucune session valide trouvée - redirection vers login</p>";
} else {
    echo "<p style='color: green;'>✅ Session valide trouvée</p>";
}

echo "<h2>Liens de test :</h2>";
echo "<a href='souhaits_enseignants.php'>Tester souhaits_enseignants.php</a><br>";
echo "<a href='Affichage_liste_UE.php'>Retour à la liste des UE</a>";
?>
