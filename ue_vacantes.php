<?php
require_once 'config.php';
session_start();

// Afficher les erreurs pour le débogage
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Vérification des droits avec débogage
$debug_info = [];
$debug_info['session_id'] = session_id();
$debug_info['session_status'] = session_status();
$debug_info['user_id_set'] = isset($_SESSION['user_id']);
$debug_info['user_id'] = $_SESSION['user_id'] ?? 'non défini';
$debug_info['type_utilisateur'] = $_SESSION['type_utilisateur'] ?? 'non défini';
$debug_info['role'] = $_SESSION['role'] ?? 'non défini';
$debug_info['user_type'] = $_SESSION['user_type'] ?? 'non défini';

// Accepter tous les utilisateurs connectés pour le moment
if (!isset($_SESSION['user_id'])) {
    // Si l'utilisateur n'est pas connecté du tout
    $debug_info['redirect_reason'] = 'Utilisateur non connecté';

    // Commenter temporairement la redirection pour le débogage
    // header("Location: login.php");
    // exit;
}

// Définir un rôle par défaut pour le débogage si aucun n'est défini
$user_role = $_SESSION['type_utilisateur'] ?? $_SESSION['role'] ?? $_SESSION['user_type'] ?? 'visiteur';
$debug_info['user_role_final'] = $user_role;

// Récupérer l'ID du département (s'il existe)
$departement_id = $_SESSION['id_departement'] ?? null;
$ues_vacantes = [];

// Si l'utilisateur n'a pas de département assigné et n'est pas admin
if (!$departement_id && $user_role !== 'admin') {
    // Afficher un message d'erreur au lieu de rediriger
    $error = "Vous n'avez pas de département assigné. Veuillez contacter l'administrateur.";
}

try {
    $pdo = new PDO("mysql:host=".DB_HOST.";dbname=".DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Nous savons maintenant que seule la table unites_enseignements existe
    $ueTableName = 'unites_enseignements';

    // Vérifier si la table existe
    $stmt = $pdo->query("SHOW TABLES LIKE '$ueTableName'");
    if ($stmt->rowCount() == 0) {
        throw new PDOException("La table $ueTableName n'existe pas");
    }

    // Vérifier si les tables référencées existent
    $stmt = $pdo->query("SHOW TABLES LIKE 'choix_professeurs'");
    $choixProfsExists = $stmt->rowCount() > 0;

    $stmt = $pdo->query("SHOW TABLES LIKE 'affectations_vacataires'");
    $affectationsVacatairesExists = $stmt->rowCount() > 0;

    $stmt = $pdo->query("SHOW TABLES LIKE 'filiere'");
    $filiereExists = $stmt->rowCount() > 0;

    // Construire une requête simplifiée en fonction des tables existantes
    if ($departement_id) {
        // Si l'utilisateur a un département assigné
        if ($filiereExists) {
            // Si la table filiere existe, utiliser la relation département -> filière -> UE
            $query = "
                SELECT ue.*
                FROM $ueTableName ue
                WHERE ue.filiere IN (
                    SELECT nom_filiere
                    FROM filiere
                    WHERE id_departement = ?
                )
            ";

            if ($choixProfsExists && $affectationsVacatairesExists) {
                // Ajouter les conditions pour les UE vacantes si les tables existent
                $query = "
                    SELECT ue.*
                    FROM $ueTableName ue
                    LEFT JOIN choix_professeurs cp ON ue.id_ue = cp.ue_id AND cp.statut = 'valide'
                    WHERE ue.filiere IN (
                        SELECT nom_filiere
                        FROM filiere
                        WHERE id_departement = ?
                    )
                    AND cp.ue_id IS NULL
                    AND ue.id_ue NOT IN (
                        SELECT ue_id
                        FROM affectations_vacataires
                        WHERE type_enseignement IS NOT NULL
                    )
                ";
            }
        } else {
            // Si la table filiere n'existe pas, utiliser directement le département_id dans la table UE
            $query = "SELECT * FROM $ueTableName WHERE id_departement = ?";
        }
    } else {
        // Si l'utilisateur n'a pas de département assigné, afficher toutes les UE
        $query = "SELECT * FROM $ueTableName";
    }

    // Ajouter l'ordre
    $query .= " ORDER BY filiere, id_ue";

    $stmt = $pdo->prepare($query);
    $stmt->execute([$departement_id]);
    $ues_vacantes = $stmt->fetchAll();

    // Validation des UE vacantes
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['valider_vacantes'])) {
        // Vérifier que la liste n'a pas changé
        $current_ids = array_column($ues_vacantes, 'id_ue');
        $posted_ids = json_decode($_POST['ues_ids'], true);

        if (array_diff($current_ids, $posted_ids)) {
            $error = "La liste a été modifiée depuis l'affichage. Veuillez actualiser.";
        } else {
            // Historique des validations
            $stmt = $pdo->prepare("
                INSERT INTO validation_vacantes
                (departement_id, ues_ids, date_validation)
                VALUES (?, ?, NOW())
            ");
            $stmt->execute([$departement_id, implode(',', $current_ids)]);

            // Vérifier si la colonne statut existe dans la table
            $stmt = $pdo->query("SHOW COLUMNS FROM $ueTableName LIKE 'statut'");
            $statutExists = $stmt->rowCount() > 0;

            if (!$statutExists) {
                // Ajouter la colonne statut si elle n'existe pas
                $pdo->exec("ALTER TABLE $ueTableName ADD COLUMN statut VARCHAR(50) DEFAULT NULL");
                echo "<!-- Colonne statut ajoutée à la table $ueTableName -->";
            }

            // Marquer les UE comme officiellement vacantes
            $stmt = $pdo->prepare("
                UPDATE $ueTableName
                SET statut = 'vacant'
                WHERE id_ue = ?
            ");

            foreach ($current_ids as $id_ue) {
                $stmt->execute([$id_ue]);
            }

            $success = "Liste validée avec succès (" . count($current_ids) . " UE)";
        }
    }

} catch (PDOException $e) {
    $error = "Erreur base de données : " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>UE Vacantes</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .ue-vacante { background-color: #fff3cd; border-left: 4px solid #ffc107; }
        .badge-vacant { background-color: #ffc107; color: black; }
    </style>
</head>
<body>
    <?php include 'navbar_chef.php'; ?>

    <div class="container mt-4">
        <h2 class="mb-4">
            <i class="fas fa-box-open me-2"></i>Unités d'Enseignement Vacantes
        </h2>

        <?php if (isset($success)): ?>
            <div class="alert alert-success"><?= $success ?></div>
        <?php endif; ?>

        <?php if (isset($error)): ?>
            <div class="alert alert-danger"><?= $error ?></div>
        <?php endif; ?>

        <!-- Informations de débogage -->
        <div class="card mb-4 bg-dark text-white">
            <div class="card-header bg-info text-white">
                <h4 class="m-0">Informations de débogage</h4>
            </div>
            <div class="card-body">
                <h5>Variables de session</h5>
                <pre><?php print_r($debug_info); ?></pre>

                <h5>Tables de la base de données</h5>
                <div class="row">
                    <?php
                    try {
                        // Vérifier les tables importantes
                        $tables = [
                            'utilisateurs',
                            'unites_enseignement',
                            'unites_enseignements',
                            'departements',
                            'filiere',
                            'matieres',
                            'affectations'
                        ];

                        foreach ($tables as $table) {
                            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
                            $exists = $stmt->rowCount() > 0;

                            echo '<div class="col-md-6 mb-2">';
                            if ($exists) {
                                echo '<div class="alert alert-success">Table <strong>' . htmlspecialchars($table) . '</strong> existe</div>';
                            } else {
                                echo '<div class="alert alert-danger">Table <strong>' . htmlspecialchars($table) . '</strong> n\'existe pas</div>';
                            }
                            echo '</div>';
                        }
                    } catch (PDOException $e) {
                        echo '<div class="alert alert-danger">Erreur: ' . htmlspecialchars($e->getMessage()) . '</div>';
                    }
                    ?>
                </div>

                <div class="mt-3">
                    <a href="debug_session.php" class="btn btn-info">Voir toutes les informations de session</a>
                </div>
            </div>
        </div>

        <div class="card shadow">
            <div class="card-header bg-warning">
                <h4 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    UE Non Attribuées - Département <?= htmlspecialchars($_SESSION['nom_departement']) ?>
                </h4>
            </div>

            <div class="card-body">
                <?php if (empty($ues_vacantes)): ?>
                    <div class="alert alert-success mb-0">
                        <i class="fas fa-check-circle me-2"></i>
                        Toutes les unités sont correctement attribuées !
                    </div>
                <?php else: ?>
                    <form method="post">
                        <input type="hidden" name="ues_ids" value="<?= htmlspecialchars(json_encode(array_column($ues_vacantes, 'id_ue'))) ?>">

                        <div class="table-responsive">
                            <table class="table table-hover align-middle">
                                <thead class="table-warning">
                                    <tr>
                                        <th>Code UE</th>
                                        <th>Matière</th>
                                        <th>Filière</th>
                                        <th>Niveau</th>
                                        <th>Crédits</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($ues_vacantes as $ue):
                                        $matiere = get_matiere($pdo, $ue['id_matiere']);
                                    ?>
                                    <tr class="ue-vacante">
                                        <td><?= htmlspecialchars($ue['id_ue']) ?></td>
                                        <td>
                                            <div class="fw-bold"><?= htmlspecialchars($matiere['nom']) ?></div>
                                            <small class="text-muted"><?= htmlspecialchars($ue['type_enseignement']) ?></small>
                                        </td>
                                        <td><?= htmlspecialchars($ue['filiere']) ?></td>
                                        <td><?= htmlspecialchars($ue['niveau']) ?></td>
                                        <td><?= htmlspecialchars($matiere['credit']) ?></td>
                                        <td>
                                            <div class="d-flex gap-2">
                                                <a href="affecter_ue.php?id_ue=<?= $ue['id_ue'] ?>"
                                                   class="btn btn-sm btn-primary"
                                                   title="Affecter manuellement">
                                                   <i class="fas fa-user-plus"></i>
                                                </a>
                                                <a href="details_ue.php?id_ue=<?= $ue['id_ue'] ?>"
                                                   class="btn btn-sm btn-info"
                                                   title="Voir détails">
                                                   <i class="fas fa-eye"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <div class="mt-4 text-end">
                            <button type="submit" name="valider_vacantes"
                                    class="btn btn-lg btn-warning fw-bold"
                                    onclick="return confirm('Confirmer la validation de ces UE comme vacantes ?')">
                                <i class="fas fa-check-circle me-2"></i>
                                Valider la liste des UE vacantes
                            </button>
                        </div>
                    </form>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <?php
    // Fonction helper pour récupérer les infos matière
    function get_matiere($pdo, $id_matiere) {
        try {
            // Vérifier si la table matieres existe
            $stmt = $pdo->query("SHOW TABLES LIKE 'matieres'");
            $matieresExists = $stmt->rowCount() > 0;

            if ($matieresExists && $id_matiere) {
                $stmt = $pdo->prepare("SELECT * FROM matieres WHERE id_matiere = ?");
                $stmt->execute([$id_matiere]);
                $result = $stmt->fetch();

                if ($result) {
                    return $result;
                }
            }

            // Si la table n'existe pas ou si la matière n'est pas trouvée, essayer de récupérer les infos depuis unites_enseignements
            if ($id_matiere) {
                $stmt = $pdo->prepare("
                    SELECT filiere as nom, 3 as credit, id_ue, niveau
                    FROM unites_enseignements
                    WHERE id_matiere = ?
                ");
                $stmt->execute([$id_matiere]);
                $result = $stmt->fetch();

                if ($result) {
                    return $result;
                }
            }

            // Si aucune information n'est trouvée
            return ['nom' => 'Matière ' . ($id_matiere ?? 'Inconnue'), 'credit' => 3];
        } catch (PDOException $e) {
            // En cas d'erreur, retourner des valeurs par défaut
            return ['nom' => 'Erreur: ' . $e->getMessage(), 'credit' => 0];
        }
    }
    ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>