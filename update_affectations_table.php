<?php
// Script pour mettre à jour la structure de la table affectations
require_once 'config.php';

// Afficher les erreurs pour le débogage
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Mise à jour de la table affectations</h1>";

try {
    // Connexion à la base de données
    $pdo = new PDO(
        "mysql:host=".DB_HOST.";dbname=".DB_NAME.";charset=utf8",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );
    
    // Vérifier si la colonne ue_id existe dans la table affectations
    $stmt = $pdo->query("SHOW COLUMNS FROM affectations LIKE 'ue_id'");
    $ueIdExists = $stmt->rowCount() > 0;
    
    if (!$ueIdExists) {
        // Ajouter la colonne ue_id
        $pdo->exec("ALTER TABLE affectations ADD COLUMN ue_id INT NOT NULL AFTER professeur_id");
        echo "<p style='color:green'>La colonne 'ue_id' a été ajoutée à la table 'affectations' avec succès.</p>";
        
        // Récupérer les données des unités d'enseignement
        $stmt = $pdo->query("SELECT * FROM unites_enseignements");
        $ues = $stmt->fetchAll();
        
        echo "<p>Nombre d'unités d'enseignement trouvées : " . count($ues) . "</p>";
        
        // Mettre à jour les affectations existantes avec des valeurs par défaut pour ue_id
        if (!empty($ues)) {
            // Utiliser la première UE comme valeur par défaut
            $defaultUeId = $ues[0]['id_ue'];
            
            $stmt = $pdo->prepare("UPDATE affectations SET ue_id = ? WHERE ue_id = 0 OR ue_id IS NULL");
            $stmt->execute([$defaultUeId]);
            
            $rowCount = $stmt->rowCount();
            echo "<p style='color:green'>$rowCount affectations ont été mises à jour avec l'UE par défaut (ID: $defaultUeId).</p>";
        } else {
            echo "<p style='color:orange'>Aucune unité d'enseignement trouvée. Veuillez en créer avant d'affecter des professeurs.</p>";
        }
    } else {
        echo "<p style='color:blue'>La colonne 'ue_id' existe déjà dans la table 'affectations'.</p>";
    }
    
    // Afficher la structure actuelle de la table
    $stmt = $pdo->query("DESCRIBE affectations");
    $columns = $stmt->fetchAll();
    
    echo "<h2>Structure actuelle de la table affectations</h2>";
    echo "<table border='1' cellpadding='5' cellspacing='0'>";
    echo "<tr><th>Nom</th><th>Type</th><th>Null</th><th>Clé</th><th>Défaut</th><th>Extra</th></tr>";
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default'] ?? 'NULL') . "</td>";
        echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    // Ajouter un lien vers charge_horaire.php
    echo "<p><a href='charge_horaire.php' style='padding: 10px 20px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px;'>Retour à la page des charges horaires</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color:red'>Erreur de base de données: " . $e->getMessage() . "</p>";
}
?>
