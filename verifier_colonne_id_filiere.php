<?php
// Script pour vérifier et ajouter la colonne id_filiere à la table utilisateurs

// Connexion à la base de données
require_once 'config.php';

try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8",
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]
    );

    echo "<h1>Vérification de la structure de la table utilisateurs</h1>";

    // Vérifier si la colonne id_filiere existe déjà dans la table utilisateurs
    $stmt = $pdo->prepare("
        SELECT COLUMN_NAME
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'utilisateurs' AND COLUMN_NAME = 'id_filiere'
    ");
    $stmt->execute([DB_NAME]);
    
    if ($stmt->rowCount() === 0) {
        // La colonne n'existe pas, on l'ajoute
        echo "<p>La colonne id_filiere n'existe pas dans la table utilisateurs. Ajout en cours...</p>";
        
        $pdo->exec("ALTER TABLE utilisateurs ADD COLUMN id_filiere INT");
        $pdo->exec("ALTER TABLE utilisateurs ADD CONSTRAINT fk_utilisateur_filiere FOREIGN KEY (id_filiere) REFERENCES filiere(id_filiere) ON DELETE SET NULL");
        $pdo->exec("CREATE INDEX idx_utilisateurs_id_filiere ON utilisateurs(id_filiere)");
        
        echo "<p style='color:green'>La colonne id_filiere a été ajoutée à la table utilisateurs avec succès.</p>";
        
        // Mettre à jour les coordinateurs existants pour leur attribuer une filière par défaut
        // basée sur leur département
        $pdo->exec("
            UPDATE utilisateurs u
            JOIN departement d ON u.id_departement = d.id_departement
            LEFT JOIN filiere f ON d.id_departement = f.id_departement
            SET u.id_filiere = f.id_filiere
            WHERE u.type_utilisateur = 'coordinateur' 
            AND u.id_filiere IS NULL 
            AND f.id_filiere IS NOT NULL
        ");
        
        echo "<p style='color:green'>Les coordinateurs existants ont été mis à jour avec leur filière correspondante.</p>";
    } else {
        echo "<p style='color:green'>La colonne id_filiere existe déjà dans la table utilisateurs.</p>";
    }
    
    // Afficher la structure actuelle de la table utilisateurs
    $result = $pdo->query("DESCRIBE utilisateurs");
    $columns = $result->fetchAll();
    
    echo "<h2>Structure actuelle de la table utilisateurs</h2>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Champ</th><th>Type</th><th>Null</th><th>Clé</th><th>Défaut</th><th>Extra</th></tr>";
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    // Vérifier les contraintes de clé étrangère
    $result = $pdo->query("
        SELECT CONSTRAINT_NAME, COLUMN_NAME, REFERENCED_TABLE_NAME, REFERENCED_COLUMN_NAME
        FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
        WHERE TABLE_SCHEMA = '" . DB_NAME . "' 
        AND TABLE_NAME = 'utilisateurs'
        AND REFERENCED_TABLE_NAME IS NOT NULL
    ");
    $constraints = $result->fetchAll();
    
    echo "<h2>Contraintes de clé étrangère sur la table utilisateurs</h2>";
    
    if (count($constraints) > 0) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Nom de la contrainte</th><th>Colonne</th><th>Table référencée</th><th>Colonne référencée</th></tr>";
        
        foreach ($constraints as $constraint) {
            echo "<tr>";
            echo "<td>" . $constraint['CONSTRAINT_NAME'] . "</td>";
            echo "<td>" . $constraint['COLUMN_NAME'] . "</td>";
            echo "<td>" . $constraint['REFERENCED_TABLE_NAME'] . "</td>";
            echo "<td>" . $constraint['REFERENCED_COLUMN_NAME'] . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    } else {
        echo "<p>Aucune contrainte de clé étrangère trouvée.</p>";
    }
    
    echo "<p>Vérification terminée.</p>";
    
} catch (PDOException $e) {
    die("<p style='color:red'>Erreur de base de données : " . htmlspecialchars($e->getMessage()) . "</p>");
}
?>
